// Direct test of Mailinator email sending
const https = require('https');

async function sendMailinatorEmail() {
  const emailData = {
    to: '<EMAIL>',
    from: '<EMAIL>',
    subject: 'Video Interview Invitation - Technical Position',
    text: 'You have been invited to a video interview. Please click the link to join.',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0;">🎯 Video Interview Invitation</h1>
        </div>
        
        <div style="padding: 30px; background: #f9f9f9;">
          <h2 style="color: #333;">Hello Ram Prasad,</h2>
          
          <p style="font-size: 16px; line-height: 1.6; color: #555;">
            You have been invited to participate in a video interview for the <strong>Technical Position</strong>.
          </p>
          
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #667eea; margin-top: 0;">Interview Details:</h3>
            <p><strong>Position:</strong> Technical Position</p>
            <p><strong>Scheduled:</strong> ${new Date(Date.now() + 2 * 60 * 60 * 1000).toLocaleString()}</p>
            <p><strong>Duration:</strong> 45 minutes</p>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="http://localhost:3000/interview/invite/test-token-123" 
               style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
              🚀 Join Interview
            </a>
          </div>
          
          <p style="color: #666; font-size: 14px;">
            This invitation will expire in 7 days. If you have any questions, please contact our support team.
          </p>
        </div>
        
        <div style="background: #333; color: white; padding: 20px; text-align: center;">
          <p style="margin: 0; font-size: 14px;">
            Powered by AI Interview System
          </p>
        </div>
      </div>
    `
  };

  const postData = JSON.stringify(emailData);

  const options = {
    hostname: 'api.mailinator.com',
    port: 443,
    path: '/api/v2/domains/mailinator.com/inboxes/ramprasad/messages',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': postData.length
    }
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      console.log(`Status: ${res.statusCode}`);
      console.log(`Headers:`, res.headers);
      
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        console.log('Response:', responseData);
        resolve(responseData);
      });
    });

    req.on('error', (e) => {
      console.error(`Problem with request: ${e.message}`);
      reject(e);
    });

    req.write(postData);
    req.end();
  });
}

// Run the test
sendMailinatorEmail()
  .then(() => {
    console.log('✅ Email sent successfully!');
    console.log('🔗 Check inbox at: https://www.mailinator.com/v4/public/inboxes.jsp?to=ramprasad');
  })
  .catch((error) => {
    console.error('❌ Failed to send email:', error);
  });
