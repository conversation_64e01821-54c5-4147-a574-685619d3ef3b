// Video Upload Service for GitHub and Google Drive integration

export interface UploadResult {
  success: boolean
  url?: string
  error?: string
  platform: 'github' | 'google-drive' | 'local'
}

export class VideoUploadService {
  // GitHub upload using GitHub API (requires personal access token)
  static async uploadToGitHub(
    videoBlob: Blob, 
    fileName: string,
    repoOwner: string = 'your-username',
    repoName: string = 'interview-recordings',
    branch: string = 'main'
  ): Promise<UploadResult> {
    try {
      // Convert blob to base64
      const base64Data = await this.blobToBase64(videoBlob)
      const content = base64Data.split(',')[1] // Remove data:video/webm;base64, prefix

      const githubToken = process.env.NEXT_PUBLIC_GITHUB_TOKEN
      if (!githubToken) {
        console.log('GitHub token not configured, skipping GitHub upload')
        return { success: false, error: 'GitHub token not configured', platform: 'github' }
      }

      // Create file in GitHub repository
      const response = await fetch(`https://api.github.com/repos/${repoOwner}/${repoName}/contents/recordings/${fileName}`, {
        method: 'PUT',
        headers: {
          'Authorization': `token ${githubToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: `Add interview recording: ${fileName}`,
          content: content,
          branch: branch
        })
      })

      if (response.ok) {
        const result = await response.json()
        return {
          success: true,
          url: result.content.download_url,
          platform: 'github'
        }
      } else {
        const error = await response.text()
        return {
          success: false,
          error: `GitHub upload failed: ${error}`,
          platform: 'github'
        }
      }
    } catch (error) {
      return {
        success: false,
        error: `GitHub upload error: ${error}`,
        platform: 'github'
      }
    }
  }

  // Google Drive upload using Google Drive API
  static async uploadToGoogleDrive(
    videoBlob: Blob,
    fileName: string,
    folderId?: string
  ): Promise<UploadResult> {
    try {
      const accessToken = await this.getGoogleAccessToken()
      if (!accessToken) {
        return {
          success: false,
          error: 'Google Drive authentication failed',
          platform: 'google-drive'
        }
      }

      // Create file metadata
      const metadata = {
        name: fileName,
        parents: folderId ? [folderId] : undefined
      }

      // Create form data for multipart upload
      const formData = new FormData()
      formData.append('metadata', new Blob([JSON.stringify(metadata)], { type: 'application/json' }))
      formData.append('file', videoBlob)

      const response = await fetch('https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`
        },
        body: formData
      })

      if (response.ok) {
        const result = await response.json()
        
        // Make file publicly viewable (optional)
        await this.makeGoogleDriveFilePublic(result.id, accessToken)
        
        return {
          success: true,
          url: `https://drive.google.com/file/d/${result.id}/view`,
          platform: 'google-drive'
        }
      } else {
        const error = await response.text()
        return {
          success: false,
          error: `Google Drive upload failed: ${error}`,
          platform: 'google-drive'
        }
      }
    } catch (error) {
      return {
        success: false,
        error: `Google Drive upload error: ${error}`,
        platform: 'google-drive'
      }
    }
  }

  // Local storage fallback (for demo purposes)
  static async saveToLocalStorage(videoBlob: Blob, fileName: string): Promise<UploadResult> {
    try {
      // Convert blob to data URL for local storage
      const dataUrl = await this.blobToBase64(videoBlob)
      
      // Store in localStorage (note: this has size limitations)
      const recordings = JSON.parse(localStorage.getItem('interviewRecordings') || '[]')
      recordings.push({
        fileName,
        dataUrl,
        timestamp: new Date().toISOString(),
        size: videoBlob.size
      })
      
      localStorage.setItem('interviewRecordings', JSON.stringify(recordings))
      
      return {
        success: true,
        url: `local://${fileName}`,
        platform: 'local'
      }
    } catch (error) {
      return {
        success: false,
        error: `Local storage error: ${error}`,
        platform: 'local'
      }
    }
  }

  // Main upload function that tries multiple platforms
  static async uploadVideo(
    videoBlob: Blob,
    studentName: string,
    interviewTitle: string,
    timestamp: Date = new Date()
  ): Promise<UploadResult[]> {
    const fileName = `interview_${studentName.replace(/\s+/g, '_')}_${timestamp.getTime()}.webm`
    const results: UploadResult[] = []

    console.log(`📹 Starting video upload for ${fileName}`)
    console.log(`📊 Video size: ${(videoBlob.size / 1024 / 1024).toFixed(2)} MB`)

    // Try GitHub upload first
    console.log('🔄 Attempting GitHub upload...')
    const githubResult = await this.uploadToGitHub(videoBlob, fileName)
    results.push(githubResult)
    
    if (githubResult.success) {
      console.log('✅ GitHub upload successful:', githubResult.url)
    } else {
      console.log('❌ GitHub upload failed:', githubResult.error)
    }

    // Try Google Drive upload
    console.log('🔄 Attempting Google Drive upload...')
    const driveResult = await this.uploadToGoogleDrive(videoBlob, fileName)
    results.push(driveResult)
    
    if (driveResult.success) {
      console.log('✅ Google Drive upload successful:', driveResult.url)
    } else {
      console.log('❌ Google Drive upload failed:', driveResult.error)
    }

    // Fallback to local storage
    console.log('🔄 Saving to local storage as fallback...')
    const localResult = await this.saveToLocalStorage(videoBlob, fileName)
    results.push(localResult)
    
    if (localResult.success) {
      console.log('✅ Local storage successful')
    } else {
      console.log('❌ Local storage failed:', localResult.error)
    }

    return results
  }

  // Helper function to convert blob to base64
  private static blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = reject
      reader.readAsDataURL(blob)
    })
  }

  // Get Google access token (simplified - in production, use proper OAuth flow)
  private static async getGoogleAccessToken(): Promise<string | null> {
    // This is a simplified version. In production, you would:
    // 1. Implement proper OAuth 2.0 flow
    // 2. Store refresh tokens securely
    // 3. Handle token refresh
    
    const clientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID
    const clientSecret = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_SECRET
    
    if (!clientId || !clientSecret) {
      console.log('Google Drive credentials not configured')
      return null
    }

    // For demo purposes, return null to skip Google Drive upload
    // In production, implement proper OAuth flow here
    return null
  }

  // Make Google Drive file publicly viewable
  private static async makeGoogleDriveFilePublic(fileId: string, accessToken: string): Promise<void> {
    try {
      await fetch(`https://www.googleapis.com/drive/v3/files/${fileId}/permissions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          role: 'reader',
          type: 'anyone'
        })
      })
    } catch (error) {
      console.log('Failed to make file public:', error)
    }
  }

  // Get all locally stored recordings
  static getLocalRecordings(): Array<{fileName: string, dataUrl: string, timestamp: string, size: number}> {
    try {
      return JSON.parse(localStorage.getItem('interviewRecordings') || '[]')
    } catch {
      return []
    }
  }

  // Clear local recordings
  static clearLocalRecordings(): void {
    localStorage.removeItem('interviewRecordings')
  }

  // Download video blob as file
  static downloadVideo(videoBlob: Blob, fileName: string): void {
    const url = URL.createObjectURL(videoBlob)
    const a = document.createElement('a')
    a.href = url
    a.download = fileName
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }
}
