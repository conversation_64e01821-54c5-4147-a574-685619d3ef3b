{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/app/interview/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useSession } from 'next-auth/react'\nimport { useRouter, useParams } from 'next/navigation'\nimport { useEffect, useState, useRef } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport {\n  Video,\n  VideoOff,\n  Mic,\n  MicOff,\n  Monitor,\n  MonitorOff,\n  Phone,\n  Clock,\n  Send,\n  Code,\n  MessageSquare,\n  Square,\n  RotateCcw\n} from 'lucide-react'\n\ninterface InterviewData {\n  id: string\n  title: string\n  duration: number\n  questions: any[]\n  startTime: Date\n}\n\nexport default function InterviewRoom() {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n  const params = useParams()\n  const [interview, setInterview] = useState<InterviewData | null>(null)\n  const [isVideoOn, setIsVideoOn] = useState(true)\n  const [isAudioOn, setIsAudioOn] = useState(true)\n  const [isScreenSharing, setIsScreenSharing] = useState(false)\n  const [timeElapsed, setTimeElapsed] = useState(0)\n  const [currentQuestion, setCurrentQuestion] = useState(0)\n  const [code, setCode] = useState('')\n  const [notes, setNotes] = useState('')\n  const [isInterviewerSpeaking, setIsInterviewerSpeaking] = useState(false)\n  const [isListening, setIsListening] = useState(false)\n  const [studentResponse, setStudentResponse] = useState('')\n  const [interviewerMessage, setInterviewerMessage] = useState('')\n  \n  const localVideoRef = useRef<HTMLVideoElement>(null)\n  const remoteVideoRef = useRef<HTMLVideoElement>(null)\n\n  useEffect(() => {\n    if (status === 'loading') return\n\n    if (!session) {\n      router.push('/auth/signin')\n      return\n    }\n\n    // Initialize interview data\n    fetchInterviewData()\n    initializeMedia()\n    startTimer()\n  }, [session, status, router, params.id])\n\n  // Start interviewer introduction when interview loads\n  useEffect(() => {\n    if (interview) {\n      setTimeout(() => {\n        startInterviewerIntroduction()\n      }, 3000) // Wait 3 seconds after interview loads\n    }\n  }, [interview])\n\n  const startFirstQuestion = () => {\n    askCurrentQuestion()\n  }\n\n  const fetchInterviewData = async () => {\n    try {\n      const interviewId = params.id as string\n\n      // Demo interview data based on ID\n      const demoInterviews = {\n        'demo-practice': {\n          id: 'demo-practice',\n          title: 'Practice Interview - Frontend Developer',\n          duration: 30,\n          questions: [\n            {\n              id: 1,\n              type: 'CODING',\n              title: 'Two Sum Problem',\n              description: 'Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target.\\n\\nExample:\\nInput: nums = [2,7,11,15], target = 9\\nOutput: [0,1]\\nExplanation: Because nums[0] + nums[1] == 9, we return [0, 1].\\n\\nConstraints:\\n- 2 <= nums.length <= 10^4\\n- -10^9 <= nums[i] <= 10^9\\n- -10^9 <= target <= 10^9\\n- Only one valid answer exists.',\n              timeLimit: 15\n            },\n            {\n              id: 2,\n              type: 'THEORY',\n              title: 'React Hooks Explanation',\n              description: 'Explain the difference between useState and useEffect hooks in React. Provide examples of when you would use each one and discuss their lifecycle.',\n              timeLimit: 10\n            },\n            {\n              id: 3,\n              type: 'BEHAVIORAL',\n              title: 'Problem Solving Experience',\n              description: 'Tell me about a challenging technical problem you solved recently. What was your approach and what did you learn?',\n              timeLimit: 5\n            }\n          ],\n          startTime: new Date()\n        },\n        'demo-1': {\n          id: 'demo-1',\n          title: 'Frontend Developer Interview',\n          duration: 60,\n          questions: [\n            {\n              id: 1,\n              type: 'CODING',\n              title: 'Array Manipulation',\n              description: 'Write a function that removes duplicates from an array while maintaining the original order.',\n              timeLimit: 20\n            },\n            {\n              id: 2,\n              type: 'THEORY',\n              title: 'CSS Flexbox',\n              description: 'Explain how CSS Flexbox works and provide examples of common use cases.',\n              timeLimit: 15\n            }\n          ],\n          startTime: new Date()\n        },\n        'demo-2': {\n          id: 'demo-2',\n          title: 'React Developer Assessment',\n          duration: 90,\n          questions: [\n            {\n              id: 1,\n              type: 'CODING',\n              title: 'React Component Optimization',\n              description: 'Create a React component that efficiently renders a large list of items with search functionality.',\n              timeLimit: 45\n            },\n            {\n              id: 2,\n              type: 'THEORY',\n              title: 'State Management',\n              description: 'Compare different state management solutions in React (Context, Redux, Zustand). When would you use each?',\n              timeLimit: 20\n            }\n          ],\n          startTime: new Date()\n        }\n      }\n\n      const interviewData = demoInterviews[interviewId as keyof typeof demoInterviews] || demoInterviews['demo-practice']\n      setInterview(interviewData)\n    } catch (error) {\n      console.error('Error fetching interview data:', error)\n    }\n  }\n\n  const initializeMedia = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({ \n        video: true, \n        audio: true \n      })\n      \n      if (localVideoRef.current) {\n        localVideoRef.current.srcObject = stream\n      }\n    } catch (error) {\n      console.error('Error accessing media devices:', error)\n    }\n  }\n\n  const startTimer = () => {\n    const interval = setInterval(() => {\n      setTimeElapsed(prev => prev + 1)\n    }, 1000)\n\n    return () => clearInterval(interval)\n  }\n\n  const toggleVideo = () => {\n    setIsVideoOn(!isVideoOn)\n    // Implement actual video toggle logic\n  }\n\n  const toggleAudio = () => {\n    setIsAudioOn(!isAudioOn)\n    // Implement actual audio toggle logic\n  }\n\n  const toggleScreenShare = () => {\n    setIsScreenSharing(!isScreenSharing)\n    // Implement screen sharing logic\n  }\n\n  const endInterview = () => {\n    // Show completion message and redirect\n    const confirmed = window.confirm('Are you sure you want to end the interview? Your progress will be saved.')\n    if (confirmed) {\n      // In a real app, this would save the interview data\n      alert('Interview completed! Check your results in the Results section.')\n      router.push('/student/results')\n    }\n  }\n\n  const nextQuestion = () => {\n    if (interview && currentQuestion < interview.questions.length - 1) {\n      setCurrentQuestion(currentQuestion + 1)\n      setCode('')\n      // Interviewer asks the next question\n      setTimeout(() => askCurrentQuestion(), 1000)\n    }\n  }\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60)\n    const secs = seconds % 60\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`\n  }\n\n  // Interviewer Speech Functions\n  const startInterviewerIntroduction = () => {\n    const introMessage = `Hello! Welcome to your interview session. I'm your AI interviewer today. We'll be going through ${interview?.questions.length} questions covering different areas. Click \"Start First Question\" when you're ready to begin!`\n    speakInterviewerMessage(introMessage)\n    setInterviewerMessage(introMessage)\n  }\n\n  const askCurrentQuestion = () => {\n    if (interview && interview.questions[currentQuestion]) {\n      const question = interview.questions[currentQuestion]\n      const questionMessage = `Question ${currentQuestion + 1}: ${question.title}. ${question.description}. You have ${question.timeLimit} minutes for this question. Please take your time to think and provide your answer.`\n      speakInterviewerMessage(questionMessage)\n      setInterviewerMessage(questionMessage)\n    }\n  }\n\n  const speakInterviewerMessage = (message: string) => {\n    setIsInterviewerSpeaking(true)\n\n    // Use Web Speech API for text-to-speech\n    if ('speechSynthesis' in window) {\n      const utterance = new SpeechSynthesisUtterance(message)\n      utterance.rate = 0.9\n      utterance.pitch = 1\n      utterance.volume = 0.8\n\n      // Try to use a professional voice\n      const voices = speechSynthesis.getVoices()\n      const preferredVoice = voices.find(voice =>\n        voice.name.includes('Google') ||\n        voice.name.includes('Microsoft') ||\n        voice.lang.includes('en-US')\n      )\n      if (preferredVoice) {\n        utterance.voice = preferredVoice\n      }\n\n      utterance.onend = () => {\n        setIsInterviewerSpeaking(false)\n        setIsListening(true)\n      }\n\n      speechSynthesis.speak(utterance)\n    } else {\n      // Fallback for browsers without speech synthesis\n      setTimeout(() => {\n        setIsInterviewerSpeaking(false)\n        setIsListening(true)\n      }, 3000)\n    }\n  }\n\n  const startListening = () => {\n    setIsListening(true)\n    setTimeout(() => {\n      setIsListening(false)\n    }, 30000) // Stop listening after 30 seconds\n  }\n\n  const stopListening = () => {\n    setIsListening(false)\n  }\n\n  if (status === 'loading' || !interview) {\n    return <div>Loading interview...</div>\n  }\n\n  const question = interview.questions[currentQuestion]\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 text-white\">\n      {/* Header */}\n      <div className=\"bg-gray-800 p-4 flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-xl font-bold\">{interview.title}</h1>\n          <p className=\"text-gray-400\">Question {currentQuestion + 1} of {interview.questions.length}</p>\n        </div>\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"flex items-center space-x-2\">\n            <Clock className=\"h-4 w-4\" />\n            <span>{formatTime(timeElapsed)}</span>\n          </div>\n          <Button variant=\"destructive\" onClick={endInterview}>\n            <Phone className=\"h-4 w-4 mr-2\" />\n            End Interview\n          </Button>\n        </div>\n      </div>\n\n      <div className=\"flex h-[calc(100vh-80px)]\">\n        {/* Video Section */}\n        <div className=\"w-1/3 p-4 space-y-4\">\n          {/* AI Interviewer Avatar */}\n          <Card className=\"bg-gray-800 border-gray-700\">\n            <CardContent className=\"p-4\">\n              <div className=\"aspect-video bg-gradient-to-br from-blue-900 to-purple-900 rounded-lg flex items-center justify-center mb-2 relative overflow-hidden\">\n                {/* Animated Avatar */}\n                <div className={`relative transition-all duration-500 ${isInterviewerSpeaking ? 'scale-110' : 'scale-100'}`}>\n                  {/* Avatar Face */}\n                  <div className=\"w-32 h-32 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center relative\">\n                    {/* Eyes */}\n                    <div className=\"absolute top-8 left-8 w-3 h-3 bg-white rounded-full\"></div>\n                    <div className=\"absolute top-8 right-8 w-3 h-3 bg-white rounded-full\"></div>\n\n                    {/* Mouth - animated when speaking */}\n                    <div className={`absolute bottom-8 left-1/2 transform -translate-x-1/2 transition-all duration-300 ${\n                      isInterviewerSpeaking\n                        ? 'w-8 h-4 bg-white rounded-full animate-pulse'\n                        : 'w-6 h-2 bg-white rounded-full'\n                    }`}></div>\n\n                    {/* Speaking indicator */}\n                    {isInterviewerSpeaking && (\n                      <div className=\"absolute -bottom-4 left-1/2 transform -translate-x-1/2\">\n                        <div className=\"flex space-x-1\">\n                          <div className=\"w-2 h-2 bg-green-400 rounded-full animate-bounce\"></div>\n                          <div className=\"w-2 h-2 bg-green-400 rounded-full animate-bounce\" style={{animationDelay: '0.1s'}}></div>\n                          <div className=\"w-2 h-2 bg-green-400 rounded-full animate-bounce\" style={{animationDelay: '0.2s'}}></div>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Listening indicator */}\n                  {isListening && (\n                    <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2\">\n                      <div className=\"flex items-center space-x-2 bg-red-500 px-3 py-1 rounded-full text-xs\">\n                        <div className=\"w-2 h-2 bg-white rounded-full animate-pulse\"></div>\n                        <span>Listening...</span>\n                      </div>\n                    </div>\n                  )}\n                </div>\n\n                {/* Background animation */}\n                <div className=\"absolute inset-0 opacity-20\">\n                  <div className={`w-full h-full rounded-lg transition-all duration-1000 ${\n                    isInterviewerSpeaking\n                      ? 'bg-gradient-to-r from-green-400 to-blue-500'\n                      : 'bg-gradient-to-r from-blue-600 to-purple-600'\n                  }`}></div>\n                </div>\n              </div>\n              <p className=\"text-sm text-gray-400 text-center font-medium\">\n                AI Interviewer {isInterviewerSpeaking ? '🗣️' : isListening ? '👂' : '💭'}\n              </p>\n            </CardContent>\n          </Card>\n\n          {/* Interviewer Speech Bubble */}\n          {interviewerMessage && (\n            <Card className=\"bg-blue-900 border-blue-700\">\n              <CardContent className=\"p-4\">\n                <div className=\"relative\">\n                  <div className=\"bg-blue-800 p-3 rounded-lg text-sm\">\n                    <p className=\"text-blue-100\">{interviewerMessage}</p>\n                  </div>\n                  <div className=\"absolute -top-2 left-4 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-blue-800\"></div>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {/* Student Response Controls */}\n          <Card className=\"bg-gray-800 border-gray-700\">\n            <CardContent className=\"p-4\">\n              <h3 className=\"text-sm font-medium mb-3\">Your Response</h3>\n\n              {/* Voice Response */}\n              <div className=\"space-y-3\">\n                <div className=\"flex space-x-2\">\n                  <Button\n                    size=\"sm\"\n                    onClick={startListening}\n                    disabled={isListening || isInterviewerSpeaking}\n                    className={isListening ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'}\n                  >\n                    {isListening ? (\n                      <>\n                        <Square className=\"h-4 w-4 mr-2\" />\n                        Stop Recording\n                      </>\n                    ) : (\n                      <>\n                        <Mic className=\"h-4 w-4 mr-2\" />\n                        Start Speaking\n                      </>\n                    )}\n                  </Button>\n\n                  <Button\n                    size=\"sm\"\n                    variant=\"outline\"\n                    onClick={askCurrentQuestion}\n                    disabled={isInterviewerSpeaking}\n                  >\n                    <RotateCcw className=\"h-4 w-4 mr-2\" />\n                    Repeat Question\n                  </Button>\n                </div>\n\n                {/* Text Response */}\n                <div>\n                  <textarea\n                    className=\"w-full h-20 bg-gray-700 text-white p-3 rounded-lg text-sm resize-none\"\n                    placeholder=\"Or type your response here...\"\n                    value={studentResponse}\n                    onChange={(e) => setStudentResponse(e.target.value)}\n                  />\n                </div>\n\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-xs text-gray-400\">\n                    {isListening ? 'Recording your response...' : 'Click \"Start Speaking\" to give verbal answer'}\n                  </span>\n                  <div className=\"flex space-x-2\">\n                    {currentQuestion === 0 && !interviewerMessage.includes('Question 1') && (\n                      <Button size=\"sm\" onClick={startFirstQuestion} className=\"bg-blue-600 hover:bg-blue-700\">\n                        Start First Question\n                      </Button>\n                    )}\n                    {currentQuestion < interview.questions.length - 1 && (\n                      <Button size=\"sm\" variant=\"outline\" onClick={nextQuestion}>\n                        Next Question →\n                      </Button>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Local Video */}\n          <Card className=\"bg-gray-800 border-gray-700\">\n            <CardContent className=\"p-4\">\n              <div className=\"aspect-video bg-gray-700 rounded-lg flex items-center justify-center mb-2\">\n                <video\n                  ref={localVideoRef}\n                  className=\"w-full h-full object-cover rounded-lg\"\n                  autoPlay\n                  playsInline\n                  muted\n                />\n              </div>\n              \n              {/* Controls */}\n              <div className=\"flex justify-center space-x-2\">\n                <Button\n                  size=\"sm\"\n                  variant={isVideoOn ? \"default\" : \"destructive\"}\n                  onClick={toggleVideo}\n                >\n                  {isVideoOn ? <Video className=\"h-4 w-4\" /> : <VideoOff className=\"h-4 w-4\" />}\n                </Button>\n                <Button\n                  size=\"sm\"\n                  variant={isAudioOn ? \"default\" : \"destructive\"}\n                  onClick={toggleAudio}\n                >\n                  {isAudioOn ? <Mic className=\"h-4 w-4\" /> : <MicOff className=\"h-4 w-4\" />}\n                </Button>\n                <Button\n                  size=\"sm\"\n                  variant={isScreenSharing ? \"default\" : \"outline\"}\n                  onClick={toggleScreenShare}\n                >\n                  {isScreenSharing ? <MonitorOff className=\"h-4 w-4\" /> : <Monitor className=\"h-4 w-4\" />}\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Main Content */}\n        <div className=\"flex-1 p-4\">\n          <Card className=\"h-full bg-gray-800 border-gray-700\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center justify-between\">\n                <span>{question?.title}</span>\n                <div className=\"flex space-x-2\">\n                  {currentQuestion > 0 && (\n                    <Button variant=\"outline\" size=\"sm\">\n                      Previous\n                    </Button>\n                  )}\n                  {currentQuestion < interview.questions.length - 1 && (\n                    <Button size=\"sm\" onClick={nextQuestion}>\n                      Next Question\n                    </Button>\n                  )}\n                </div>\n              </CardTitle>\n              <CardDescription className=\"text-gray-300\">\n                {question?.description}\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"flex-1\">\n              <Tabs defaultValue=\"question\" className=\"h-full\">\n                <TabsList className=\"grid w-full grid-cols-3\">\n                  <TabsTrigger value=\"question\">Question</TabsTrigger>\n                  <TabsTrigger value=\"code\">Code Editor</TabsTrigger>\n                  <TabsTrigger value=\"notes\">Notes</TabsTrigger>\n                </TabsList>\n                \n                <TabsContent value=\"question\" className=\"mt-4\">\n                  <div className=\"bg-gray-700 p-4 rounded-lg\">\n                    <h3 className=\"font-semibold mb-2\">Question Details</h3>\n                    <p className=\"text-gray-300 mb-4\">{question?.description}</p>\n                    <div className=\"flex items-center space-x-4 text-sm text-gray-400\">\n                      <span>Type: {question?.type}</span>\n                      <span>Time Limit: {question?.timeLimit} minutes</span>\n                    </div>\n                  </div>\n                </TabsContent>\n                \n                <TabsContent value=\"code\" className=\"mt-4\">\n                  <div className=\"h-96\">\n                    <textarea\n                      className=\"w-full h-full bg-gray-700 text-white p-4 rounded-lg font-mono text-sm resize-none\"\n                      placeholder=\"Write your code here...\"\n                      value={code}\n                      onChange={(e) => setCode(e.target.value)}\n                    />\n                  </div>\n                </TabsContent>\n                \n                <TabsContent value=\"notes\" className=\"mt-4\">\n                  <div className=\"h-96\">\n                    <textarea\n                      className=\"w-full h-full bg-gray-700 text-white p-4 rounded-lg resize-none\"\n                      placeholder=\"Take notes here...\"\n                      value={notes}\n                      onChange={(e) => setNotes(e.target.value)}\n                    />\n                  </div>\n                </TabsContent>\n              </Tabs>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Chat Section */}\n        <div className=\"w-80 p-4\">\n          <Card className=\"h-full bg-gray-800 border-gray-700\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <MessageSquare className=\"h-5 w-5 mr-2\" />\n                Chat\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"flex flex-col h-full\">\n              <div className=\"flex-1 bg-gray-700 rounded-lg p-4 mb-4 overflow-y-auto\">\n                {/* Chat messages would go here */}\n                <div className=\"text-gray-400 text-center\">\n                  Chat with your interviewer\n                </div>\n              </div>\n              <div className=\"flex space-x-2\">\n                <input\n                  type=\"text\"\n                  placeholder=\"Type a message...\"\n                  className=\"flex-1 bg-gray-700 text-white px-3 py-2 rounded-lg text-sm\"\n                />\n                <Button size=\"sm\">\n                  <Send className=\"h-4 w-4\" />\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AAgCe,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACjE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC/C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAEhD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,WAAW,WAAW;YAE1B,IAAI,CAAC,SAAS;gBACZ,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,4BAA4B;YAC5B;YACA;YACA;QACF;kCAAG;QAAC;QAAS;QAAQ;QAAQ,OAAO,EAAE;KAAC;IAEvC,sDAAsD;IACtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,WAAW;gBACb;+CAAW;wBACT;oBACF;8CAAG,MAAM,uCAAuC;;YAClD;QACF;kCAAG;QAAC;KAAU;IAEd,MAAM,qBAAqB;QACzB;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,cAAc,OAAO,EAAE;YAE7B,kCAAkC;YAClC,MAAM,iBAAiB;gBACrB,iBAAiB;oBACf,IAAI;oBACJ,OAAO;oBACP,UAAU;oBACV,WAAW;wBACT;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,aAAa;4BACb,WAAW;wBACb;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,aAAa;4BACb,WAAW;wBACb;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,aAAa;4BACb,WAAW;wBACb;qBACD;oBACD,WAAW,IAAI;gBACjB;gBACA,UAAU;oBACR,IAAI;oBACJ,OAAO;oBACP,UAAU;oBACV,WAAW;wBACT;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,aAAa;4BACb,WAAW;wBACb;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,aAAa;4BACb,WAAW;wBACb;qBACD;oBACD,WAAW,IAAI;gBACjB;gBACA,UAAU;oBACR,IAAI;oBACJ,OAAO;oBACP,UAAU;oBACV,WAAW;wBACT;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,aAAa;4BACb,WAAW;wBACb;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,aAAa;4BACb,WAAW;wBACb;qBACD;oBACD,WAAW,IAAI;gBACjB;YACF;YAEA,MAAM,gBAAgB,cAAc,CAAC,YAA2C,IAAI,cAAc,CAAC,gBAAgB;YACnH,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBACvD,OAAO;gBACP,OAAO;YACT;YAEA,IAAI,cAAc,OAAO,EAAE;gBACzB,cAAc,OAAO,CAAC,SAAS,GAAG;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,MAAM,aAAa;QACjB,MAAM,WAAW,YAAY;YAC3B,eAAe,CAAA,OAAQ,OAAO;QAChC,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B;IAEA,MAAM,cAAc;QAClB,aAAa,CAAC;IACd,sCAAsC;IACxC;IAEA,MAAM,cAAc;QAClB,aAAa,CAAC;IACd,sCAAsC;IACxC;IAEA,MAAM,oBAAoB;QACxB,mBAAmB,CAAC;IACpB,iCAAiC;IACnC;IAEA,MAAM,eAAe;QACnB,uCAAuC;QACvC,MAAM,YAAY,OAAO,OAAO,CAAC;QACjC,IAAI,WAAW;YACb,oDAAoD;YACpD,MAAM;YACN,OAAO,IAAI,CAAC;QACd;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,aAAa,kBAAkB,UAAU,SAAS,CAAC,MAAM,GAAG,GAAG;YACjE,mBAAmB,kBAAkB;YACrC,QAAQ;YACR,qCAAqC;YACrC,WAAW,IAAM,sBAAsB;QACzC;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAClF;IAEA,+BAA+B;IAC/B,MAAM,+BAA+B;QACnC,MAAM,eAAe,CAAC,gGAAgG,EAAE,WAAW,UAAU,OAAO,6FAA6F,CAAC;QAClP,wBAAwB;QACxB,sBAAsB;IACxB;IAEA,MAAM,qBAAqB;QACzB,IAAI,aAAa,UAAU,SAAS,CAAC,gBAAgB,EAAE;YACrD,MAAM,WAAW,UAAU,SAAS,CAAC,gBAAgB;YACrD,MAAM,kBAAkB,CAAC,SAAS,EAAE,kBAAkB,EAAE,EAAE,EAAE,SAAS,KAAK,CAAC,EAAE,EAAE,SAAS,WAAW,CAAC,WAAW,EAAE,SAAS,SAAS,CAAC,mFAAmF,CAAC;YACxN,wBAAwB;YACxB,sBAAsB;QACxB;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,yBAAyB;QAEzB,wCAAwC;QACxC,IAAI,qBAAqB,QAAQ;YAC/B,MAAM,YAAY,IAAI,yBAAyB;YAC/C,UAAU,IAAI,GAAG;YACjB,UAAU,KAAK,GAAG;YAClB,UAAU,MAAM,GAAG;YAEnB,kCAAkC;YAClC,MAAM,SAAS,gBAAgB,SAAS;YACxC,MAAM,iBAAiB,OAAO,IAAI,CAAC,CAAA,QACjC,MAAM,IAAI,CAAC,QAAQ,CAAC,aACpB,MAAM,IAAI,CAAC,QAAQ,CAAC,gBACpB,MAAM,IAAI,CAAC,QAAQ,CAAC;YAEtB,IAAI,gBAAgB;gBAClB,UAAU,KAAK,GAAG;YACpB;YAEA,UAAU,KAAK,GAAG;gBAChB,yBAAyB;gBACzB,eAAe;YACjB;YAEA,gBAAgB,KAAK,CAAC;QACxB,OAAO;YACL,iDAAiD;YACjD,WAAW;gBACT,yBAAyB;gBACzB,eAAe;YACjB,GAAG;QACL;IACF;IAEA,MAAM,iBAAiB;QACrB,eAAe;QACf,WAAW;YACT,eAAe;QACjB,GAAG,OAAO,kCAAkC;;IAC9C;IAEA,MAAM,gBAAgB;QACpB,eAAe;IACjB;IAEA,IAAI,WAAW,aAAa,CAAC,WAAW;QACtC,qBAAO,6LAAC;sBAAI;;;;;;IACd;IAEA,MAAM,WAAW,UAAU,SAAS,CAAC,gBAAgB;IAErD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAqB,UAAU,KAAK;;;;;;0CAClD,6LAAC;gCAAE,WAAU;;oCAAgB;oCAAU,kBAAkB;oCAAE;oCAAK,UAAU,SAAS,CAAC,MAAM;;;;;;;;;;;;;kCAE5F,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;kDAAM,WAAW;;;;;;;;;;;;0CAEpB,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAc,SAAS;;kDACrC,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAMxC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAW,CAAC,qCAAqC,EAAE,wBAAwB,cAAc,aAAa;;sEAEzG,6LAAC;4DAAI,WAAU;;8EAEb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;;;;;;8EAGf,6LAAC;oEAAI,WAAW,CAAC,kFAAkF,EACjG,wBACI,gDACA,iCACJ;;;;;;gEAGD,uCACC,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;;;;;0FACf,6LAAC;gFAAI,WAAU;gFAAmD,OAAO;oFAAC,gBAAgB;gFAAM;;;;;;0FAChG,6LAAC;gFAAI,WAAU;gFAAmD,OAAO;oFAAC,gBAAgB;gFAAM;;;;;;;;;;;;;;;;;;;;;;;wDAOvG,6BACC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;kFAAK;;;;;;;;;;;;;;;;;;;;;;;8DAOd,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAW,CAAC,sDAAsD,EACrE,wBACI,gDACA,gDACJ;;;;;;;;;;;;;;;;;sDAGN,6LAAC;4CAAE,WAAU;;gDAAgD;gDAC3C,wBAAwB,QAAQ,cAAc,OAAO;;;;;;;;;;;;;;;;;;4BAM1E,oCACC,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;8DAAiB;;;;;;;;;;;0DAEhC,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAOvB,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAG,WAAU;sDAA2B;;;;;;sDAGzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAS;4DACT,UAAU,eAAe;4DACzB,WAAW,cAAc,gCAAgC;sEAExD,4BACC;;kFACE,6LAAC,yMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;6FAIrC;;kFACE,6LAAC,mMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;sEAMtC,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS;4DACT,UAAU;;8EAEV,6LAAC,mNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;8DAM1C,6LAAC;8DACC,cAAA,6LAAC;wDACC,WAAU;wDACV,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;8DAItD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEACb,cAAc,+BAA+B;;;;;;sEAEhD,6LAAC;4DAAI,WAAU;;gEACZ,oBAAoB,KAAK,CAAC,mBAAmB,QAAQ,CAAC,+BACrD,6LAAC,qIAAA,CAAA,SAAM;oEAAC,MAAK;oEAAK,SAAS;oEAAoB,WAAU;8EAAgC;;;;;;gEAI1F,kBAAkB,UAAU,SAAS,CAAC,MAAM,GAAG,mBAC9C,6LAAC,qIAAA,CAAA,SAAM;oEAAC,MAAK;oEAAK,SAAQ;oEAAU,SAAS;8EAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAWvE,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,KAAK;gDACL,WAAU;gDACV,QAAQ;gDACR,WAAW;gDACX,KAAK;;;;;;;;;;;sDAKT,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS,YAAY,YAAY;oDACjC,SAAS;8DAER,0BAAY,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;6EAAe,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEnE,6LAAC,qIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS,YAAY,YAAY;oDACjC,SAAS;8DAER,0BAAY,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;6EAAe,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAE/D,6LAAC,qIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS,kBAAkB,YAAY;oDACvC,SAAS;8DAER,gCAAkB,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;6EAAe,6LAAC,2MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQrF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC;8DAAM,UAAU;;;;;;8DACjB,6LAAC;oDAAI,WAAU;;wDACZ,kBAAkB,mBACjB,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,MAAK;sEAAK;;;;;;wDAIrC,kBAAkB,UAAU,SAAS,CAAC,MAAM,GAAG,mBAC9C,6LAAC,qIAAA,CAAA,SAAM;4DAAC,MAAK;4DAAK,SAAS;sEAAc;;;;;;;;;;;;;;;;;;sDAM/C,6LAAC,mIAAA,CAAA,kBAAe;4CAAC,WAAU;sDACxB,UAAU;;;;;;;;;;;;8CAGf,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC,mIAAA,CAAA,OAAI;wCAAC,cAAa;wCAAW,WAAU;;0DACtC,6LAAC,mIAAA,CAAA,WAAQ;gDAAC,WAAU;;kEAClB,6LAAC,mIAAA,CAAA,cAAW;wDAAC,OAAM;kEAAW;;;;;;kEAC9B,6LAAC,mIAAA,CAAA,cAAW;wDAAC,OAAM;kEAAO;;;;;;kEAC1B,6LAAC,mIAAA,CAAA,cAAW;wDAAC,OAAM;kEAAQ;;;;;;;;;;;;0DAG7B,6LAAC,mIAAA,CAAA,cAAW;gDAAC,OAAM;gDAAW,WAAU;0DACtC,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAqB;;;;;;sEACnC,6LAAC;4DAAE,WAAU;sEAAsB,UAAU;;;;;;sEAC7C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;wEAAK;wEAAO,UAAU;;;;;;;8EACvB,6LAAC;;wEAAK;wEAAa,UAAU;wEAAU;;;;;;;;;;;;;;;;;;;;;;;;0DAK7C,6LAAC,mIAAA,CAAA,cAAW;gDAAC,OAAM;gDAAO,WAAU;0DAClC,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,WAAU;wDACV,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;0DAK7C,6LAAC,mIAAA,CAAA,cAAW;gDAAC,OAAM;gDAAQ,WAAU;0DACnC,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,WAAU;wDACV,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUtD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;8CAI9C,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;sDAEb,cAAA,6LAAC;gDAAI,WAAU;0DAA4B;;;;;;;;;;;sDAI7C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,WAAU;;;;;;8DAEZ,6LAAC,qIAAA,CAAA,SAAM;oDAAC,MAAK;8DACX,cAAA,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlC;GA3jBwB;;QACY,iJAAA,CAAA,aAAU;QAC7B,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}]}