'use client'

import { useEffect, useState } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { EmailService, InterviewInvitation } from '@/lib/email-service'
import { VideoUploadService, UploadResult } from '@/lib/video-upload'
import { DemoAuth } from '@/lib/demo-auth'
import { 
  CheckCircle, 
  Upload, 
  Download, 
  ExternalLink,
  Star,
  Clock,
  User,
  Trophy,
  Share2,
  Home,
  RefreshCw,
  AlertCircle
} from 'lucide-react'

export default function InterviewCompletePage() {
  const params = useParams()
  const router = useRouter()
  const token = params.token as string

  const [invitation, setInvitation] = useState<InterviewInvitation | null>(null)
  const [loading, setLoading] = useState(true)
  const [uploadResults, setUploadResults] = useState<UploadResult[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [uploadComplete, setUploadComplete] = useState(false)
  const [congratsSpoken, setCongratsSpoken] = useState(false)
  const [showResults, setShowResults] = useState(false)

  // Mock interview results
  const interviewResults = {
    overallScore: 85,
    duration: '32 minutes',
    questionsAnswered: 5,
    strengths: [
      'Clear communication skills',
      'Good technical knowledge',
      'Confident presentation',
      'Problem-solving approach'
    ],
    improvements: [
      'Could provide more specific examples',
      'Consider elaborating on past experiences'
    ],
    feedback: 'Great job! You demonstrated strong technical skills and communicated your thoughts clearly. Your problem-solving approach was methodical and well-structured. Keep practicing behavioral questions with specific examples from your experience.'
  }

  useEffect(() => {
    // Check authentication
    const currentUser = DemoAuth.getCurrentUser()
    if (!currentUser) {
      router.push(`/interview/invite/${token}`)
      return
    }

    // Load invitation
    if (token) {
      const invitationData = EmailService.getInvitationByToken(token)
      if (invitationData) {
        setInvitation(invitationData)
        // Mark invitation as completed
        EmailService.updateInvitationStatus(token, 'COMPLETED')
      } else {
        router.push('/')
        return
      }
      setLoading(false)
    }
  }, [token, router])

  useEffect(() => {
    // Speak congratulations message
    if (invitation && !congratsSpoken) {
      setCongratsSpoken(true)
      speakCongratulations()
      
      // Start video upload simulation
      setTimeout(() => {
        simulateVideoUpload()
      }, 3000)
      
      // Show results after upload
      setTimeout(() => {
        setShowResults(true)
      }, 8000)
    }
  }, [invitation, congratsSpoken])

  const speakCongratulations = () => {
    const message = `Congratulations ${invitation?.studentName}! You have successfully completed your video interview for ${invitation?.interviewTitle}. Your responses have been recorded and will be reviewed by our team. You should receive feedback within 24 to 48 hours. Thank you for your time and effort!`
    
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(message)
      utterance.rate = 0.9
      utterance.pitch = 1
      utterance.volume = 0.8

      const voices = window.speechSynthesis.getVoices()
      const preferredVoice = voices.find(voice => 
        voice.name.includes('Google') || 
        voice.name.includes('Microsoft') ||
        voice.lang.startsWith('en')
      )
      if (preferredVoice) {
        utterance.voice = preferredVoice
      }

      window.speechSynthesis.speak(utterance)
    }
  }

  const simulateVideoUpload = async () => {
    setIsUploading(true)
    
    // Simulate video blob (in real app, this would come from the recording)
    const mockVideoBlob = new Blob(['mock video data'], { type: 'video/webm' })
    
    try {
      const results = await VideoUploadService.uploadVideo(
        mockVideoBlob,
        invitation?.studentName || 'Student',
        invitation?.interviewTitle || 'Interview'
      )
      
      setUploadResults(results)
      setUploadComplete(true)
    } catch (error) {
      console.error('Upload failed:', error)
    } finally {
      setIsUploading(false)
    }
  }

  const downloadRecording = () => {
    // In a real app, this would download the actual recording
    const mockVideoBlob = new Blob(['mock video data'], { type: 'video/webm' })
    VideoUploadService.downloadVideo(
      mockVideoBlob, 
      `interview_${invitation?.studentName?.replace(/\s+/g, '_')}_${Date.now()}.webm`
    )
  }

  const shareResults = () => {
    if (navigator.share) {
      navigator.share({
        title: 'Interview Completed!',
        text: `I just completed my video interview for ${invitation?.interviewTitle}!`,
        url: window.location.href
      })
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
      alert('Link copied to clipboard!')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p>Loading results...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-600 to-blue-600 p-6">
        <div className="max-w-4xl mx-auto text-center">
          <div className="flex justify-center mb-4">
            <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center">
              <Trophy className="h-12 w-12 text-green-600" />
            </div>
          </div>
          <h1 className="text-4xl font-bold text-white mb-2">🎉 Congratulations!</h1>
          <p className="text-green-100 text-lg">You've successfully completed your video interview!</p>
        </div>
      </div>

      <div className="max-w-4xl mx-auto p-6">
        <div className="grid md:grid-cols-2 gap-6">
          {/* Interview Summary */}
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <CheckCircle className="h-5 w-5 mr-2 text-green-500" />
                Interview Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-3">
                <User className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-400">Candidate</p>
                  <p className="text-white font-medium">{invitation?.studentName}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Star className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-400">Position</p>
                  <p className="text-white font-medium">{invitation?.interviewTitle}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Clock className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-400">Completed</p>
                  <p className="text-white font-medium">{new Date().toLocaleString()}</p>
                </div>
              </div>

              {showResults && (
                <div className="mt-6 p-4 bg-green-900 border border-green-700 rounded-lg">
                  <h4 className="text-green-100 font-medium mb-2">Overall Score</h4>
                  <div className="flex items-center space-x-3">
                    <div className="text-3xl font-bold text-green-400">{interviewResults.overallScore}</div>
                    <div className="text-green-200">/ 100</div>
                  </div>
                  <p className="text-green-200 text-sm mt-1">Excellent performance!</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Upload Status */}
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Upload className="h-5 w-5 mr-2 text-blue-500" />
                Recording Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isUploading ? (
                <div className="text-center py-6">
                  <RefreshCw className="h-8 w-8 text-blue-500 mx-auto mb-4 animate-spin" />
                  <p className="text-white mb-2">Uploading your interview recording...</p>
                  <p className="text-gray-400 text-sm">This may take a few moments</p>
                </div>
              ) : uploadComplete ? (
                <div className="space-y-3">
                  <div className="flex items-center space-x-2 text-green-400">
                    <CheckCircle className="h-5 w-5" />
                    <span>Recording uploaded successfully!</span>
                  </div>
                  
                  {uploadResults.map((result, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                      <div className="flex items-center space-x-2">
                        {result.success ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                          <AlertCircle className="h-4 w-4 text-red-500" />
                        )}
                        <span className="text-white text-sm capitalize">{result.platform}</span>
                      </div>
                      {result.success && result.url && (
                        <Button size="sm" variant="outline" className="border-gray-600">
                          <ExternalLink className="h-3 w-3 mr-1" />
                          View
                        </Button>
                      )}
                    </div>
                  ))}
                  
                  <Button onClick={downloadRecording} variant="outline" className="w-full border-gray-600">
                    <Download className="h-4 w-4 mr-2" />
                    Download Recording
                  </Button>
                </div>
              ) : (
                <div className="text-center py-6">
                  <Upload className="h-8 w-8 text-gray-500 mx-auto mb-4" />
                  <p className="text-gray-400">Preparing to upload recording...</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Detailed Results */}
        {showResults && (
          <div className="mt-6 grid md:grid-cols-2 gap-6">
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">Performance Highlights</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="text-green-400 font-medium mb-2">Strengths</h4>
                    <ul className="space-y-1">
                      {interviewResults.strengths.map((strength, index) => (
                        <li key={index} className="text-gray-300 text-sm flex items-center">
                          <CheckCircle className="h-3 w-3 text-green-500 mr-2" />
                          {strength}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="text-yellow-400 font-medium mb-2">Areas for Improvement</h4>
                    <ul className="space-y-1">
                      {interviewResults.improvements.map((improvement, index) => (
                        <li key={index} className="text-gray-300 text-sm flex items-center">
                          <Star className="h-3 w-3 text-yellow-500 mr-2" />
                          {improvement}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">Detailed Feedback</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 text-sm leading-relaxed">
                  {interviewResults.feedback}
                </p>
                
                <div className="mt-4 p-3 bg-blue-900 border border-blue-700 rounded-lg">
                  <p className="text-blue-100 text-sm">
                    <strong>Next Steps:</strong> Our team will review your recording and provide additional feedback within 24-48 hours. Check your email for updates!
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Action Buttons */}
        <div className="mt-8 flex justify-center space-x-4">
          <Button onClick={shareResults} variant="outline" className="border-gray-600">
            <Share2 className="h-4 w-4 mr-2" />
            Share Results
          </Button>
          
          <Button onClick={() => router.push('/')} className="bg-blue-600 hover:bg-blue-700">
            <Home className="h-4 w-4 mr-2" />
            Back to Home
          </Button>
        </div>

        {/* Thank You Message */}
        <div className="mt-8 text-center">
          <Card className="bg-gradient-to-r from-blue-900 to-purple-900 border-blue-700">
            <CardContent className="p-6">
              <h3 className="text-xl font-bold text-white mb-2">Thank You!</h3>
              <p className="text-blue-100">
                We appreciate the time and effort you put into this interview. 
                Your responses will help us understand your skills and potential. 
                We'll be in touch soon with next steps!
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
