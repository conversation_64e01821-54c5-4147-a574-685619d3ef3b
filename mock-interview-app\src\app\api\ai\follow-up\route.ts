import { NextRequest, NextResponse } from 'next/server'
import { OpenAIService } from '@/lib/openai-service'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { originalQuestion, candidateAnswer } = body

    // Validate input
    if (!originalQuestion || !candidateAnswer) {
      return NextResponse.json(
        { error: 'Original question and candidate answer are required' },
        { status: 400 }
      )
    }

    // Check if OpenAI is configured
    if (!OpenAIService.isConfigured()) {
      // Mock follow-up questions
      const mockFollowUps = [
        "Can you elaborate on that approach?",
        "What challenges did you face with that solution?",
        "How would you improve that implementation?",
        "Can you walk me through your thought process?",
        "What alternatives did you consider?"
      ]

      const randomFollowUp = mockFollowUps[Math.floor(Math.random() * mockFollowUps.length)]

      return NextResponse.json({
        success: true,
        followUpQuestion: randomFollowUp,
        source: 'mock'
      })
    }

    // Use real OpenAI generation
    const followUpQuestion = await OpenAIService.generateFollowUp(originalQuestion, candidateAnswer)

    return NextResponse.json({
      success: true,
      followUpQuestion,
      source: 'openai'
    })

  } catch (error) {
    console.error('Error generating follow-up:', error)
    return NextResponse.json(
      { error: 'Failed to generate follow-up question' },
      { status: 500 }
    )
  }
}
