'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { MainNav } from '@/components/navigation/main-nav'
import { 
  Users, 
  Calendar, 
  TrendingUp, 
  BookOpen, 
  Plus,
  BarChart3,
  Clock,
  CheckCircle
} from 'lucide-react'

interface TenantStats {
  totalStudents: number
  activeInterviews: number
  completedInterviews: number
  averageScore: number
  totalQuestions: number
}

export default function TenantDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [stats, setStats] = useState<TenantStats>({
    totalStudents: 0,
    activeInterviews: 0,
    completedInterviews: 0,
    averageScore: 0,
    totalQuestions: 0
  })
  const [recentInterviews, setRecentInterviews] = useState([])
  const [topPerformers, setTopPerformers] = useState([])

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/signin')
      return
    }

    if (session.user.role !== 'TENANT') {
      router.push('/')
      return
    }

    fetchDashboardData()
  }, [session, status, router])

  const fetchDashboardData = async () => {
    try {
      // Mock data - replace with actual API calls
      setStats({
        totalStudents: 45,
        activeInterviews: 8,
        completedInterviews: 127,
        averageScore: 76,
        totalQuestions: 89
      })

      setRecentInterviews([
        {
          id: '1',
          studentName: 'John Doe',
          position: 'Frontend Developer',
          scheduledAt: new Date('2024-06-20T10:00:00'),
          status: 'SCHEDULED'
        },
        {
          id: '2',
          studentName: 'Jane Smith',
          position: 'Backend Developer',
          scheduledAt: new Date('2024-06-19T14:00:00'),
          status: 'COMPLETED',
          score: 85
        },
        {
          id: '3',
          studentName: 'Mike Johnson',
          position: 'Full Stack Developer',
          scheduledAt: new Date('2024-06-21T09:00:00'),
          status: 'IN_PROGRESS'
        }
      ])

      setTopPerformers([
        { name: 'Alice Brown', score: 92, interviews: 3 },
        { name: 'Bob Wilson', score: 88, interviews: 2 },
        { name: 'Carol Davis', score: 85, interviews: 4 }
      ])
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    }
  }

  if (status === 'loading') {
    return <div>Loading...</div>
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <MainNav />
      
      <div className="py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="mb-8 flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Organization Dashboard
              </h1>
              <p className="text-gray-600 mt-2">
                Manage your interview processes and track student performance
              </p>
            </div>
            <div className="flex space-x-4">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Schedule Interview
              </Button>
              <Button variant="outline">
                <BookOpen className="h-4 w-4 mr-2" />
                Add Questions
              </Button>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Students</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalStudents}</div>
                <p className="text-xs text-muted-foreground">
                  +12% from last month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Interviews</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.activeInterviews}</div>
                <p className="text-xs text-muted-foreground">
                  Currently in progress
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Completed</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.completedInterviews}</div>
                <p className="text-xs text-muted-foreground">
                  Total interviews
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Average Score</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.averageScore}%</div>
                <p className="text-xs text-muted-foreground">
                  +3% from last month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Questions</CardTitle>
                <BookOpen className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalQuestions}</div>
                <p className="text-xs text-muted-foreground">
                  In question bank
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Recent Interviews */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="h-5 w-5 mr-2" />
                  Recent Interviews
                </CardTitle>
                <CardDescription>
                  Latest interview activities
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentInterviews.map((interview: any) => (
                    <div key={interview.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h3 className="font-medium">{interview.studentName}</h3>
                        <p className="text-sm text-gray-600">{interview.position}</p>
                        <p className="text-sm text-gray-500">
                          {interview.scheduledAt.toLocaleDateString()} at {interview.scheduledAt.toLocaleTimeString()}
                        </p>
                      </div>
                      <div className="text-right">
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          interview.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                          interview.status === 'IN_PROGRESS' ? 'bg-blue-100 text-blue-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {interview.status}
                        </span>
                        {interview.score && (
                          <p className="text-sm font-medium mt-1">{interview.score}%</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Top Performers */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2" />
                  Top Performers
                </CardTitle>
                <CardDescription>
                  Students with highest scores
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {topPerformers.map((performer: any, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold mr-3 ${
                          index === 0 ? 'bg-yellow-500' :
                          index === 1 ? 'bg-gray-400' :
                          'bg-orange-500'
                        }`}>
                          {index + 1}
                        </div>
                        <div>
                          <h3 className="font-medium">{performer.name}</h3>
                          <p className="text-sm text-gray-600">{performer.interviews} interviews</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <span className="text-lg font-bold text-green-600">{performer.score}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className="mt-8">
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Common management tasks
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <Button className="h-20 flex flex-col items-center justify-center">
                    <Plus className="h-6 w-6 mb-2" />
                    Schedule Interview
                  </Button>
                  <Button variant="outline" className="h-20 flex flex-col items-center justify-center">
                    <Users className="h-6 w-6 mb-2" />
                    Manage Students
                  </Button>
                  <Button variant="outline" className="h-20 flex flex-col items-center justify-center">
                    <BookOpen className="h-6 w-6 mb-2" />
                    Question Bank
                  </Button>
                  <Button variant="outline" className="h-20 flex flex-col items-center justify-center">
                    <BarChart3 className="h-6 w-6 mb-2" />
                    View Reports
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
