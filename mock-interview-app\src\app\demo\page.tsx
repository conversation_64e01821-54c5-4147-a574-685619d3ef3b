'use client'

import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { MainNav } from '@/components/navigation/main-nav'
import {
  User,
  Building,
  GraduationCap,
  Shield,
  Copy,
  ExternalLink
} from 'lucide-react'

export default function DemoPage() {
  const demoAccounts = [
    {
      role: 'Admin',
      icon: Shield,
      email: '<EMAIL>',
      password: 'admin123',
      description: 'Full system access, manage tenants and users',
      features: ['Manage all tenants', 'System analytics', 'User management', 'Platform settings'],
      color: 'border-red-200 bg-red-50'
    },
    {
      role: 'Tenant (Organization)',
      icon: Building,
      email: '<EMAIL>',
      password: 'tenant123',
      description: 'Organization manager, schedule interviews',
      features: ['Manage students', 'Schedule interviews', 'Create questions', 'View reports'],
      color: 'border-blue-200 bg-blue-50'
    },
    {
      role: 'Student',
      icon: GraduationCap,
      email: '<EMAIL>',
      password: 'student123',
      description: 'Student account for taking interviews',
      features: ['Take interviews', 'View results', 'Track progress', 'Practice questions'],
      color: 'border-green-200 bg-green-50'
    }
  ]

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <MainNav />
      
      <div className="py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              🎯 Demo Accounts
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Try our Mock Interview Platform with these pre-configured demo accounts. 
              Each account type provides different features and access levels.
            </p>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 max-w-2xl mx-auto">
              <p className="text-yellow-800">
                <strong>Demo Mode:</strong> This application is running in demo mode. 
                No real data is stored, and you can safely test all features.
              </p>
            </div>
          </div>

          {/* Demo Accounts */}
          <div className="grid md:grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
            {demoAccounts.map((account, index) => {
              const Icon = account.icon
              return (
                <Card key={index} className={`${account.color} border-2`}>
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <Icon className="h-8 w-8" />
                      <div>
                        <CardTitle className="text-xl">{account.role}</CardTitle>
                        <CardDescription className="text-gray-700">
                          {account.description}
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {/* Credentials */}
                      <div className="bg-white rounded-lg p-4 border">
                        <h4 className="font-semibold mb-2">Login Credentials:</h4>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">Email:</span>
                            <div className="flex items-center space-x-2">
                              <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                                {account.email}
                              </code>
                              <button
                                onClick={() => copyToClipboard(account.email)}
                                className="text-gray-500 hover:text-gray-700"
                              >
                                <Copy className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">Password:</span>
                            <div className="flex items-center space-x-2">
                              <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                                {account.password}
                              </code>
                              <button
                                onClick={() => copyToClipboard(account.password)}
                                className="text-gray-500 hover:text-gray-700"
                              >
                                <Copy className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Features */}
                      <div>
                        <h4 className="font-semibold mb-2">Features Available:</h4>
                        <ul className="space-y-1">
                          {account.features.map((feature, idx) => (
                            <li key={idx} className="text-sm text-gray-700 flex items-center">
                              <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* Login Button */}
                      <Link href="/auth/signin" className="block">
                        <Button className="w-full">
                          <User className="h-4 w-4 mr-2" />
                          Login as {account.role}
                        </Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>

          {/* Quick Start Guide */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="text-2xl">🚀 Quick Start Guide</CardTitle>
              <CardDescription>
                Follow these steps to explore the platform
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="font-semibold text-lg mb-4">For Students:</h3>
                  <ol className="space-y-2 text-sm">
                    <li className="flex items-start">
                      <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3 mt-0.5">1</span>
                      Login with student credentials
                    </li>
                    <li className="flex items-start">
                      <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3 mt-0.5">2</span>
                      Explore your dashboard and upcoming interviews
                    </li>
                    <li className="flex items-start">
                      <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3 mt-0.5">3</span>
                      Join a mock interview session
                    </li>
                    <li className="flex items-start">
                      <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3 mt-0.5">4</span>
                      View your results and feedback
                    </li>
                  </ol>
                </div>
                <div>
                  <h3 className="font-semibold text-lg mb-4">For Organizations:</h3>
                  <ol className="space-y-2 text-sm">
                    <li className="flex items-start">
                      <span className="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3 mt-0.5">1</span>
                      Login with tenant credentials
                    </li>
                    <li className="flex items-start">
                      <span className="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3 mt-0.5">2</span>
                      Manage your question bank
                    </li>
                    <li className="flex items-start">
                      <span className="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3 mt-0.5">3</span>
                      Schedule interviews for students
                    </li>
                    <li className="flex items-start">
                      <span className="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3 mt-0.5">4</span>
                      Review performance analytics
                    </li>
                  </ol>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Additional Demo Accounts */}
          <Card>
            <CardHeader>
              <CardTitle>Additional Demo Accounts</CardTitle>
              <CardDescription>
                More accounts available for testing different scenarios
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-semibold">Alternative Student</h4>
                  <p className="text-sm text-gray-600 mb-2">Another student account for testing</p>
                  <div className="space-y-1 text-sm">
                    <div>Email: <code><EMAIL></code></div>
                    <div>Password: <code>student123</code></div>
                  </div>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-semibold">Startup Organization</h4>
                  <p className="text-sm text-gray-600 mb-2">Different organization for testing</p>
                  <div className="space-y-1 text-sm">
                    <div>Email: <code><EMAIL></code></div>
                    <div>Password: <code>startup123</code></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="text-center mt-12">
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth/signin">
                <Button size="lg" className="text-lg px-8 py-3">
                  <User className="h-5 w-5 mr-2" />
                  Go to Login
                </Button>
              </Link>
              <Link href="/auth/signup">
                <Button variant="outline" size="lg" className="text-lg px-8 py-3">
                  <ExternalLink className="h-5 w-5 mr-2" />
                  Create New Account
                </Button>
              </Link>
              <Link href="/">
                <Button variant="outline" size="lg" className="text-lg px-8 py-3">
                  Back to Home
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
