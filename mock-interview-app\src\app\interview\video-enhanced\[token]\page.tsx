'use client'

import { useEffect, useState, useRef } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { EmailService, InterviewInvitation } from '@/lib/email-service'
import { DemoAuth } from '@/lib/demo-auth'
import { 
  Play, 
  Pause, 
  Ski<PERSON><PERSON>or<PERSON>, 
  RotateCcw,
  Circle,
  Maximize,
  Minimize,
  Code,
  PlayCircle,
  Loader2
} from 'lucide-react'

// Enhanced interview questions with coding challenges
const interviewQuestions = [
  {
    id: 1,
    type: 'introduction',
    question: "Hello! 👋 Let's start with a brief introduction. Please tell me about yourself, your background, and what interests you about this position.",
    timeLimit: 120,
    hasCode: false
  },
  {
    id: 2,
    type: 'technical',
    question: "Can you explain the difference between let, const, and var in JavaScript? Please write a simple example demonstrating each one.",
    timeLimit: 300,
    hasCode: true,
    codeTemplate: `// Demonstrate the differences between let, const, and var
// Write your examples below:

// Example 1: var
function varExample() {
  var x = 1;
  if (true) {
    var x = 2; // Same variable
    console.log(x); // 2
  }
  console.log(x); // 2
}

// Example 2: let
function letExample() {
  let y = 1;
  if (true) {
    let y = 2; // Different variable
    console.log(y); // 2
  }
  console.log(y); // 1
}

// Example 3: const
function constExample() {
  const z = 1;
  // z = 2; // This would cause an error
  console.log(z); // 1
}

// Test your examples
varExample();
letExample();
constExample();`
  },
  {
    id: 3,
    type: 'coding',
    question: "Write a function that finds the maximum number in an array. Explain your approach and implement it.",
    timeLimit: 360,
    hasCode: true,
    codeTemplate: `// Find the maximum number in an array
// Implement the function below:

function findMaximum(numbers) {
  // Your implementation here
  if (numbers.length === 0) return undefined;
  
  let max = numbers[0];
  for (let i = 1; i < numbers.length; i++) {
    if (numbers[i] > max) {
      max = numbers[i];
    }
  }
  return max;
}

// Test cases
const testArray1 = [1, 5, 3, 9, 2];
const testArray2 = [-1, -5, -3, -9, -2];
const testArray3 = [42];

console.log("Test 1:", findMaximum(testArray1)); // Expected: 9
console.log("Test 2:", findMaximum(testArray2)); // Expected: -1
console.log("Test 3:", findMaximum(testArray3)); // Expected: 42`
  },
  {
    id: 4,
    type: 'problem-solving',
    question: "Debug this code: There's a function that should reverse a string, but it's not working correctly. Find and fix the bug.",
    timeLimit: 300,
    hasCode: true,
    codeTemplate: `// Debug this function - it should reverse a string but has a bug
function reverseString(str) {
  let reversed = "";
  // Bug: i <= str.length should be i < str.length
  for (let i = 0; i < str.length; i++) {
    reversed += str[str.length - 1 - i];
  }
  return reversed;
}

// Test the function
console.log(reverseString("hello")); // Should output: "olleh"
console.log(reverseString("JavaScript")); // Should output: "tpircSavaJ"

// The bug was: i <= str.length caused accessing str[str.length] which is undefined`
  },
  {
    id: 5,
    type: 'closing',
    question: "Do you have any questions about the role or our company? Is there anything else you'd like me to know about you?",
    timeLimit: 120,
    hasCode: false
  }
]

export default function EnhancedVideoInterviewPage() {
  const params = useParams()
  const router = useRouter()
  const token = params.token as string
  
  const [loading, setLoading] = useState(true)
  const [invitation, setInvitation] = useState<InterviewInvitation | null>(null)
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [isRecording, setIsRecording] = useState(false)
  const [isInterviewerSpeaking, setIsInterviewerSpeaking] = useState(false)
  const [isListening, setIsListening] = useState(false)
  const [timeElapsed, setTimeElapsed] = useState(0)
  const [interviewerMessage, setInterviewerMessage] = useState('')
  const [hasStarted, setHasStarted] = useState(false)
  
  // Code editor states
  const [code, setCode] = useState('')
  const [codeOutput, setCodeOutput] = useState('')
  const [isCodeExpanded, setIsCodeExpanded] = useState(false)
  const [isRunning, setIsRunning] = useState(false)

  const videoRef = useRef<HTMLVideoElement>(null)
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const recordedChunksRef = useRef<Blob[]>([])

  const currentQuestion = interviewQuestions[currentQuestionIndex]

  useEffect(() => {
    // Check authentication
    const currentUser = DemoAuth.getCurrentUser()
    if (!currentUser) {
      router.push(`/interview/invite/${token}`)
      return
    }

    // Load invitation
    if (token) {
      const invitationData = EmailService.getInvitationByToken(token)
      if (invitationData) {
        setInvitation(invitationData)
      } else {
        router.push('/')
        return
      }
      setLoading(false)
    }
  }, [token, router])

  useEffect(() => {
    let interval: NodeJS.Timeout
    if (hasStarted && !loading) {
      interval = setInterval(() => {
        setTimeElapsed(prev => prev + 1)
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [hasStarted, loading])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const setupCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: true, 
        audio: true 
      })
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream
      }

      const mediaRecorder = new MediaRecorder(stream)
      mediaRecorderRef.current = mediaRecorder

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          recordedChunksRef.current.push(event.data)
        }
      }

      return true
    } catch (error) {
      console.error('Error accessing camera:', error)
      return false
    }
  }

  const speakQuestion = (question: string) => {
    setIsInterviewerSpeaking(true)
    setInterviewerMessage(question)

    // Load code template if question has code
    if (currentQuestion?.hasCode && currentQuestion?.codeTemplate) {
      setCode(currentQuestion.codeTemplate)
      setCodeOutput('')
    }

    if ('speechSynthesis' in window) {
      window.speechSynthesis.cancel()
      
      const utterance = new SpeechSynthesisUtterance(question)
      utterance.rate = 0.9
      utterance.pitch = 1
      utterance.volume = 0.8

      const voices = window.speechSynthesis.getVoices()
      const preferredVoice = voices.find(voice => 
        voice.name.includes('Google') || 
        voice.name.includes('Microsoft') ||
        voice.lang.startsWith('en')
      )
      if (preferredVoice) {
        utterance.voice = preferredVoice
      }

      utterance.onend = () => {
        setIsInterviewerSpeaking(false)
        setIsListening(true)
      }

      utterance.onerror = () => {
        setIsInterviewerSpeaking(false)
        setIsListening(true)
      }

      window.speechSynthesis.speak(utterance)
    } else {
      setTimeout(() => {
        setIsInterviewerSpeaking(false)
        setIsListening(true)
      }, 3000)
    }
  }

  const runCode = async () => {
    setIsRunning(true)
    setCodeOutput('Running code...\n')

    try {
      // Create a safe execution environment
      const originalConsoleLog = console.log
      let output = ''
      
      // Override console.log to capture output
      console.log = (...args) => {
        output += args.map(arg => 
          typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
        ).join(' ') + '\n'
      }

      // Execute the code in a try-catch block
      const func = new Function(code)
      func()

      // Restore original console.log
      console.log = originalConsoleLog

      setCodeOutput(output || 'Code executed successfully (no output)')
    } catch (error) {
      setCodeOutput(`Error: ${error.message}`)
    }

    setIsRunning(false)
  }

  const startInterview = async () => {
    const cameraReady = await setupCamera()
    if (!cameraReady) {
      alert('Please allow camera and microphone access to continue.')
      return
    }

    setHasStarted(true)
    speakQuestion("Welcome to your AI-powered video interview! I'm your virtual interviewer. Let's begin with our first question.")
    
    setTimeout(() => {
      speakQuestion(currentQuestion.question)
    }, 3000)
  }

  const startRecording = () => {
    if (mediaRecorderRef.current && !isRecording) {
      recordedChunksRef.current = []
      mediaRecorderRef.current.start()
      setIsRecording(true)
      setIsListening(true)
    }
  }

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
      setIsRecording(false)
      setIsListening(false)
    }
  }

  const nextQuestion = () => {
    stopRecording()
    
    if (currentQuestionIndex < interviewQuestions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1)
      setTimeout(() => {
        speakQuestion(interviewQuestions[currentQuestionIndex + 1].question)
      }, 1000)
    } else {
      completeInterview()
    }
  }

  const repeatQuestion = () => {
    speakQuestion(currentQuestion.question)
  }

  const completeInterview = () => {
    stopRecording()
    speakQuestion("Congratulations! You have successfully completed the interview. Thank you for your time and responses.")
    
    setTimeout(() => {
      router.push(`/interview/complete/${token}`)
    }, 4000)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p>Loading interview...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <div className="bg-gray-800 p-4 flex justify-between items-center">
        <div>
          <h1 className="text-xl font-bold text-white">🎯 AI Video Interview with Code Editor</h1>
          <p className="text-gray-400">
            Question {currentQuestionIndex + 1} of {interviewQuestions.length}
            {hasStarted && ` • ${formatTime(timeElapsed)}`}
          </p>
        </div>
        <div className="flex items-center space-x-4">
          {isRecording && (
            <div className="flex items-center space-x-2 text-red-400">
              <Circle className="h-3 w-3 fill-current animate-pulse" />
              <span className="text-sm">Recording</span>
            </div>
          )}
          <Button variant="destructive" onClick={completeInterview}>
            End Interview
          </Button>
        </div>
      </div>

      <div className={`flex h-[calc(100vh-80px)] transition-all duration-300 ${isCodeExpanded ? 'flex-col' : ''}`}>
        {/* Left Panel: AI Interviewer Emoji */}
        <div className={`${isCodeExpanded ? 'h-20' : 'w-1/4'} p-4 space-y-4 transition-all duration-300`}>
          <Card className="bg-gray-800 border-gray-700 h-full">
            <CardHeader>
              <CardTitle className="text-white text-sm flex items-center gap-2">
                🤖 AI Interviewer
                {isCodeExpanded && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsCodeExpanded(false)}
                    className="ml-auto text-gray-400 hover:text-white"
                  >
                    <Minimize className="h-4 w-4" />
                  </Button>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className={isCodeExpanded ? 'py-2' : ''}>
              <div className={`flex ${isCodeExpanded ? 'flex-row items-center space-x-4' : 'flex-col'}`}>
                {/* Large Interviewer Emoji */}
                <div className="relative">
                  <div className={`${isCodeExpanded ? 'w-12 h-12 text-3xl' : 'w-20 h-20 text-5xl'} mx-auto rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center transition-all duration-300 ${
                    isInterviewerSpeaking ? 'animate-pulse scale-110' : ''
                  }`}>
                    {isInterviewerSpeaking ? '🗣️' : isListening ? '👂' : '🤖'}
                  </div>
                  
                  {/* Speaking indicator */}
                  {isInterviewerSpeaking && (
                    <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2">
                      <div className="flex space-x-1">
                        <div className="w-1 h-1 bg-green-400 rounded-full animate-bounce"></div>
                        <div className="w-1 h-1 bg-green-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                        <div className="w-1 h-1 bg-green-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Status and Message */}
                <div className={`${isCodeExpanded ? 'flex-1' : 'text-center mt-4'}`}>
                  <p className="text-xs text-gray-300 mb-2">
                    {isInterviewerSpeaking ? '🎙️ Speaking...' : 
                     isListening ? '👂 Listening...' : 
                     '💭 Thinking...'}
                  </p>
                  
                  {interviewerMessage && !isCodeExpanded && (
                    <div className="bg-gray-700/50 p-2 rounded text-xs text-white">
                      {interviewerMessage.length > 100 ? 
                        interviewerMessage.substring(0, 100) + '...' : 
                        interviewerMessage}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Center Panel: Video Screen */}
        {!isCodeExpanded && (
          <div className="w-1/4 p-4">
            <Card className="bg-gray-800 border-gray-700 h-full">
              <CardHeader>
                <CardTitle className="text-white text-sm">📹 Your Video</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="relative">
                  <video
                    ref={videoRef}
                    autoPlay
                    muted
                    playsInline
                    className="w-full h-48 bg-gray-900 rounded-lg object-cover"
                  />

                  {/* Recording indicator */}
                  {isRecording && (
                    <div className="absolute top-2 right-2 flex items-center gap-1 bg-red-600 text-white px-2 py-1 rounded-full text-xs">
                      <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                      REC
                    </div>
                  )}
                </div>

                {/* Video Controls */}
                <div className="mt-4 space-y-2">
                  {!hasStarted ? (
                    <Button onClick={startInterview} className="w-full bg-blue-600 hover:bg-blue-700">
                      <Play className="h-4 w-4 mr-2" />
                      Start Interview
                    </Button>
                  ) : (
                    <div className="space-y-2">
                      <div className="flex gap-2">
                        {!isRecording ? (
                          <Button onClick={startRecording} className="flex-1 bg-green-600 hover:bg-green-700">
                            <Circle className="h-4 w-4 mr-2" />
                            Record Answer
                          </Button>
                        ) : (
                          <Button onClick={stopRecording} className="flex-1 bg-red-600 hover:bg-red-700">
                            <Pause className="h-4 w-4 mr-2" />
                            Stop Recording
                          </Button>
                        )}
                      </div>

                      <div className="flex gap-2">
                        <Button onClick={repeatQuestion} variant="outline" className="flex-1">
                          <RotateCcw className="h-4 w-4 mr-2" />
                          Repeat
                        </Button>
                        <Button onClick={nextQuestion} className="flex-1 bg-purple-600 hover:bg-purple-700">
                          <SkipForward className="h-4 w-4 mr-2" />
                          Next
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Right Panel: Code Editor */}
        <div className={`${isCodeExpanded ? 'flex-1' : 'w-1/2'} p-4 transition-all duration-300`}>
          <Card className="bg-gray-800 border-gray-700 h-full">
            <CardHeader>
              <CardTitle className="text-white text-sm flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Code className="h-4 w-4" />
                  Code Editor
                  {currentQuestion?.hasCode && (
                    <span className="text-xs bg-blue-600 px-2 py-1 rounded">
                      Coding Question
                    </span>
                  )}
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsCodeExpanded(!isCodeExpanded)}
                    className="text-gray-400 hover:text-white"
                  >
                    {isCodeExpanded ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="h-full flex flex-col">
              {currentQuestion?.hasCode ? (
                <div className="flex-1 flex flex-col space-y-4">
                  {/* Question Display */}
                  <div className="bg-gray-700/50 p-3 rounded-lg">
                    <p className="text-white text-sm">{currentQuestion.question}</p>
                  </div>

                  {/* Code Editor */}
                  <div className="flex-1 flex flex-col">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-xs text-gray-400">JavaScript Code Editor</span>
                      <Button
                        onClick={runCode}
                        disabled={isRunning}
                        className="bg-green-600 hover:bg-green-700 text-xs px-3 py-1"
                      >
                        {isRunning ? (
                          <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                        ) : (
                          <PlayCircle className="h-3 w-3 mr-1" />
                        )}
                        Run Code
                      </Button>
                    </div>

                    <textarea
                      value={code}
                      onChange={(e) => setCode(e.target.value)}
                      className={`flex-1 bg-gray-900 text-white font-mono text-sm p-4 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none resize-none ${
                        isCodeExpanded ? 'min-h-[400px]' : 'min-h-[200px]'
                      }`}
                      placeholder="Write your code here..."
                      spellCheck={false}
                    />
                  </div>

                  {/* Output Panel */}
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-400">Output</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setCodeOutput('')}
                        className="text-xs text-gray-400 hover:text-white"
                      >
                        Clear
                      </Button>
                    </div>
                    <div className={`bg-black text-green-400 font-mono text-xs p-3 rounded-lg border border-gray-600 overflow-auto ${
                      isCodeExpanded ? 'max-h-32' : 'max-h-24'
                    }`}>
                      <pre className="whitespace-pre-wrap">
                        {codeOutput || 'No output yet. Click "Run Code" to execute your code.'}
                      </pre>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center text-gray-400">
                    <Code className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p className="text-sm">No coding required for this question</p>
                    <p className="text-xs mt-2">Focus on your verbal response</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
