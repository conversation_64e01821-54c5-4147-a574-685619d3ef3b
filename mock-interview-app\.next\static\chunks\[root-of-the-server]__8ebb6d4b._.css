/* [next]/internal/font/google/geist_e531dabc.module.css [app-client] (css) */
@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/gyByhwUxId8gMEwYGFWNOITddY4-s.b7d310ad.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/gyByhwUxId8gMEwSGFWNOITddY4-s.81df3a5b.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/gyByhwUxId8gMEwcGFWNOITd-s.p.da1ebef7.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Geist Fallback;
  src: local(Arial);
  ascent-override: 95.94%;
  descent-override: 28.16%;
  line-gap-override: 0.0%;
  size-adjust: 104.76%;
}

.geist_e531dabc-module__QGiZLq__className {
  font-family: Geist, Geist Fallback;
  font-style: normal;
}

.geist_e531dabc-module__QGiZLq__variable {
  --font-geist-sans: "Geist", "Geist Fallback";
}


/* [next]/internal/font/google/geist_mono_68a01160.module.css [app-client] (css) */
@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/or3nQ6H_1_WfwkMZI_qYFrMdmhHkjkotbA-s.cb6bbcb1.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/or3nQ6H_1_WfwkMZI_qYFrkdmhHkjkotbA-s.e32db976.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/or3nQ6H_1_WfwkMZI_qYFrcdmhHkjko-s.p.be19f591.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Geist Mono Fallback;
  src: local(Arial);
  ascent-override: 74.67%;
  descent-override: 21.92%;
  line-gap-override: 0.0%;
  size-adjust: 134.59%;
}

.geist_mono_68a01160-module__YLcDdW__className {
  font-family: Geist Mono, Geist Mono Fallback;
  font-style: normal;
}

.geist_mono_68a01160-module__YLcDdW__variable {
  --font-geist-mono: "Geist Mono", "Geist Mono Fallback";
}


/* [project]/src/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-duration: initial;
      --tw-animation-delay: 0s;
      --tw-animation-direction: normal;
      --tw-animation-duration: initial;
      --tw-animation-fill-mode: none;
      --tw-animation-iteration-count: 1;
      --tw-enter-opacity: 1;
      --tw-enter-rotate: 0;
      --tw-enter-scale: 1;
      --tw-enter-translate-x: 0;
      --tw-enter-translate-y: 0;
      --tw-exit-opacity: 1;
      --tw-exit-rotate: 0;
      --tw-exit-scale: 1;
      --tw-exit-translate-x: 0;
      --tw-exit-translate-y: 0;
    }
  }
}

@layer theme {
  :root, :host {
    --color-red-50: oklch(97.1% .013 17.38);
    --color-red-100: oklch(93.6% .032 17.717);
    --color-red-200: oklch(88.5% .062 18.334);
    --color-red-400: oklch(70.4% .191 22.216);
    --color-red-500: oklch(63.7% .237 25.331);
    --color-red-600: oklch(57.7% .245 27.325);
    --color-red-700: oklch(50.5% .213 27.518);
    --color-red-800: oklch(44.4% .177 26.899);
    --color-orange-100: oklch(95.4% .038 75.164);
    --color-orange-400: oklch(75% .183 55.934);
    --color-orange-500: oklch(70.5% .213 47.604);
    --color-orange-600: oklch(64.6% .222 41.116);
    --color-orange-700: oklch(55.3% .195 38.402);
    --color-orange-900: oklch(40.8% .123 38.172);
    --color-yellow-50: oklch(98.7% .026 102.212);
    --color-yellow-100: oklch(97.3% .071 103.193);
    --color-yellow-200: oklch(94.5% .129 101.54);
    --color-yellow-400: oklch(85.2% .199 91.936);
    --color-yellow-500: oklch(79.5% .184 86.047);
    --color-yellow-600: oklch(68.1% .162 75.834);
    --color-yellow-800: oklch(47.6% .114 61.907);
    --color-green-50: oklch(98.2% .018 155.826);
    --color-green-100: oklch(96.2% .044 156.743);
    --color-green-200: oklch(92.5% .084 155.995);
    --color-green-300: oklch(87.1% .15 154.449);
    --color-green-400: oklch(79.2% .209 151.711);
    --color-green-500: oklch(72.3% .219 149.579);
    --color-green-600: oklch(62.7% .194 149.214);
    --color-green-700: oklch(52.7% .154 150.069);
    --color-green-800: oklch(44.8% .119 151.328);
    --color-green-900: oklch(39.3% .095 152.535);
    --color-blue-50: oklch(97% .014 254.604);
    --color-blue-100: oklch(93.2% .032 255.585);
    --color-blue-200: oklch(88.2% .059 254.128);
    --color-blue-300: oklch(80.9% .105 251.813);
    --color-blue-400: oklch(70.7% .165 254.624);
    --color-blue-500: oklch(62.3% .214 259.815);
    --color-blue-600: oklch(54.6% .245 262.881);
    --color-blue-700: oklch(48.8% .243 264.376);
    --color-blue-800: oklch(42.4% .199 265.638);
    --color-blue-900: oklch(37.9% .146 265.522);
    --color-indigo-100: oklch(93% .034 272.788);
    --color-purple-100: oklch(94.6% .033 307.174);
    --color-purple-400: oklch(71.4% .203 305.504);
    --color-purple-500: oklch(62.7% .265 303.9);
    --color-purple-600: oklch(55.8% .288 302.321);
    --color-purple-700: oklch(49.6% .265 301.924);
    --color-purple-800: oklch(43.8% .218 303.724);
    --color-purple-900: oklch(38.1% .176 304.987);
    --color-gray-50: oklch(98.5% .002 247.839);
    --color-gray-100: oklch(96.7% .003 264.542);
    --color-gray-300: oklch(87.2% .01 258.338);
    --color-gray-400: oklch(70.7% .022 261.325);
    --color-gray-500: oklch(55.1% .027 264.364);
    --color-gray-600: oklch(44.6% .03 256.802);
    --color-gray-700: oklch(37.3% .034 259.733);
    --color-gray-800: oklch(27.8% .033 256.848);
    --color-gray-900: oklch(21% .034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-2xl: 42rem;
    --container-3xl: 48rem;
    --container-4xl: 56rem;
    --container-7xl: 80rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --tracking-widest: .1em;
    --leading-relaxed: 1.625;
    --radius-xs: .125rem;
    --animate-spin: spin 1s linear infinite;
    --animate-pulse: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite;
    --animate-bounce: bounce 1s infinite;
    --aspect-video: 16 / 9;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-geist-sans);
    --default-mono-font-family: var(--font-geist-mono);
  }
}

@layer base {
  *, :after, :before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }

  ::file-selector-button {
    margin-inline-end: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }

  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }

  * {
    border-color: var(--border);
    outline-color: var(--ring);
  }

  @supports (color: color-mix(in lab, red, red)) {
    * {
      outline-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }

  body {
    background-color: var(--background);
    color: var(--foreground);
  }
}

@layer components;

@layer utilities {
  .\@container\/card-header {
    container: card-header / inline-size;
  }

  .pointer-events-none {
    pointer-events: none;
  }

  .sr-only {
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    position: absolute;
    overflow: hidden;
  }

  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .static {
    position: static;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }

  .-top-1 {
    top: calc(var(--spacing) * -1);
  }

  .-top-2 {
    top: calc(var(--spacing) * -2);
  }

  .-top-4 {
    top: calc(var(--spacing) * -4);
  }

  .top-0 {
    top: calc(var(--spacing) * 0);
  }

  .top-1\/2 {
    top: 50%;
  }

  .top-2 {
    top: calc(var(--spacing) * 2);
  }

  .top-4 {
    top: calc(var(--spacing) * 4);
  }

  .top-6 {
    top: calc(var(--spacing) * 6);
  }

  .top-8 {
    top: calc(var(--spacing) * 8);
  }

  .top-\[1px\] {
    top: 1px;
  }

  .top-\[50\%\] {
    top: 50%;
  }

  .top-\[60\%\] {
    top: 60%;
  }

  .top-full {
    top: 100%;
  }

  .right-0 {
    right: calc(var(--spacing) * 0);
  }

  .right-2 {
    right: calc(var(--spacing) * 2);
  }

  .right-4 {
    right: calc(var(--spacing) * 4);
  }

  .right-6 {
    right: calc(var(--spacing) * 6);
  }

  .right-8 {
    right: calc(var(--spacing) * 8);
  }

  .-bottom-2 {
    bottom: calc(var(--spacing) * -2);
  }

  .-bottom-4 {
    bottom: calc(var(--spacing) * -4);
  }

  .bottom-6 {
    bottom: calc(var(--spacing) * 6);
  }

  .bottom-8 {
    bottom: calc(var(--spacing) * 8);
  }

  .left-0 {
    left: calc(var(--spacing) * 0);
  }

  .left-1\/2 {
    left: 50%;
  }

  .left-2 {
    left: calc(var(--spacing) * 2);
  }

  .left-3 {
    left: calc(var(--spacing) * 3);
  }

  .left-4 {
    left: calc(var(--spacing) * 4);
  }

  .left-6 {
    left: calc(var(--spacing) * 6);
  }

  .left-8 {
    left: calc(var(--spacing) * 8);
  }

  .left-\[50\%\] {
    left: 50%;
  }

  .isolate {
    isolation: isolate;
  }

  .z-50 {
    z-index: 50;
  }

  .z-\[1\] {
    z-index: 1;
  }

  .col-start-2 {
    grid-column-start: 2;
  }

  .row-span-2 {
    grid-row: span 2 / span 2;
  }

  .row-start-1 {
    grid-row-start: 1;
  }

  .-mx-1 {
    margin-inline: calc(var(--spacing) * -1);
  }

  .mx-auto {
    margin-inline: auto;
  }

  .my-1 {
    margin-block: calc(var(--spacing) * 1);
  }

  .mt-0\.5 {
    margin-top: calc(var(--spacing) * .5);
  }

  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }

  .mt-1\.5 {
    margin-top: calc(var(--spacing) * 1.5);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }

  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }

  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }

  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }

  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }

  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }

  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }

  .mb-16 {
    margin-bottom: calc(var(--spacing) * 16);
  }

  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }

  .ml-6 {
    margin-left: calc(var(--spacing) * 6);
  }

  .ml-auto {
    margin-left: auto;
  }

  .block {
    display: block;
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .inline-flex {
    display: inline-flex;
  }

  .table {
    display: table;
  }

  .table-caption {
    display: table-caption;
  }

  .table-cell {
    display: table-cell;
  }

  .table-row {
    display: table-row;
  }

  .field-sizing-content {
    field-sizing: content;
  }

  .aspect-square {
    aspect-ratio: 1;
  }

  .aspect-video {
    aspect-ratio: var(--aspect-video);
  }

  .size-2 {
    width: calc(var(--spacing) * 2);
    height: calc(var(--spacing) * 2);
  }

  .size-3 {
    width: calc(var(--spacing) * 3);
    height: calc(var(--spacing) * 3);
  }

  .size-3\.5 {
    width: calc(var(--spacing) * 3.5);
    height: calc(var(--spacing) * 3.5);
  }

  .size-4 {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .size-8 {
    width: calc(var(--spacing) * 8);
    height: calc(var(--spacing) * 8);
  }

  .size-9 {
    width: calc(var(--spacing) * 9);
    height: calc(var(--spacing) * 9);
  }

  .size-full {
    width: 100%;
    height: 100%;
  }

  .h-0 {
    height: calc(var(--spacing) * 0);
  }

  .h-1 {
    height: calc(var(--spacing) * 1);
  }

  .h-1\.5 {
    height: calc(var(--spacing) * 1.5);
  }

  .h-2 {
    height: calc(var(--spacing) * 2);
  }

  .h-3 {
    height: calc(var(--spacing) * 3);
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-5 {
    height: calc(var(--spacing) * 5);
  }

  .h-6 {
    height: calc(var(--spacing) * 6);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-9 {
    height: calc(var(--spacing) * 9);
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .h-12 {
    height: calc(var(--spacing) * 12);
  }

  .h-16 {
    height: calc(var(--spacing) * 16);
  }

  .h-20 {
    height: calc(var(--spacing) * 20);
  }

  .h-24 {
    height: calc(var(--spacing) * 24);
  }

  .h-32 {
    height: calc(var(--spacing) * 32);
  }

  .h-96 {
    height: calc(var(--spacing) * 96);
  }

  .h-\[calc\(100\%-1px\)\] {
    height: calc(100% - 1px);
  }

  .h-\[calc\(100vh-80px\)\] {
    height: calc(100vh - 80px);
  }

  .h-\[var\(--radix-navigation-menu-viewport-height\)\] {
    height: var(--radix-navigation-menu-viewport-height);
  }

  .h-\[var\(--radix-select-trigger-height\)\] {
    height: var(--radix-select-trigger-height);
  }

  .h-full {
    height: 100%;
  }

  .h-px {
    height: 1px;
  }

  .max-h-\(--radix-dropdown-menu-content-available-height\) {
    max-height: var(--radix-dropdown-menu-content-available-height);
  }

  .max-h-\(--radix-select-content-available-height\) {
    max-height: var(--radix-select-content-available-height);
  }

  .min-h-16 {
    min-height: calc(var(--spacing) * 16);
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .w-0 {
    width: calc(var(--spacing) * 0);
  }

  .w-1 {
    width: calc(var(--spacing) * 1);
  }

  .w-1\/3 {
    width: 33.3333%;
  }

  .w-2 {
    width: calc(var(--spacing) * 2);
  }

  .w-3 {
    width: calc(var(--spacing) * 3);
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-5 {
    width: calc(var(--spacing) * 5);
  }

  .w-6 {
    width: calc(var(--spacing) * 6);
  }

  .w-8 {
    width: calc(var(--spacing) * 8);
  }

  .w-12 {
    width: calc(var(--spacing) * 12);
  }

  .w-16 {
    width: calc(var(--spacing) * 16);
  }

  .w-20 {
    width: calc(var(--spacing) * 20);
  }

  .w-24 {
    width: calc(var(--spacing) * 24);
  }

  .w-32 {
    width: calc(var(--spacing) * 32);
  }

  .w-56 {
    width: calc(var(--spacing) * 56);
  }

  .w-80 {
    width: calc(var(--spacing) * 80);
  }

  .w-fit {
    width: fit-content;
  }

  .w-full {
    width: 100%;
  }

  .w-max {
    width: max-content;
  }

  .max-w-2xl {
    max-width: var(--container-2xl);
  }

  .max-w-3xl {
    max-width: var(--container-3xl);
  }

  .max-w-4xl {
    max-width: var(--container-4xl);
  }

  .max-w-7xl {
    max-width: var(--container-7xl);
  }

  .max-w-\[calc\(100\%-2rem\)\] {
    max-width: calc(100% - 2rem);
  }

  .max-w-max {
    max-width: max-content;
  }

  .max-w-md {
    max-width: var(--container-md);
  }

  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }

  .min-w-\[8rem\] {
    min-width: 8rem;
  }

  .min-w-\[var\(--radix-select-trigger-width\)\] {
    min-width: var(--radix-select-trigger-width);
  }

  .flex-1 {
    flex: 1;
  }

  .shrink-0 {
    flex-shrink: 0;
  }

  .caption-bottom {
    caption-side: bottom;
  }

  .origin-\(--radix-dropdown-menu-content-transform-origin\) {
    transform-origin: var(--radix-dropdown-menu-content-transform-origin);
  }

  .origin-\(--radix-select-content-transform-origin\) {
    transform-origin: var(--radix-select-content-transform-origin);
  }

  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-\[-50\%\] {
    --tw-translate-x: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-\[-50\%\] {
    --tw-translate-y: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .scale-100 {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .scale-110 {
    --tw-scale-x: 110%;
    --tw-scale-y: 110%;
    --tw-scale-z: 110%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .rotate-45 {
    rotate: 45deg;
  }

  .transform {
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .animate-bounce {
    animation: var(--animate-bounce);
  }

  .animate-pulse {
    animation: var(--animate-pulse);
  }

  .animate-spin {
    animation: var(--animate-spin);
  }

  .cursor-default {
    cursor: default;
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .resize-none {
    resize: none;
  }

  .scroll-my-1 {
    scroll-margin-block: calc(var(--spacing) * 1);
  }

  .list-none {
    list-style-type: none;
  }

  .auto-rows-min {
    grid-auto-rows: min-content;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .grid-rows-\[auto_auto\] {
    grid-template-rows: auto auto;
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-col-reverse {
    flex-direction: column-reverse;
  }

  .flex-row {
    flex-direction: row;
  }

  .items-center {
    align-items: center;
  }

  .items-end {
    align-items: flex-end;
  }

  .items-start {
    align-items: flex-start;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .justify-end {
    justify-content: flex-end;
  }

  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }

  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }

  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }

  :where(.space-y-0 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-1 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-3 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-4 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-6 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-8 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-6 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
  }

  .self-start {
    align-self: flex-start;
  }

  .justify-self-end {
    justify-self: flex-end;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .overflow-x-auto {
    overflow-x: auto;
  }

  .overflow-x-hidden {
    overflow-x: hidden;
  }

  .overflow-y-auto {
    overflow-y: auto;
  }

  .rounded {
    border-radius: .25rem;
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius);
  }

  .rounded-md {
    border-radius: calc(var(--radius)  - 2px);
  }

  .rounded-sm {
    border-radius: calc(var(--radius)  - 4px);
  }

  .rounded-xl {
    border-radius: calc(var(--radius)  + 4px);
  }

  .rounded-xs {
    border-radius: var(--radius-xs);
  }

  .rounded-tl-sm {
    border-top-left-radius: calc(var(--radius)  - 4px);
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }

  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .border-r-2 {
    border-right-style: var(--tw-border-style);
    border-right-width: 2px;
  }

  .border-r-4 {
    border-right-style: var(--tw-border-style);
    border-right-width: 4px;
  }

  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }

  .border-b-4 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 4px;
  }

  .border-l-2 {
    border-left-style: var(--tw-border-style);
    border-left-width: 2px;
  }

  .border-l-4 {
    border-left-style: var(--tw-border-style);
    border-left-width: 4px;
  }

  .border-blue-200 {
    border-color: var(--color-blue-200);
  }

  .border-blue-500 {
    border-color: var(--color-blue-500);
  }

  .border-blue-600 {
    border-color: var(--color-blue-600);
  }

  .border-blue-700 {
    border-color: var(--color-blue-700);
  }

  .border-gray-500 {
    border-color: var(--color-gray-500);
  }

  .border-gray-600 {
    border-color: var(--color-gray-600);
  }

  .border-gray-700 {
    border-color: var(--color-gray-700);
  }

  .border-gray-800 {
    border-color: var(--color-gray-800);
  }

  .border-green-200 {
    border-color: var(--color-green-200);
  }

  .border-green-300 {
    border-color: var(--color-green-300);
  }

  .border-green-600 {
    border-color: var(--color-green-600);
  }

  .border-green-700 {
    border-color: var(--color-green-700);
  }

  .border-input {
    border-color: var(--input);
  }

  .border-orange-700 {
    border-color: var(--color-orange-700);
  }

  .border-red-200 {
    border-color: var(--color-red-200);
  }

  .border-red-600 {
    border-color: var(--color-red-600);
  }

  .border-transparent {
    border-color: #0000;
  }

  .border-yellow-200 {
    border-color: var(--color-yellow-200);
  }

  .border-yellow-600 {
    border-color: var(--color-yellow-600);
  }

  .border-b-blue-800 {
    border-bottom-color: var(--color-blue-800);
  }

  .border-b-blue-900 {
    border-bottom-color: var(--color-blue-900);
  }

  .bg-background {
    background-color: var(--background);
  }

  .bg-black\/50 {
    background-color: #00000080;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/50 {
      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
    }
  }

  .bg-blue-50 {
    background-color: var(--color-blue-50);
  }

  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }

  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }

  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }

  .bg-blue-800 {
    background-color: var(--color-blue-800);
  }

  .bg-blue-900 {
    background-color: var(--color-blue-900);
  }

  .bg-border {
    background-color: var(--border);
  }

  .bg-card {
    background-color: var(--card);
  }

  .bg-destructive {
    background-color: var(--destructive);
  }

  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }

  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }

  .bg-gray-400 {
    background-color: var(--color-gray-400);
  }

  .bg-gray-700 {
    background-color: var(--color-gray-700);
  }

  .bg-gray-800 {
    background-color: var(--color-gray-800);
  }

  .bg-gray-900 {
    background-color: var(--color-gray-900);
  }

  .bg-green-50 {
    background-color: var(--color-green-50);
  }

  .bg-green-100 {
    background-color: var(--color-green-100);
  }

  .bg-green-400 {
    background-color: var(--color-green-400);
  }

  .bg-green-500 {
    background-color: var(--color-green-500);
  }

  .bg-green-600 {
    background-color: var(--color-green-600);
  }

  .bg-green-900 {
    background-color: var(--color-green-900);
  }

  .bg-muted {
    background-color: var(--muted);
  }

  .bg-muted\/50 {
    background-color: var(--muted);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-muted\/50 {
      background-color: color-mix(in oklab, var(--muted) 50%, transparent);
    }
  }

  .bg-orange-500 {
    background-color: var(--color-orange-500);
  }

  .bg-orange-600 {
    background-color: var(--color-orange-600);
  }

  .bg-orange-900 {
    background-color: var(--color-orange-900);
  }

  .bg-popover {
    background-color: var(--popover);
  }

  .bg-primary {
    background-color: var(--primary);
  }

  .bg-purple-100 {
    background-color: var(--color-purple-100);
  }

  .bg-purple-600 {
    background-color: var(--color-purple-600);
  }

  .bg-red-50 {
    background-color: var(--color-red-50);
  }

  .bg-red-100 {
    background-color: var(--color-red-100);
  }

  .bg-red-500 {
    background-color: var(--color-red-500);
  }

  .bg-red-600 {
    background-color: var(--color-red-600);
  }

  .bg-secondary {
    background-color: var(--secondary);
  }

  .bg-transparent {
    background-color: #0000;
  }

  .bg-white {
    background-color: var(--color-white);
  }

  .bg-yellow-50 {
    background-color: var(--color-yellow-50);
  }

  .bg-yellow-100 {
    background-color: var(--color-yellow-100);
  }

  .bg-yellow-500 {
    background-color: var(--color-yellow-500);
  }

  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .from-blue-50 {
    --tw-gradient-from: var(--color-blue-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-blue-300 {
    --tw-gradient-from: var(--color-blue-300);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-blue-400 {
    --tw-gradient-from: var(--color-blue-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-blue-600 {
    --tw-gradient-from: var(--color-blue-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-blue-900 {
    --tw-gradient-from: var(--color-blue-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-green-400 {
    --tw-gradient-from: var(--color-green-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-green-600 {
    --tw-gradient-from: var(--color-green-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-blue-500 {
    --tw-gradient-to: var(--color-blue-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-blue-600 {
    --tw-gradient-to: var(--color-blue-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-indigo-100 {
    --tw-gradient-to: var(--color-indigo-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-purple-400 {
    --tw-gradient-to: var(--color-purple-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-purple-500 {
    --tw-gradient-to: var(--color-purple-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-purple-600 {
    --tw-gradient-to: var(--color-purple-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-purple-900 {
    --tw-gradient-to: var(--color-purple-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .fill-current {
    fill: currentColor;
  }

  .object-cover {
    object-fit: cover;
  }

  .p-1 {
    padding: calc(var(--spacing) * 1);
  }

  .p-2 {
    padding: calc(var(--spacing) * 2);
  }

  .p-3 {
    padding: calc(var(--spacing) * 3);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .p-12 {
    padding: calc(var(--spacing) * 12);
  }

  .p-\[3px\] {
    padding: 3px;
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }

  .py-0\.5 {
    padding-block: calc(var(--spacing) * .5);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }

  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }

  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }

  .py-20 {
    padding-block: calc(var(--spacing) * 20);
  }

  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }

  .pt-8 {
    padding-top: calc(var(--spacing) * 8);
  }

  .pr-2 {
    padding-right: calc(var(--spacing) * 2);
  }

  .pr-2\.5 {
    padding-right: calc(var(--spacing) * 2.5);
  }

  .pr-3 {
    padding-right: calc(var(--spacing) * 3);
  }

  .pr-8 {
    padding-right: calc(var(--spacing) * 8);
  }

  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }

  .pl-2 {
    padding-left: calc(var(--spacing) * 2);
  }

  .pl-8 {
    padding-left: calc(var(--spacing) * 8);
  }

  .pl-10 {
    padding-left: calc(var(--spacing) * 10);
  }

  .text-center {
    text-align: center;
  }

  .text-left {
    text-align: left;
  }

  .text-right {
    text-align: right;
  }

  .align-middle {
    vertical-align: middle;
  }

  .font-mono {
    font-family: var(--font-geist-mono);
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }

  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }

  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }

  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-extrabold {
    --tw-font-weight: var(--font-weight-extrabold);
    font-weight: var(--font-weight-extrabold);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .tracking-widest {
    --tw-tracking: var(--tracking-widest);
    letter-spacing: var(--tracking-widest);
  }

  .whitespace-nowrap {
    white-space: nowrap;
  }

  .text-blue-100 {
    color: var(--color-blue-100);
  }

  .text-blue-200 {
    color: var(--color-blue-200);
  }

  .text-blue-400 {
    color: var(--color-blue-400);
  }

  .text-blue-500 {
    color: var(--color-blue-500);
  }

  .text-blue-600 {
    color: var(--color-blue-600);
  }

  .text-blue-800 {
    color: var(--color-blue-800);
  }

  .text-card-foreground {
    color: var(--card-foreground);
  }

  .text-destructive {
    color: var(--destructive);
  }

  .text-foreground {
    color: var(--foreground);
  }

  .text-gray-300 {
    color: var(--color-gray-300);
  }

  .text-gray-400 {
    color: var(--color-gray-400);
  }

  .text-gray-500 {
    color: var(--color-gray-500);
  }

  .text-gray-600 {
    color: var(--color-gray-600);
  }

  .text-gray-700 {
    color: var(--color-gray-700);
  }

  .text-gray-800 {
    color: var(--color-gray-800);
  }

  .text-gray-900 {
    color: var(--color-gray-900);
  }

  .text-green-100 {
    color: var(--color-green-100);
  }

  .text-green-200 {
    color: var(--color-green-200);
  }

  .text-green-400 {
    color: var(--color-green-400);
  }

  .text-green-500 {
    color: var(--color-green-500);
  }

  .text-green-600 {
    color: var(--color-green-600);
  }

  .text-green-700 {
    color: var(--color-green-700);
  }

  .text-green-800 {
    color: var(--color-green-800);
  }

  .text-muted-foreground {
    color: var(--muted-foreground);
  }

  .text-orange-100 {
    color: var(--color-orange-100);
  }

  .text-orange-400 {
    color: var(--color-orange-400);
  }

  .text-orange-500 {
    color: var(--color-orange-500);
  }

  .text-orange-600 {
    color: var(--color-orange-600);
  }

  .text-orange-700 {
    color: var(--color-orange-700);
  }

  .text-popover-foreground {
    color: var(--popover-foreground);
  }

  .text-primary {
    color: var(--primary);
  }

  .text-primary-foreground {
    color: var(--primary-foreground);
  }

  .text-purple-600 {
    color: var(--color-purple-600);
  }

  .text-purple-800 {
    color: var(--color-purple-800);
  }

  .text-red-400 {
    color: var(--color-red-400);
  }

  .text-red-500 {
    color: var(--color-red-500);
  }

  .text-red-600 {
    color: var(--color-red-600);
  }

  .text-red-800 {
    color: var(--color-red-800);
  }

  .text-secondary-foreground {
    color: var(--secondary-foreground);
  }

  .text-white {
    color: var(--color-white);
  }

  .text-yellow-400 {
    color: var(--color-yellow-400);
  }

  .text-yellow-500 {
    color: var(--color-yellow-500);
  }

  .text-yellow-600 {
    color: var(--color-yellow-600);
  }

  .text-yellow-800 {
    color: var(--color-yellow-800);
  }

  .capitalize {
    text-transform: capitalize;
  }

  .underline-offset-4 {
    text-underline-offset: 4px;
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .opacity-20 {
    opacity: .2;
  }

  .opacity-50 {
    opacity: .5;
  }

  .opacity-70 {
    opacity: .7;
  }

  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xs {
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, #0000000d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-blue-500\/50 {
    --tw-shadow-color: #3080ff80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-blue-500\/50 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-blue-500) 50%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .ring-offset-background {
    --tw-ring-offset-color: var(--background);
  }

  .outline-hidden {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .outline-hidden {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[color\,box-shadow\] {
    transition-property: color, box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .duration-100 {
    --tw-duration: .1s;
    transition-duration: .1s;
  }

  .duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .duration-500 {
    --tw-duration: .5s;
    transition-duration: .5s;
  }

  .duration-1000 {
    --tw-duration: 1s;
    transition-duration: 1s;
  }

  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }

  .select-none {
    -webkit-user-select: none;
    user-select: none;
  }

  .running {
    animation-play-state: running;
  }

  .group-data-\[disabled\=true\]\:pointer-events-none:is(:where(.group)[data-disabled="true"] *) {
    pointer-events: none;
  }

  .group-data-\[disabled\=true\]\:opacity-50:is(:where(.group)[data-disabled="true"] *) {
    opacity: .5;
  }

  .group-data-\[state\=open\]\:rotate-180:is(:where(.group)[data-state="open"] *) {
    rotate: 180deg;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:top-full:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    top: 100%;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:mt-1\.5:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    margin-top: calc(var(--spacing) * 1.5);
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:overflow-hidden:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    overflow: hidden;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:rounded-md:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    border-radius: calc(var(--radius)  - 2px);
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:border:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:bg-popover:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    background-color: var(--popover);
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:text-popover-foreground:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    color: var(--popover-foreground);
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:shadow:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:duration-200:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .peer-disabled\:cursor-not-allowed:is(:where(.peer):disabled ~ *) {
    cursor: not-allowed;
  }

  .peer-disabled\:opacity-50:is(:where(.peer):disabled ~ *) {
    opacity: .5;
  }

  .selection\:bg-primary ::selection, .selection\:bg-primary::selection {
    background-color: var(--primary);
  }

  .selection\:text-primary-foreground ::selection, .selection\:text-primary-foreground::selection {
    color: var(--primary-foreground);
  }

  .file\:inline-flex::file-selector-button {
    display: inline-flex;
  }

  .file\:h-7::file-selector-button {
    height: calc(var(--spacing) * 7);
  }

  .file\:border-0::file-selector-button {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .file\:bg-transparent::file-selector-button {
    background-color: #0000;
  }

  .file\:text-sm::file-selector-button {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .file\:font-medium::file-selector-button {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .file\:text-foreground::file-selector-button {
    color: var(--foreground);
  }

  .placeholder\:text-muted-foreground::placeholder {
    color: var(--muted-foreground);
  }

  @media (hover: hover) {
    .hover\:bg-accent:hover {
      background-color: var(--accent);
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-700:hover {
      background-color: var(--color-blue-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-destructive\/80:hover {
      background-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-destructive\/80:hover {
        background-color: color-mix(in oklab, var(--destructive) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-destructive\/90:hover {
      background-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-destructive\/90:hover {
        background-color: color-mix(in oklab, var(--destructive) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-green-100:hover {
      background-color: var(--color-green-100);
    }
  }

  @media (hover: hover) {
    .hover\:bg-green-700:hover {
      background-color: var(--color-green-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-muted\/50:hover {
      background-color: var(--muted);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-muted\/50:hover {
        background-color: color-mix(in oklab, var(--muted) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-orange-700:hover {
      background-color: var(--color-orange-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary\/80:hover {
      background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-primary\/80:hover {
        background-color: color-mix(in oklab, var(--primary) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary\/90:hover {
      background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-primary\/90:hover {
        background-color: color-mix(in oklab, var(--primary) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-700:hover {
      background-color: var(--color-red-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-secondary\/80:hover {
      background-color: var(--secondary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-secondary\/80:hover {
        background-color: color-mix(in oklab, var(--secondary) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:from-blue-700:hover {
      --tw-gradient-from: var(--color-blue-700);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:to-purple-700:hover {
      --tw-gradient-to: var(--color-purple-700);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:text-accent-foreground:hover {
      color: var(--accent-foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-blue-300:hover {
      color: var(--color-blue-300);
    }
  }

  @media (hover: hover) {
    .hover\:text-blue-500:hover {
      color: var(--color-blue-500);
    }
  }

  @media (hover: hover) {
    .hover\:text-blue-800:hover {
      color: var(--color-blue-800);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-700:hover {
      color: var(--color-gray-700);
    }
  }

  @media (hover: hover) {
    .hover\:text-green-500:hover {
      color: var(--color-green-500);
    }
  }

  @media (hover: hover) {
    .hover\:text-primary:hover {
      color: var(--primary);
    }
  }

  @media (hover: hover) {
    .hover\:text-white:hover {
      color: var(--color-white);
    }
  }

  @media (hover: hover) {
    .hover\:underline:hover {
      text-decoration-line: underline;
    }
  }

  @media (hover: hover) {
    .hover\:opacity-100:hover {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .hover\:shadow-lg:hover {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  .focus\:bg-accent:focus {
    background-color: var(--accent);
  }

  .focus\:text-accent-foreground:focus {
    color: var(--accent-foreground);
  }

  .focus\:ring-2:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-ring:focus {
    --tw-ring-color: var(--ring);
  }

  .focus\:ring-offset-2:focus {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus\:outline-hidden:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .focus\:outline-hidden:focus {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .focus\:outline-none:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  .focus-visible\:border-ring:focus-visible {
    border-color: var(--ring);
  }

  .focus-visible\:ring-\[3px\]:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-destructive\/20:focus-visible {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-destructive\/20:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  .focus-visible\:ring-ring\/50:focus-visible {
    --tw-ring-color: var(--ring);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-ring\/50:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }

  .focus-visible\:outline-1:focus-visible {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .focus-visible\:outline-ring:focus-visible {
    outline-color: var(--ring);
  }

  .disabled\:pointer-events-none:disabled {
    pointer-events: none;
  }

  .disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed;
  }

  .disabled\:opacity-50:disabled {
    opacity: .5;
  }

  .has-data-\[slot\=card-action\]\:grid-cols-\[1fr_auto\]:has([data-slot="card-action"]) {
    grid-template-columns: 1fr auto;
  }

  .has-\[\>svg\]\:px-2\.5:has( > svg) {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .has-\[\>svg\]\:px-3:has( > svg) {
    padding-inline: calc(var(--spacing) * 3);
  }

  .has-\[\>svg\]\:px-4:has( > svg) {
    padding-inline: calc(var(--spacing) * 4);
  }

  .aria-invalid\:border-destructive[aria-invalid="true"] {
    border-color: var(--destructive);
  }

  .aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  .data-\[active\=true\]\:bg-accent\/50[data-active="true"] {
    background-color: var(--accent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[active\=true\]\:bg-accent\/50[data-active="true"] {
      background-color: color-mix(in oklab, var(--accent) 50%, transparent);
    }
  }

  .data-\[active\=true\]\:text-accent-foreground[data-active="true"] {
    color: var(--accent-foreground);
  }

  @media (hover: hover) {
    .data-\[active\=true\]\:hover\:bg-accent[data-active="true"]:hover {
      background-color: var(--accent);
    }
  }

  .data-\[active\=true\]\:focus\:bg-accent[data-active="true"]:focus {
    background-color: var(--accent);
  }

  .data-\[disabled\]\:pointer-events-none[data-disabled] {
    pointer-events: none;
  }

  .data-\[disabled\]\:opacity-50[data-disabled] {
    opacity: .5;
  }

  .data-\[error\=true\]\:text-destructive[data-error="true"] {
    color: var(--destructive);
  }

  .data-\[inset\]\:pl-8[data-inset] {
    padding-left: calc(var(--spacing) * 8);
  }

  .data-\[motion\=from-end\]\:slide-in-from-right-52[data-motion="from-end"] {
    --tw-enter-translate-x: calc(52 * var(--spacing));
  }

  .data-\[motion\=from-start\]\:slide-in-from-left-52[data-motion="from-start"] {
    --tw-enter-translate-x: calc(52 * var(--spacing) * -1);
  }

  .data-\[motion\=to-end\]\:slide-out-to-right-52[data-motion="to-end"] {
    --tw-exit-translate-x: calc(52 * var(--spacing));
  }

  .data-\[motion\=to-start\]\:slide-out-to-left-52[data-motion="to-start"] {
    --tw-exit-translate-x: calc(52 * var(--spacing) * -1);
  }

  .data-\[motion\^\=from-\]\:animate-in[data-motion^="from-"] {
    animation: enter var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .data-\[motion\^\=from-\]\:fade-in[data-motion^="from-"] {
    --tw-enter-opacity: 0;
  }

  .data-\[motion\^\=to-\]\:animate-out[data-motion^="to-"] {
    animation: exit var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .data-\[motion\^\=to-\]\:fade-out[data-motion^="to-"] {
    --tw-exit-opacity: 0;
  }

  .data-\[placeholder\]\:text-muted-foreground[data-placeholder] {
    color: var(--muted-foreground);
  }

  .data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
    --tw-translate-y: calc(var(--spacing) * 1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
    --tw-enter-translate-y: calc(2 * var(--spacing) * -1);
  }

  .data-\[side\=left\]\:-translate-x-1[data-side="left"] {
    --tw-translate-x: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
    --tw-enter-translate-x: calc(2 * var(--spacing));
  }

  .data-\[side\=right\]\:translate-x-1[data-side="right"] {
    --tw-translate-x: calc(var(--spacing) * 1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
    --tw-enter-translate-x: calc(2 * var(--spacing) * -1);
  }

  .data-\[side\=top\]\:-translate-y-1[data-side="top"] {
    --tw-translate-y: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
    --tw-enter-translate-y: calc(2 * var(--spacing));
  }

  .data-\[size\=default\]\:h-9[data-size="default"] {
    height: calc(var(--spacing) * 9);
  }

  .data-\[size\=sm\]\:h-8[data-size="sm"] {
    height: calc(var(--spacing) * 8);
  }

  :is(.\*\*\:data-\[slot\=navigation-menu-link\]\:focus\:ring-0 *)[data-slot="navigation-menu-link"]:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  :is(.\*\*\:data-\[slot\=navigation-menu-link\]\:focus\:outline-none *)[data-slot="navigation-menu-link"]:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  :is(.\*\:data-\[slot\=select-value\]\:line-clamp-1 > *)[data-slot="select-value"] {
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  :is(.\*\:data-\[slot\=select-value\]\:flex > *)[data-slot="select-value"] {
    display: flex;
  }

  :is(.\*\:data-\[slot\=select-value\]\:items-center > *)[data-slot="select-value"] {
    align-items: center;
  }

  :is(.\*\:data-\[slot\=select-value\]\:gap-2 > *)[data-slot="select-value"] {
    gap: calc(var(--spacing) * 2);
  }

  .data-\[state\=active\]\:bg-background[data-state="active"] {
    background-color: var(--background);
  }

  .data-\[state\=active\]\:shadow-sm[data-state="active"] {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .data-\[state\=closed\]\:animate-out[data-state="closed"] {
    animation: exit var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
    --tw-exit-opacity: 0;
  }

  .data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
    --tw-exit-scale: .95;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:animate-out:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="closed"] {
    animation: exit var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:fade-out-0:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="closed"] {
    --tw-exit-opacity: 0;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:zoom-out-95:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="closed"] {
    --tw-exit-scale: .95;
  }

  .data-\[state\=hidden\]\:animate-out[data-state="hidden"] {
    animation: exit var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .data-\[state\=hidden\]\:fade-out[data-state="hidden"] {
    --tw-exit-opacity: 0;
  }

  .data-\[state\=open\]\:animate-in[data-state="open"] {
    animation: enter var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .data-\[state\=open\]\:bg-accent[data-state="open"] {
    background-color: var(--accent);
  }

  .data-\[state\=open\]\:bg-accent\/50[data-state="open"] {
    background-color: var(--accent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[state\=open\]\:bg-accent\/50[data-state="open"] {
      background-color: color-mix(in oklab, var(--accent) 50%, transparent);
    }
  }

  .data-\[state\=open\]\:text-accent-foreground[data-state="open"] {
    color: var(--accent-foreground);
  }

  .data-\[state\=open\]\:text-muted-foreground[data-state="open"] {
    color: var(--muted-foreground);
  }

  .data-\[state\=open\]\:fade-in-0[data-state="open"] {
    --tw-enter-opacity: 0;
  }

  .data-\[state\=open\]\:zoom-in-90[data-state="open"] {
    --tw-enter-scale: .9;
  }

  .data-\[state\=open\]\:zoom-in-95[data-state="open"] {
    --tw-enter-scale: .95;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:animate-in:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="open"] {
    animation: enter var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:fade-in-0:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="open"] {
    --tw-enter-opacity: 0;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:zoom-in-95:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="open"] {
    --tw-enter-scale: .95;
  }

  @media (hover: hover) {
    .data-\[state\=open\]\:hover\:bg-accent[data-state="open"]:hover {
      background-color: var(--accent);
    }
  }

  .data-\[state\=open\]\:focus\:bg-accent[data-state="open"]:focus {
    background-color: var(--accent);
  }

  .data-\[state\=selected\]\:bg-muted[data-state="selected"] {
    background-color: var(--muted);
  }

  .data-\[state\=visible\]\:animate-in[data-state="visible"] {
    animation: enter var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .data-\[state\=visible\]\:fade-in[data-state="visible"] {
    --tw-enter-opacity: 0;
  }

  .data-\[variant\=destructive\]\:text-destructive[data-variant="destructive"] {
    color: var(--destructive);
  }

  .data-\[variant\=destructive\]\:focus\:bg-destructive\/10[data-variant="destructive"]:focus {
    background-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[variant\=destructive\]\:focus\:bg-destructive\/10[data-variant="destructive"]:focus {
      background-color: color-mix(in oklab, var(--destructive) 10%, transparent);
    }
  }

  .data-\[variant\=destructive\]\:focus\:text-destructive[data-variant="destructive"]:focus {
    color: var(--destructive);
  }

  @media (width >= 40rem) {
    .sm\:max-w-lg {
      max-width: var(--container-lg);
    }
  }

  @media (width >= 40rem) {
    .sm\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 40rem) {
    .sm\:justify-end {
      justify-content: flex-end;
    }
  }

  @media (width >= 40rem) {
    .sm\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:text-left {
      text-align: left;
    }
  }

  @media (width >= 48rem) {
    .md\:absolute {
      position: absolute;
    }
  }

  @media (width >= 48rem) {
    .md\:col-span-2 {
      grid-column: span 2 / span 2;
    }
  }

  @media (width >= 48rem) {
    .md\:w-\[var\(--radix-navigation-menu-viewport-width\)\] {
      width: var(--radix-navigation-menu-viewport-width);
    }
  }

  @media (width >= 48rem) {
    .md\:w-auto {
      width: auto;
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-1 {
      grid-template-columns: repeat(1, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 48rem) {
    .md\:text-4xl {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-6xl {
      font-size: var(--text-6xl);
      line-height: var(--tw-leading, var(--text-6xl--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-sm {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-5 {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }
  }

  .dark\:border-input:is(.dark *) {
    border-color: var(--input);
  }

  .dark\:bg-destructive\/60:is(.dark *) {
    background-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-destructive\/60:is(.dark *) {
      background-color: color-mix(in oklab, var(--destructive) 60%, transparent);
    }
  }

  .dark\:bg-input\/30:is(.dark *) {
    background-color: var(--input);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-input\/30:is(.dark *) {
      background-color: color-mix(in oklab, var(--input) 30%, transparent);
    }
  }

  .dark\:text-muted-foreground:is(.dark *) {
    color: var(--muted-foreground);
  }

  @media (hover: hover) {
    .dark\:hover\:bg-accent\/50:is(.dark *):hover {
      background-color: var(--accent);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-accent\/50:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--accent) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-input\/50:is(.dark *):hover {
      background-color: var(--input);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-input\/50:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--input) 50%, transparent);
      }
    }
  }

  .dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
    }
  }

  .dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
    }
  }

  .dark\:data-\[state\=active\]\:border-input:is(.dark *)[data-state="active"] {
    border-color: var(--input);
  }

  .dark\:data-\[state\=active\]\:bg-input\/30:is(.dark *)[data-state="active"] {
    background-color: var(--input);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:data-\[state\=active\]\:bg-input\/30:is(.dark *)[data-state="active"] {
      background-color: color-mix(in oklab, var(--input) 30%, transparent);
    }
  }

  .dark\:data-\[state\=active\]\:text-foreground:is(.dark *)[data-state="active"] {
    color: var(--foreground);
  }

  .dark\:data-\[variant\=destructive\]\:focus\:bg-destructive\/20:is(.dark *)[data-variant="destructive"]:focus {
    background-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:data-\[variant\=destructive\]\:focus\:bg-destructive\/20:is(.dark *)[data-variant="destructive"]:focus {
      background-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  .\[\&_svg\]\:pointer-events-none svg {
    pointer-events: none;
  }

  .\[\&_svg\]\:shrink-0 svg {
    flex-shrink: 0;
  }

  .\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-4 svg:not([class*="size-"]) {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .\[\&_svg\:not\(\[class\*\=\'text-\'\]\)\]\:text-muted-foreground svg:not([class*="text-"]) {
    color: var(--muted-foreground);
  }

  .\[\&_tr\]\:border-b tr {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .\[\&_tr\:last-child\]\:border-0 tr:last-child {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role="checkbox"]) {
    padding-right: calc(var(--spacing) * 0);
  }

  .\[\.border-b\]\:pb-6.border-b {
    padding-bottom: calc(var(--spacing) * 6);
  }

  .\[\.border-t\]\:pt-6.border-t {
    padding-top: calc(var(--spacing) * 6);
  }

  :is(.\*\:\[span\]\:last\:flex > *):is(span):last-child {
    display: flex;
  }

  :is(.\*\:\[span\]\:last\:items-center > *):is(span):last-child {
    align-items: center;
  }

  :is(.\*\:\[span\]\:last\:gap-2 > *):is(span):last-child {
    gap: calc(var(--spacing) * 2);
  }

  :is(.data-\[variant\=destructive\]\:\*\:\[svg\]\:\!text-destructive[data-variant="destructive"] > *):is(svg) {
    color: var(--destructive) !important;
  }

  .\[\&\>\[role\=checkbox\]\]\:translate-y-\[2px\] > [role="checkbox"] {
    --tw-translate-y: 2px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .\[\&\>tr\]\:last\:border-b-0 > tr:last-child {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 0;
  }
}

@property --tw-animation-delay {
  syntax: "*";
  inherits: false;
  initial-value: 0s;
}

@property --tw-animation-direction {
  syntax: "*";
  inherits: false;
  initial-value: normal;
}

@property --tw-animation-duration {
  syntax: "*";
  inherits: false
}

@property --tw-animation-fill-mode {
  syntax: "*";
  inherits: false;
  initial-value: none;
}

@property --tw-animation-iteration-count {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-opacity {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-rotate {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-enter-scale {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-enter-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-opacity {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-exit-rotate {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-scale {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-exit-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

:root {
  --radius: .625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(.145 0 0);
  --primary: oklch(.205 0 0);
  --primary-foreground: oklch(.985 0 0);
  --secondary: oklch(.97 0 0);
  --secondary-foreground: oklch(.205 0 0);
  --muted: oklch(.97 0 0);
  --muted-foreground: oklch(.556 0 0);
  --accent: oklch(.97 0 0);
  --accent-foreground: oklch(.205 0 0);
  --destructive: oklch(.577 .245 27.325);
  --border: oklch(.922 0 0);
  --input: oklch(.922 0 0);
  --ring: oklch(.708 0 0);
  --chart-1: oklch(.646 .222 41.116);
  --chart-2: oklch(.6 .118 184.704);
  --chart-3: oklch(.398 .07 227.392);
  --chart-4: oklch(.828 .189 84.429);
  --chart-5: oklch(.769 .188 70.08);
  --sidebar: oklch(.985 0 0);
  --sidebar-foreground: oklch(.145 0 0);
  --sidebar-primary: oklch(.205 0 0);
  --sidebar-primary-foreground: oklch(.985 0 0);
  --sidebar-accent: oklch(.97 0 0);
  --sidebar-accent-foreground: oklch(.205 0 0);
  --sidebar-border: oklch(.922 0 0);
  --sidebar-ring: oklch(.708 0 0);
}

.dark {
  --background: oklch(.145 0 0);
  --foreground: oklch(.985 0 0);
  --card: oklch(.205 0 0);
  --card-foreground: oklch(.985 0 0);
  --popover: oklch(.205 0 0);
  --popover-foreground: oklch(.985 0 0);
  --primary: oklch(.922 0 0);
  --primary-foreground: oklch(.205 0 0);
  --secondary: oklch(.269 0 0);
  --secondary-foreground: oklch(.985 0 0);
  --muted: oklch(.269 0 0);
  --muted-foreground: oklch(.708 0 0);
  --accent: oklch(.269 0 0);
  --accent-foreground: oklch(.985 0 0);
  --destructive: oklch(.704 .191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(.556 0 0);
  --chart-1: oklch(.488 .243 264.376);
  --chart-2: oklch(.696 .17 162.48);
  --chart-3: oklch(.769 .188 70.08);
  --chart-4: oklch(.627 .265 303.9);
  --chart-5: oklch(.645 .246 16.439);
  --sidebar: oklch(.205 0 0);
  --sidebar-foreground: oklch(.985 0 0);
  --sidebar-primary: oklch(.488 .243 264.376);
  --sidebar-primary-foreground: oklch(.985 0 0);
  --sidebar-accent: oklch(.269 0 0);
  --sidebar-accent-foreground: oklch(.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(.556 0 0);
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-tracking {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

@keyframes bounce {
  0%, 100% {
    animation-timing-function: cubic-bezier(.8, 0, 1, 1);
    transform: translateY(-25%);
  }

  50% {
    animation-timing-function: cubic-bezier(0, 0, .2, 1);
    transform: none;
  }
}

@keyframes enter {
  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}

@keyframes exit {
  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}


/*# sourceMappingURL=%5Broot-of-the-server%5D__8ebb6d4b._.css.map*/