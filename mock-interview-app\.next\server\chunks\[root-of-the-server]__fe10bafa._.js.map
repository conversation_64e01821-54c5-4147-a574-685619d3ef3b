{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/lib/mailinator-service.ts"], "sourcesContent": ["export interface MailinatorConfig {\n  apiToken?: string\n  privateDomain?: string\n  usePublicDomain: boolean\n  webhookToken?: string\n}\n\nexport interface MailinatorMessage {\n  id: string\n  from: string\n  to: string\n  subject: string\n  text?: string\n  html?: string\n  timestamp: number\n}\n\nexport interface MailinatorInbox {\n  name: string\n  messages: MailinatorMessage[]\n}\n\nexport class MailinatorService {\n  private static config: MailinatorConfig = {\n    usePublicDomain: !process.env.MAILINATOR_API_TOKEN, // Use public if no API token\n    privateDomain: process.env.MAILINATOR_PRIVATE_DOMAIN,\n    apiToken: process.env.MAILINATOR_API_TOKEN,\n    webhookToken: process.env.MAILINATOR_WEBHOOK_TOKEN\n  }\n\n  static setConfig(config: Partial<MailinatorConfig>) {\n    this.config = { ...this.config, ...config }\n  }\n\n  static getConfig(): MailinatorConfig {\n    return { ...this.config }\n  }\n\n  // Send email via Mailinator API\n  static async sendEmail(\n    to: string,\n    subject: string,\n    html: string,\n    text?: string\n  ): Promise<boolean> {\n    try {\n      if (this.config.usePublicDomain) {\n        return await this.sendToPublicDomain(to, subject, html, text)\n      } else if (this.config.privateDomain && this.config.apiToken) {\n        return await this.sendToPrivateDomain(to, subject, html, text)\n      } else {\n        console.log('📧 Mailinator Demo Mode - Email would be sent:')\n        console.log(`To: ${to}`)\n        console.log(`Subject: ${subject}`)\n        console.log(`HTML: ${html.substring(0, 200)}...`)\n        return true\n      }\n    } catch (error) {\n      console.error('❌ Failed to send email via Mailinator:', error)\n      return false\n    }\n  }\n\n  // Send to Mailinator public domain (free tier)\n  private static async sendToPublicDomain(\n    to: string,\n    subject: string,\n    html: string,\n    text?: string\n  ): Promise<boolean> {\n    try {\n      // Extract inbox name from email\n      const inboxName = this.extractInboxName(to)\n      \n      // Use Mailinator's webhook endpoint for public domain\n      const webhookUrl = `https://www.mailinator.com/api/v2/domains/public/webhook/${inboxName}/`\n      \n      const payload = {\n        from: '<EMAIL>',\n        subject: subject,\n        text: text || this.htmlToText(html),\n        html: html,\n        to: inboxName\n      }\n\n      const response = await fetch(webhookUrl, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(payload)\n      })\n\n      if (response.ok) {\n        console.log(`✅ Email sent to Mailinator public inbox: ${inboxName}@mailinator.com`)\n        console.log(`📬 Check inbox at: https://www.mailinator.com/v4/public/inboxes.jsp?to=${inboxName}`)\n        return true\n      } else {\n        const errorText = await response.text()\n        console.error('❌ Failed to send to Mailinator public domain:', response.status, errorText)\n        return false\n      }\n    } catch (error) {\n      console.error('❌ Error sending to Mailinator public domain:', error)\n      return false\n    }\n  }\n\n  // Send to Mailinator private domain (requires API token)\n  private static async sendToPrivateDomain(\n    to: string,\n    subject: string,\n    html: string,\n    text?: string\n  ): Promise<boolean> {\n    try {\n      const inboxName = this.extractInboxName(to)\n      \n      const apiUrl = `https://api.mailinator.com/api/v2/domains/private/inboxes/${inboxName}/messages`\n      \n      const payload = {\n        from: '<EMAIL>',\n        subject: subject,\n        text: text || this.htmlToText(html),\n        html: html\n      }\n\n      const response = await fetch(apiUrl, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': this.config.apiToken!\n        },\n        body: JSON.stringify(payload)\n      })\n\n      if (response.ok) {\n        console.log(`✅ Email sent to Mailinator private domain: ${to}`)\n        return true\n      } else {\n        const errorText = await response.text()\n        console.error('❌ Failed to send to Mailinator private domain:', response.status, errorText)\n        return false\n      }\n    } catch (error) {\n      console.error('❌ Error sending to Mailinator private domain:', error)\n      return false\n    }\n  }\n\n  // Fetch messages from Mailinator inbox\n  static async getInboxMessages(inboxName: string): Promise<MailinatorMessage[]> {\n    try {\n      if (this.config.usePublicDomain) {\n        return await this.getPublicInboxMessages(inboxName)\n      } else if (this.config.privateDomain && this.config.apiToken) {\n        return await this.getPrivateInboxMessages(inboxName)\n      } else {\n        console.log('📧 Mailinator not configured for message retrieval')\n        return []\n      }\n    } catch (error) {\n      console.error('❌ Failed to fetch inbox messages:', error)\n      return []\n    }\n  }\n\n  // Get messages from public domain\n  private static async getPublicInboxMessages(inboxName: string): Promise<MailinatorMessage[]> {\n    try {\n      const response = await fetch(`https://www.mailinator.com/api/v2/domains/public/inboxes/${inboxName}`)\n      \n      if (response.ok) {\n        const data = await response.json()\n        return data.msgs || []\n      } else {\n        console.error('❌ Failed to fetch public inbox messages:', response.statusText)\n        return []\n      }\n    } catch (error) {\n      console.error('❌ Error fetching public inbox messages:', error)\n      return []\n    }\n  }\n\n  // Get messages from private domain\n  private static async getPrivateInboxMessages(inboxName: string): Promise<MailinatorMessage[]> {\n    try {\n      const response = await fetch(\n        `https://api.mailinator.com/api/v2/domains/private/inboxes/${inboxName}`,\n        {\n          headers: {\n            'Authorization': this.config.apiToken!\n          }\n        }\n      )\n      \n      if (response.ok) {\n        const data = await response.json()\n        return data.msgs || []\n      } else {\n        console.error('❌ Failed to fetch private inbox messages:', response.statusText)\n        return []\n      }\n    } catch (error) {\n      console.error('❌ Error fetching private inbox messages:', error)\n      return []\n    }\n  }\n\n  // Utility functions\n  private static extractInboxName(email: string): string {\n    const parts = email.split('@')\n    return parts[0] || 'demo'\n  }\n\n  private static htmlToText(html: string): string {\n    // Simple HTML to text conversion\n    return html\n      .replace(/<[^>]*>/g, '')\n      .replace(/&nbsp;/g, ' ')\n      .replace(/&amp;/g, '&')\n      .replace(/&lt;/g, '<')\n      .replace(/&gt;/g, '>')\n      .replace(/&quot;/g, '\"')\n      .trim()\n  }\n\n  // Generate Mailinator email for testing\n  static generateTestEmail(name: string): string {\n    const cleanName = name.toLowerCase().replace(/[^a-z0-9]/g, '')\n    return `${cleanName}@mailinator.com`\n  }\n\n  // Create demo Mailinator account setup\n  static setupDemoAccount(): {\n    testEmails: string[]\n    inboxUrls: string[]\n    instructions: string[]\n  } {\n    const testEmails = [\n      '<EMAIL>',\n      '<EMAIL>',\n      '<EMAIL>',\n      '<EMAIL>'\n    ]\n\n    const inboxUrls = testEmails.map(email => {\n      const inboxName = this.extractInboxName(email)\n      return `https://www.mailinator.com/v4/public/inboxes.jsp?to=${inboxName}`\n    })\n\n    const instructions = [\n      '1. 📧 Use any of the test emails above for demo purposes',\n      '2. 🌐 Visit the inbox URLs to check received emails',\n      '3. 🔄 Emails are automatically deleted after a few hours',\n      '4. 🆓 No signup required for public Mailinator inboxes',\n      '5. 🔗 Click email links to test the full interview flow'\n    ]\n\n    return {\n      testEmails,\n      inboxUrls,\n      instructions\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAsBO,MAAM;IACX,OAAe,SAA2B;QACxC,iBAAiB,CAAC,QAAQ,GAAG,CAAC,oBAAoB;QAClD,eAAe,QAAQ,GAAG,CAAC,yBAAyB;QACpD,UAAU,QAAQ,GAAG,CAAC,oBAAoB;QAC1C,cAAc,QAAQ,GAAG,CAAC,wBAAwB;IACpD,EAAC;IAED,OAAO,UAAU,MAAiC,EAAE;QAClD,IAAI,CAAC,MAAM,GAAG;YAAE,GAAG,IAAI,CAAC,MAAM;YAAE,GAAG,MAAM;QAAC;IAC5C;IAEA,OAAO,YAA8B;QACnC,OAAO;YAAE,GAAG,IAAI,CAAC,MAAM;QAAC;IAC1B;IAEA,gCAAgC;IAChC,aAAa,UACX,EAAU,EACV,OAAe,EACf,IAAY,EACZ,IAAa,EACK;QAClB,IAAI;YACF,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;gBAC/B,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,SAAS,MAAM;YAC1D,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;gBAC5D,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,SAAS,MAAM;YAC3D,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI;gBACvB,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS;gBACjC,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC;gBAChD,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,OAAO;QACT;IACF;IAEA,+CAA+C;IAC/C,aAAqB,mBACnB,EAAU,EACV,OAAe,EACf,IAAY,EACZ,IAAa,EACK;QAClB,IAAI;YACF,gCAAgC;YAChC,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;YAExC,sDAAsD;YACtD,MAAM,aAAa,CAAC,yDAAyD,EAAE,UAAU,CAAC,CAAC;YAE3F,MAAM,UAAU;gBACd,MAAM;gBACN,SAAS;gBACT,MAAM,QAAQ,IAAI,CAAC,UAAU,CAAC;gBAC9B,MAAM;gBACN,IAAI;YACN;YAEA,MAAM,WAAW,MAAM,MAAM,YAAY;gBACvC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,UAAU,eAAe,CAAC;gBAClF,QAAQ,GAAG,CAAC,CAAC,uEAAuE,EAAE,WAAW;gBACjG,OAAO;YACT,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,iDAAiD,SAAS,MAAM,EAAE;gBAChF,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gDAAgD;YAC9D,OAAO;QACT;IACF;IAEA,yDAAyD;IACzD,aAAqB,oBACnB,EAAU,EACV,OAAe,EACf,IAAY,EACZ,IAAa,EACK;QAClB,IAAI;YACF,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;YAExC,MAAM,SAAS,CAAC,0DAA0D,EAAE,UAAU,SAAS,CAAC;YAEhG,MAAM,UAAU;gBACd,MAAM;gBACN,SAAS;gBACT,MAAM,QAAQ,IAAI,CAAC,UAAU,CAAC;gBAC9B,MAAM;YACR;YAEA,MAAM,WAAW,MAAM,MAAM,QAAQ;gBACnC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,IAAI,CAAC,MAAM,CAAC,QAAQ;gBACvC;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,IAAI;gBAC9D,OAAO;YACT,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,kDAAkD,SAAS,MAAM,EAAE;gBACjF,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iDAAiD;YAC/D,OAAO;QACT;IACF;IAEA,uCAAuC;IACvC,aAAa,iBAAiB,SAAiB,EAAgC;QAC7E,IAAI;YACF,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;gBAC/B,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC;YAC3C,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;gBAC5D,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC;YAC5C,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,OAAO,EAAE;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO,EAAE;QACX;IACF;IAEA,kCAAkC;IAClC,aAAqB,uBAAuB,SAAiB,EAAgC;QAC3F,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,yDAAyD,EAAE,WAAW;YAEpG,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,OAAO,KAAK,IAAI,IAAI,EAAE;YACxB,OAAO;gBACL,QAAQ,KAAK,CAAC,4CAA4C,SAAS,UAAU;gBAC7E,OAAO,EAAE;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,OAAO,EAAE;QACX;IACF;IAEA,mCAAmC;IACnC,aAAqB,wBAAwB,SAAiB,EAAgC;QAC5F,IAAI;YACF,MAAM,WAAW,MAAM,MACrB,CAAC,0DAA0D,EAAE,WAAW,EACxE;gBACE,SAAS;oBACP,iBAAiB,IAAI,CAAC,MAAM,CAAC,QAAQ;gBACvC;YACF;YAGF,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,OAAO,KAAK,IAAI,IAAI,EAAE;YACxB,OAAO;gBACL,QAAQ,KAAK,CAAC,6CAA6C,SAAS,UAAU;gBAC9E,OAAO,EAAE;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,OAAO,EAAE;QACX;IACF;IAEA,oBAAoB;IACpB,OAAe,iBAAiB,KAAa,EAAU;QACrD,MAAM,QAAQ,MAAM,KAAK,CAAC;QAC1B,OAAO,KAAK,CAAC,EAAE,IAAI;IACrB;IAEA,OAAe,WAAW,IAAY,EAAU;QAC9C,iCAAiC;QACjC,OAAO,KACJ,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,WAAW,KACnB,OAAO,CAAC,UAAU,KAClB,OAAO,CAAC,SAAS,KACjB,OAAO,CAAC,SAAS,KACjB,OAAO,CAAC,WAAW,KACnB,IAAI;IACT;IAEA,wCAAwC;IACxC,OAAO,kBAAkB,IAAY,EAAU;QAC7C,MAAM,YAAY,KAAK,WAAW,GAAG,OAAO,CAAC,cAAc;QAC3D,OAAO,GAAG,UAAU,eAAe,CAAC;IACtC;IAEA,uCAAuC;IACvC,OAAO,mBAIL;QACA,MAAM,aAAa;YACjB;YACA;YACA;YACA;SACD;QAED,MAAM,YAAY,WAAW,GAAG,CAAC,CAAA;YAC/B,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;YACxC,OAAO,CAAC,oDAAoD,EAAE,WAAW;QAC3E;QAEA,MAAM,eAAe;YACnB;YACA;YACA;YACA;YACA;SACD;QAED,OAAO;YACL;YACA;YACA;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 289, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/lib/email-service.ts"], "sourcesContent": ["import crypto from 'crypto'\nimport { MailinatorService } from './mailinator-service'\n\n// Only import nodemailer on server side\nlet nodemailer: any = null\nlet prisma: any = null\n\nif (typeof window === 'undefined') {\n  // Server-side imports\n  try {\n    nodemailer = require('nodemailer')\n    prisma = require('./prisma').prisma\n  } catch (error) {\n    console.log('Server-side dependencies not available')\n  }\n}\n\nexport interface InterviewInvitation {\n  id: string\n  email: string\n  studentName: string\n  interviewTitle: string\n  scheduledAt: Date\n  token: string\n  status: 'PENDING' | 'ACCEPTED' | 'COMPLETED' | 'EXPIRED'\n  createdAt: Date\n  expiresAt: Date\n}\n\n// In-memory storage for demo (replace with database in production)\nconst invitations: Map<string, InterviewInvitation> = new Map()\n\nexport interface EmailOptions {\n  to: string\n  subject: string\n  html: string\n  attachments?: Array<{\n    filename: string\n    path: string\n  }>\n}\n\nexport class EmailService {\n  private static transporter: any = null\n\n  // Initialize transporter only on server side\n  private static getTransporter() {\n    if (!this.transporter && nodemailer && typeof window === 'undefined') {\n      this.transporter = nodemailer.createTransport({\n        host: process.env.EMAIL_HOST,\n        port: parseInt(process.env.EMAIL_PORT || '587'),\n        secure: false,\n        auth: {\n          user: process.env.EMAIL_USER,\n          pass: process.env.EMAIL_PASS,\n        },\n      })\n    }\n    return this.transporter\n  }\n\n  // Email service mode configuration\n  private static emailMode: 'NODEMAILER' | 'MAILINATOR' | 'DEMO' = 'MAILINATOR'\n\n  static setEmailMode(mode: 'NODEMAILER' | 'MAILINATOR' | 'DEMO') {\n    this.emailMode = mode\n  }\n\n  static getEmailMode(): string {\n    return this.emailMode\n  }\n\n  // Create invitation method\n  static createInvitation(\n    email: string,\n    studentName: string,\n    interviewTitle: string,\n    scheduledAt: Date\n  ) {\n    const token = this.generateInvitationToken()\n    const id = crypto.randomUUID()\n\n    return {\n      id,\n      token,\n      email,\n      studentName,\n      interviewTitle,\n      scheduledAt,\n      status: 'pending' as const,\n      createdAt: new Date(),\n      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days\n    }\n  }\n\n  // Invitation Token Management\n  static generateInvitationToken(): string {\n    return crypto.randomBytes(32).toString('hex')\n  }\n\n  static createInvitation(\n    email: string,\n    studentName: string,\n    interviewTitle: string,\n    scheduledAt: Date\n  ): InterviewInvitation {\n    const token = this.generateInvitationToken()\n    const id = crypto.randomUUID()\n    const expiresAt = new Date(scheduledAt.getTime() + 24 * 60 * 60 * 1000) // 24 hours after scheduled time\n\n    const invitation: InterviewInvitation = {\n      id,\n      email,\n      studentName,\n      interviewTitle,\n      scheduledAt,\n      token,\n      status: 'PENDING',\n      createdAt: new Date(),\n      expiresAt\n    }\n\n    invitations.set(token, invitation)\n    return invitation\n  }\n\n  static getInvitationByToken(token: string): InterviewInvitation | null {\n    const invitation = invitations.get(token)\n    if (!invitation) return null\n\n    // Check if expired\n    if (new Date() > invitation.expiresAt) {\n      invitation.status = 'EXPIRED'\n    }\n\n    return invitation\n  }\n\n  static updateInvitationStatus(token: string, status: InterviewInvitation['status']): boolean {\n    const invitation = invitations.get(token)\n    if (!invitation) return false\n\n    invitation.status = status\n    return true\n  }\n\n  static generateInvitationLink(token: string, baseUrl: string = 'http://localhost:3000'): string {\n    return `${baseUrl}/interview/invite/${token}`\n  }\n\n  static async sendEmail(options: EmailOptions): Promise<boolean> {\n    try {\n      let success = false\n\n      // Choose email service based on mode\n      switch (this.emailMode) {\n        case 'MAILINATOR':\n          success = await MailinatorService.sendEmail(\n            options.to,\n            options.subject,\n            options.html\n          )\n          break\n\n        case 'NODEMAILER':\n          // Skip email sending if no email configuration or not on server\n          if (typeof window !== 'undefined') {\n            console.log('NodeMailer only works on server side')\n            return false\n          }\n\n          if (!process.env.EMAIL_USER || !process.env.EMAIL_PASS) {\n            console.log('Email service not configured, skipping email send')\n            return true\n          }\n\n          const transporter = this.getTransporter()\n          if (!transporter) {\n            console.log('Failed to initialize email transporter')\n            return false\n          }\n\n          const info = await transporter.sendMail({\n            from: process.env.EMAIL_USER,\n            to: options.to,\n            subject: options.subject,\n            html: options.html,\n            attachments: options.attachments,\n          })\n          success = true\n          break\n\n        case 'DEMO':\n        default:\n          console.log('📧 Demo Mode - Email would be sent:')\n          console.log(`To: ${options.to}`)\n          console.log(`Subject: ${options.subject}`)\n          success = true\n          break\n      }\n\n      // Log email (skip if database not available)\n      try {\n        await prisma.emailLog.create({\n          data: {\n            to: options.to,\n            subject: options.subject,\n            body: options.html,\n            status: success ? 'SENT' : 'FAILED',\n            sentAt: new Date(),\n          },\n        })\n      } catch (dbError) {\n        console.log('Database not available for email logging')\n      }\n\n      return success\n    } catch (error) {\n      console.error('Error sending email:', error)\n\n      // Log failed email (skip if database not available)\n      try {\n        await prisma.emailLog.create({\n          data: {\n            to: options.to,\n            subject: options.subject,\n            body: options.html,\n            status: 'FAILED',\n          },\n        })\n      } catch (dbError) {\n        console.log('Database not available for email logging')\n      }\n\n      return false\n    }\n  }\n\n  static async sendInterviewInvitationWithToken(invitation: InterviewInvitation): Promise<boolean> {\n    const invitationLink = this.generateInvitationLink(invitation.token)\n\n    const html = `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <div style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;\">\n          <h1 style=\"color: white; margin: 0;\">🎯 Video Interview Invitation</h1>\n        </div>\n\n        <div style=\"padding: 30px; background: #f8f9fa;\">\n          <h2 style=\"color: #333;\">Hello ${invitation.studentName}!</h2>\n\n          <p style=\"font-size: 16px; line-height: 1.6; color: #555;\">\n            You've been invited to participate in an AI-powered video interview for:\n          </p>\n\n          <div style=\"background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea;\">\n            <h3 style=\"margin: 0 0 10px 0; color: #333;\">${invitation.interviewTitle}</h3>\n            <p style=\"margin: 0; color: #666;\">\n              📅 Scheduled: ${invitation.scheduledAt.toLocaleDateString()} at ${invitation.scheduledAt.toLocaleTimeString()}\n            </p>\n          </div>\n\n          <div style=\"text-align: center; margin: 30px 0;\">\n            <a href=\"${invitationLink}\"\n               style=\"background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;\">\n              🚀 Join Video Interview\n            </a>\n          </div>\n\n          <div style=\"background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0;\">\n            <h4 style=\"margin: 0 0 10px 0; color: #1976d2;\">📋 What to Expect:</h4>\n            <ul style=\"margin: 0; padding-left: 20px; color: #555;\">\n              <li>🤖 AI-powered interviewer with voice interaction</li>\n              <li>📹 Video recording for review and feedback</li>\n              <li>💻 Interactive coding and behavioral questions</li>\n              <li>📊 Real-time evaluation and scoring</li>\n              <li>🎉 Instant results and congratulations</li>\n            </ul>\n          </div>\n\n          <div style=\"background: #fff3e0; padding: 15px; border-radius: 5px; margin: 20px 0;\">\n            <h4 style=\"margin: 0 0 10px 0; color: #f57c00;\">⚠️ Technical Requirements:</h4>\n            <ul style=\"margin: 0; padding-left: 20px; color: #555;\">\n              <li>🌐 Stable internet connection</li>\n              <li>📷 Working camera and microphone</li>\n              <li>🌍 Modern web browser (Chrome recommended)</li>\n              <li>🔇 Quiet environment for recording</li>\n              <li>💾 Allow browser permissions for camera/mic</li>\n            </ul>\n          </div>\n\n          <p style=\"font-size: 14px; color: #777; margin-top: 30px;\">\n            This invitation expires on ${invitation.expiresAt.toLocaleDateString()}.\n            If you have any questions, please contact our support team.\n          </p>\n        </div>\n\n        <div style=\"background: #333; padding: 20px; text-align: center;\">\n          <p style=\"color: #ccc; margin: 0; font-size: 14px;\">\n            © 2024 AI Interview Platform. All rights reserved.\n          </p>\n        </div>\n      </div>\n    `\n\n    return this.sendEmail({\n      to: invitation.email,\n      subject: `🎯 Video Interview Invitation - ${invitation.interviewTitle}`,\n      html,\n    })\n  }\n\n  static async sendInterviewInvitation(\n    studentEmail: string,\n    studentName: string,\n    interviewDetails: {\n      title: string\n      scheduledAt: Date\n      duration: number\n      meetingLink: string\n    }\n  ): Promise<boolean> {\n    const html = `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <h2 style=\"color: #2563eb;\">Interview Invitation</h2>\n        <p>Dear ${studentName},</p>\n        <p>You have been invited to participate in an interview:</p>\n        \n        <div style=\"background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n          <h3 style=\"margin-top: 0;\">${interviewDetails.title}</h3>\n          <p><strong>Date & Time:</strong> ${interviewDetails.scheduledAt.toLocaleString()}</p>\n          <p><strong>Duration:</strong> ${interviewDetails.duration} minutes</p>\n          <p><strong>Meeting Link:</strong> <a href=\"${interviewDetails.meetingLink}\">${interviewDetails.meetingLink}</a></p>\n        </div>\n        \n        <p>Please make sure to:</p>\n        <ul>\n          <li>Test your camera and microphone before the interview</li>\n          <li>Ensure you have a stable internet connection</li>\n          <li>Prepare for coding and theory questions</li>\n          <li>Join the meeting 5 minutes early</li>\n        </ul>\n        \n        <p>Good luck with your interview!</p>\n        <p>Best regards,<br>Mock Interview Team</p>\n      </div>\n    `\n\n    return this.sendEmail({\n      to: studentEmail,\n      subject: `Interview Invitation - ${interviewDetails.title}`,\n      html,\n    })\n  }\n\n  static async sendInterviewResults(\n    studentEmail: string,\n    studentName: string,\n    results: {\n      score: number\n      feedback: string\n      recordingUrl?: string\n    }\n  ): Promise<boolean> {\n    const html = `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <h2 style=\"color: #2563eb;\">Interview Results</h2>\n        <p>Dear ${studentName},</p>\n        <p>Your interview has been completed. Here are your results:</p>\n        \n        <div style=\"background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n          <h3 style=\"margin-top: 0;\">Overall Score: ${results.score}/100</h3>\n          <h4>Feedback:</h4>\n          <p>${results.feedback}</p>\n          ${results.recordingUrl ? `<p><strong>Recording:</strong> <a href=\"${results.recordingUrl}\">View Recording</a></p>` : ''}\n        </div>\n        \n        <p>Keep practicing and improving your skills!</p>\n        <p>Best regards,<br>Mock Interview Team</p>\n      </div>\n    `\n\n    return this.sendEmail({\n      to: studentEmail,\n      subject: 'Your Interview Results',\n      html,\n    })\n  }\n\n  static async sendWelcomeEmail(\n    userEmail: string,\n    userName: string,\n    role: string\n  ): Promise<boolean> {\n    const html = `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <h2 style=\"color: #2563eb;\">Welcome to Mock Interview Platform!</h2>\n        <p>Dear ${userName},</p>\n        <p>Welcome to our Mock Interview Platform! Your account has been created successfully.</p>\n        \n        <div style=\"background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n          <h3 style=\"margin-top: 0;\">Account Details</h3>\n          <p><strong>Email:</strong> ${userEmail}</p>\n          <p><strong>Role:</strong> ${role}</p>\n        </div>\n        \n        <p>You can now log in and start using the platform.</p>\n        <p>Best regards,<br>Mock Interview Team</p>\n      </div>\n    `\n\n    return this.sendEmail({\n      to: userEmail,\n      subject: 'Welcome to Mock Interview Platform',\n      html,\n    })\n  }\n\n  // Demo function to create sample invitations\n  static createDemoInvitations(): InterviewInvitation[] {\n    const demoInvitations = [\n      {\n        email: '<EMAIL>',\n        studentName: 'John Doe',\n        interviewTitle: 'Frontend Developer Position',\n        scheduledAt: new Date(Date.now() + 2 * 60 * 60 * 1000) // 2 hours from now\n      },\n      {\n        email: '<EMAIL>',\n        studentName: 'Jane Smith',\n        interviewTitle: 'Full Stack Developer Role',\n        scheduledAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 1 day from now\n      },\n      {\n        email: '<EMAIL>',\n        studentName: 'Alex Johnson',\n        interviewTitle: 'React Developer Interview',\n        scheduledAt: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3 days from now\n      }\n    ]\n\n    return demoInvitations.map(demo =>\n      this.createInvitation(demo.email, demo.studentName, demo.interviewTitle, demo.scheduledAt)\n    )\n  }\n}\n\n// Initialize demo invitations\nif (typeof window === 'undefined') {\n  EmailService.createDemoInvitations()\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,wCAAwC;AACxC,IAAI,aAAkB;AACtB,IAAI,SAAc;AAElB,wCAAmC;IACjC,sBAAsB;IACtB,IAAI;QACF;QACA,SAAS,gFAAoB,MAAM;IACrC,EAAE,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC;IACd;AACF;AAcA,mEAAmE;AACnE,MAAM,cAAgD,IAAI;AAYnD,MAAM;IACX,OAAe,cAAmB,KAAI;IAEtC,6CAA6C;IAC7C,OAAe,iBAAiB;QAC9B,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,cAAc,gBAAkB,aAAa;YACpE,IAAI,CAAC,WAAW,GAAG,WAAW,eAAe,CAAC;gBAC5C,MAAM,QAAQ,GAAG,CAAC,UAAU;gBAC5B,MAAM,SAAS,QAAQ,GAAG,CAAC,UAAU,IAAI;gBACzC,QAAQ;gBACR,MAAM;oBACJ,MAAM,QAAQ,GAAG,CAAC,UAAU;oBAC5B,MAAM,QAAQ,GAAG,CAAC,UAAU;gBAC9B;YACF;QACF;QACA,OAAO,IAAI,CAAC,WAAW;IACzB;IAEA,mCAAmC;IACnC,OAAe,YAAkD,aAAY;IAE7E,OAAO,aAAa,IAA0C,EAAE;QAC9D,IAAI,CAAC,SAAS,GAAG;IACnB;IAEA,OAAO,eAAuB;QAC5B,OAAO,IAAI,CAAC,SAAS;IACvB;IAEA,2BAA2B;IAC3B,OAAO,iBACL,KAAa,EACb,WAAmB,EACnB,cAAsB,EACtB,WAAiB,EACjB;QACA,MAAM,QAAQ,IAAI,CAAC,uBAAuB;QAC1C,MAAM,KAAK,qGAAA,CAAA,UAAM,CAAC,UAAU;QAE5B,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA,QAAQ;YACR,WAAW,IAAI;YACf,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,SAAS;QACrE;IACF;IAEA,8BAA8B;IAC9B,OAAO,0BAAkC;QACvC,OAAO,qGAAA,CAAA,UAAM,CAAC,WAAW,CAAC,IAAI,QAAQ,CAAC;IACzC;IAEA,OAAO,iBACL,KAAa,EACb,WAAmB,EACnB,cAAsB,EACtB,WAAiB,EACI;QACrB,MAAM,QAAQ,IAAI,CAAC,uBAAuB;QAC1C,MAAM,KAAK,qGAAA,CAAA,UAAM,CAAC,UAAU;QAC5B,MAAM,YAAY,IAAI,KAAK,YAAY,OAAO,KAAK,KAAK,KAAK,KAAK,MAAM,gCAAgC;;QAExG,MAAM,aAAkC;YACtC;YACA;YACA;YACA;YACA;YACA;YACA,QAAQ;YACR,WAAW,IAAI;YACf;QACF;QAEA,YAAY,GAAG,CAAC,OAAO;QACvB,OAAO;IACT;IAEA,OAAO,qBAAqB,KAAa,EAA8B;QACrE,MAAM,aAAa,YAAY,GAAG,CAAC;QACnC,IAAI,CAAC,YAAY,OAAO;QAExB,mBAAmB;QACnB,IAAI,IAAI,SAAS,WAAW,SAAS,EAAE;YACrC,WAAW,MAAM,GAAG;QACtB;QAEA,OAAO;IACT;IAEA,OAAO,uBAAuB,KAAa,EAAE,MAAqC,EAAW;QAC3F,MAAM,aAAa,YAAY,GAAG,CAAC;QACnC,IAAI,CAAC,YAAY,OAAO;QAExB,WAAW,MAAM,GAAG;QACpB,OAAO;IACT;IAEA,OAAO,uBAAuB,KAAa,EAAE,UAAkB,uBAAuB,EAAU;QAC9F,OAAO,GAAG,QAAQ,kBAAkB,EAAE,OAAO;IAC/C;IAEA,aAAa,UAAU,OAAqB,EAAoB;QAC9D,IAAI;YACF,IAAI,UAAU;YAEd,qCAAqC;YACrC,OAAQ,IAAI,CAAC,SAAS;gBACpB,KAAK;oBACH,UAAU,MAAM,qIAAA,CAAA,oBAAiB,CAAC,SAAS,CACzC,QAAQ,EAAE,EACV,QAAQ,OAAO,EACf,QAAQ,IAAI;oBAEd;gBAEF,KAAK;oBACH,gEAAgE;oBAChE,uCAAmC;;oBAGnC;oBAEA,IAAI,CAAC,QAAQ,GAAG,CAAC,UAAU,IAAI,CAAC,QAAQ,GAAG,CAAC,UAAU,EAAE;wBACtD,QAAQ,GAAG,CAAC;wBACZ,OAAO;oBACT;oBAEA,MAAM,cAAc,IAAI,CAAC,cAAc;oBACvC,IAAI,CAAC,aAAa;wBAChB,QAAQ,GAAG,CAAC;wBACZ,OAAO;oBACT;oBAEA,MAAM,OAAO,MAAM,YAAY,QAAQ,CAAC;wBACtC,MAAM,QAAQ,GAAG,CAAC,UAAU;wBAC5B,IAAI,QAAQ,EAAE;wBACd,SAAS,QAAQ,OAAO;wBACxB,MAAM,QAAQ,IAAI;wBAClB,aAAa,QAAQ,WAAW;oBAClC;oBACA,UAAU;oBACV;gBAEF,KAAK;gBACL;oBACE,QAAQ,GAAG,CAAC;oBACZ,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE;oBAC/B,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,QAAQ,OAAO,EAAE;oBACzC,UAAU;oBACV;YACJ;YAEA,6CAA6C;YAC7C,IAAI;gBACF,MAAM,OAAO,QAAQ,CAAC,MAAM,CAAC;oBAC3B,MAAM;wBACJ,IAAI,QAAQ,EAAE;wBACd,SAAS,QAAQ,OAAO;wBACxB,MAAM,QAAQ,IAAI;wBAClB,QAAQ,UAAU,SAAS;wBAC3B,QAAQ,IAAI;oBACd;gBACF;YACF,EAAE,OAAO,SAAS;gBAChB,QAAQ,GAAG,CAAC;YACd;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YAEtC,oDAAoD;YACpD,IAAI;gBACF,MAAM,OAAO,QAAQ,CAAC,MAAM,CAAC;oBAC3B,MAAM;wBACJ,IAAI,QAAQ,EAAE;wBACd,SAAS,QAAQ,OAAO;wBACxB,MAAM,QAAQ,IAAI;wBAClB,QAAQ;oBACV;gBACF;YACF,EAAE,OAAO,SAAS;gBAChB,QAAQ,GAAG,CAAC;YACd;YAEA,OAAO;QACT;IACF;IAEA,aAAa,iCAAiC,UAA+B,EAAoB;QAC/F,MAAM,iBAAiB,IAAI,CAAC,sBAAsB,CAAC,WAAW,KAAK;QAEnE,MAAM,OAAO,CAAC;;;;;;;yCAOuB,EAAE,WAAW,WAAW,CAAC;;;;;;;yDAOT,EAAE,WAAW,cAAc,CAAC;;4BAEzD,EAAE,WAAW,WAAW,CAAC,kBAAkB,GAAG,IAAI,EAAE,WAAW,WAAW,CAAC,kBAAkB,GAAG;;;;;qBAKvG,EAAE,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCA6BC,EAAE,WAAW,SAAS,CAAC,kBAAkB,GAAG;;;;;;;;;;;IAW/E,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,IAAI,WAAW,KAAK;YACpB,SAAS,CAAC,gCAAgC,EAAE,WAAW,cAAc,EAAE;YACvE;QACF;IACF;IAEA,aAAa,wBACX,YAAoB,EACpB,WAAmB,EACnB,gBAKC,EACiB;QAClB,MAAM,OAAO,CAAC;;;gBAGF,EAAE,YAAY;;;;qCAIO,EAAE,iBAAiB,KAAK,CAAC;2CACnB,EAAE,iBAAiB,WAAW,CAAC,cAAc,GAAG;wCACnD,EAAE,iBAAiB,QAAQ,CAAC;qDACf,EAAE,iBAAiB,WAAW,CAAC,EAAE,EAAE,iBAAiB,WAAW,CAAC;;;;;;;;;;;;;;IAcjH,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,IAAI;YACJ,SAAS,CAAC,uBAAuB,EAAE,iBAAiB,KAAK,EAAE;YAC3D;QACF;IACF;IAEA,aAAa,qBACX,YAAoB,EACpB,WAAmB,EACnB,OAIC,EACiB;QAClB,MAAM,OAAO,CAAC;;;gBAGF,EAAE,YAAY;;;;oDAIsB,EAAE,QAAQ,KAAK,CAAC;;aAEvD,EAAE,QAAQ,QAAQ,CAAC;UACtB,EAAE,QAAQ,YAAY,GAAG,CAAC,wCAAwC,EAAE,QAAQ,YAAY,CAAC,wBAAwB,CAAC,GAAG,GAAG;;;;;;IAM9H,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,IAAI;YACJ,SAAS;YACT;QACF;IACF;IAEA,aAAa,iBACX,SAAiB,EACjB,QAAgB,EAChB,IAAY,EACM;QAClB,MAAM,OAAO,CAAC;;;gBAGF,EAAE,SAAS;;;;;qCAKU,EAAE,UAAU;oCACb,EAAE,KAAK;;;;;;IAMvC,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,IAAI;YACJ,SAAS;YACT;QACF;IACF;IAEA,6CAA6C;IAC7C,OAAO,wBAA+C;QACpD,MAAM,kBAAkB;YACtB;gBACE,OAAO;gBACP,aAAa;gBACb,gBAAgB;gBAChB,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,mBAAmB;YAC5E;YACA;gBACE,OAAO;gBACP,aAAa;gBACb,gBAAgB;gBAChB,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,MAAM,iBAAiB;YAC3E;YACA;gBACE,OAAO;gBACP,aAAa;gBACb,gBAAgB;gBAChB,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,kBAAkB;YAChF;SACD;QAED,OAAO,gBAAgB,GAAG,CAAC,CAAA,OACzB,IAAI,CAAC,gBAAgB,CAAC,KAAK,KAAK,EAAE,KAAK,WAAW,EAAE,KAAK,cAAc,EAAE,KAAK,WAAW;IAE7F;AACF;AAEA,8BAA8B;AAC9B,wCAAmC;IACjC,aAAa,qBAAqB;AACpC", "debugId": null}}, {"offset": {"line": 662, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/app/api/email/send-invitation/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { EmailService } from '@/lib/email-service'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { email, studentName, interviewTitle, scheduledAt } = body\n\n    console.log('Received invitation request:', { email, studentName, interviewTitle, scheduledAt })\n\n    // Validate required fields\n    if (!email || !studentName || !interviewTitle) {\n      return NextResponse.json(\n        { error: 'Missing required fields: email, studentName, interviewTitle' },\n        { status: 400 }\n      )\n    }\n\n    // Create invitation\n    const invitation = EmailService.createInvitation(\n      email,\n      studentName,\n      interviewTitle,\n      scheduledAt ? new Date(scheduledAt) : new Date(Date.now() + 2 * 60 * 60 * 1000) // Default: 2 hours from now\n    )\n\n    console.log('Created invitation:', invitation)\n\n    // Send invitation email\n    console.log('Attempting to send email with mode:', EmailService.getEmailMode())\n    const success = await EmailService.sendInterviewInvitationWithToken(invitation)\n    console.log('Email send result:', success)\n\n    if (success) {\n      return NextResponse.json({\n        success: true,\n        message: 'Interview invitation sent successfully',\n        invitation: {\n          id: invitation.id,\n          token: invitation.token,\n          email: invitation.email,\n          studentName: invitation.studentName,\n          interviewTitle: invitation.interviewTitle,\n          scheduledAt: invitation.scheduledAt,\n          status: invitation.status,\n          invitationUrl: `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/interview/invite/${invitation.token}`\n        }\n      })\n    } else {\n      return NextResponse.json(\n        { error: 'Failed to send invitation email' },\n        { status: 500 }\n      )\n    }\n  } catch (error) {\n    console.error('Error sending invitation:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,cAAc,EAAE,WAAW,EAAE,GAAG;QAE5D,QAAQ,GAAG,CAAC,gCAAgC;YAAE;YAAO;YAAa;YAAgB;QAAY;QAE9F,2BAA2B;QAC3B,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,gBAAgB;YAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA8D,GACvE;gBAAE,QAAQ;YAAI;QAElB;QAEA,oBAAoB;QACpB,MAAM,aAAa,gIAAA,CAAA,eAAY,CAAC,gBAAgB,CAC9C,OACA,aACA,gBACA,cAAc,IAAI,KAAK,eAAe,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,4BAA4B;;QAG9G,QAAQ,GAAG,CAAC,uBAAuB;QAEnC,wBAAwB;QACxB,QAAQ,GAAG,CAAC,uCAAuC,gIAAA,CAAA,eAAY,CAAC,YAAY;QAC5E,MAAM,UAAU,MAAM,gIAAA,CAAA,eAAY,CAAC,gCAAgC,CAAC;QACpE,QAAQ,GAAG,CAAC,sBAAsB;QAElC,IAAI,SAAS;YACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;gBACT,YAAY;oBACV,IAAI,WAAW,EAAE;oBACjB,OAAO,WAAW,KAAK;oBACvB,OAAO,WAAW,KAAK;oBACvB,aAAa,WAAW,WAAW;oBACnC,gBAAgB,WAAW,cAAc;oBACzC,aAAa,WAAW,WAAW;oBACnC,QAAQ,WAAW,MAAM;oBACzB,eAAe,GAAG,QAAQ,GAAG,CAAC,YAAY,IAAI,wBAAwB,kBAAkB,EAAE,WAAW,KAAK,EAAE;gBAC9G;YACF;QACF,OAAO;YACL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkC,GAC3C;gBAAE,QAAQ;YAAI;QAElB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}