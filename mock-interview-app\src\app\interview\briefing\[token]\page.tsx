'use client'

import { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { EmailService, InterviewInvitation } from '@/lib/email-service'
import { DemoAuth } from '@/lib/demo-auth'
import { 
  Play, 
  Pause, 
  Volume2, 
  VolumeX, 
  CheckCircle, 
  Clock,
  Video,
  Mic,
  Monitor,
  User,
  AlertTriangle,
  ArrowRight
} from 'lucide-react'

export default function InterviewBriefingPage() {
  const params = useParams()
  const router = useRouter()
  const token = params.token as string

  const [invitation, setInvitation] = useState<InterviewInvitation | null>(null)
  const [loading, setLoading] = useState(true)
  const [isSpeaking, setIsSpeaking] = useState(false)
  const [currentMessage, setCurrentMessage] = useState('')
  const [briefingStep, setBriefingStep] = useState(0)
  const [isAudioEnabled, setIsAudioEnabled] = useState(true)
  const [hasStartedBriefing, setHasStartedBriefing] = useState(false)

  const briefingMessages = [
    "Welcome to your AI-powered video interview! I'm your virtual interviewer assistant.",
    "Before we begin, let me explain what will happen during this interview session.",
    "First, we'll set up your camera and microphone to ensure everything is working properly.",
    "Then, I'll guide you through a series of questions covering technical skills, problem-solving, and behavioral scenarios.",
    "Your responses will be recorded for review, and you'll receive instant feedback on your performance.",
    "The entire interview will take approximately 30 to 45 minutes to complete.",
    "Remember to speak clearly, maintain eye contact with the camera, and take your time to think before answering.",
    "Are you ready to set up your camera and microphone? Click 'Start Camera Setup' when you're prepared to begin!"
  ]

  useEffect(() => {
    // Check authentication
    const currentUser = DemoAuth.getCurrentUser()
    if (!currentUser) {
      router.push(`/interview/invite/${token}`)
      return
    }

    // Load invitation
    if (token) {
      const invitationData = EmailService.getInvitationByToken(token)
      if (invitationData) {
        setInvitation(invitationData)
      } else {
        router.push('/')
        return
      }
      setLoading(false)
    }
  }, [token, router])

  const speakMessage = (message: string) => {
    if (!isAudioEnabled) return

    setIsSpeaking(true)
    setCurrentMessage(message)

    if ('speechSynthesis' in window) {
      // Stop any ongoing speech
      window.speechSynthesis.cancel()

      const utterance = new SpeechSynthesisUtterance(message)
      utterance.rate = 0.9
      utterance.pitch = 1
      utterance.volume = 0.8

      // Try to use a professional voice
      const voices = window.speechSynthesis.getVoices()
      const preferredVoice = voices.find(voice => 
        voice.name.includes('Google') || 
        voice.name.includes('Microsoft') ||
        voice.lang.startsWith('en')
      )
      if (preferredVoice) {
        utterance.voice = preferredVoice
      }

      utterance.onend = () => {
        setIsSpeaking(false)
        // Auto-advance to next message after a pause
        if (briefingStep < briefingMessages.length - 1) {
          setTimeout(() => {
            setBriefingStep(prev => prev + 1)
          }, 2000)
        }
      }

      utterance.onerror = () => {
        setIsSpeaking(false)
      }

      window.speechSynthesis.speak(utterance)
    } else {
      // Fallback for browsers without speech synthesis
      setTimeout(() => {
        setIsSpeaking(false)
        if (briefingStep < briefingMessages.length - 1) {
          setBriefingStep(prev => prev + 1)
        }
      }, 3000)
    }
  }

  const startBriefing = () => {
    setHasStartedBriefing(true)
    setBriefingStep(0)
  }

  const toggleAudio = () => {
    setIsAudioEnabled(!isAudioEnabled)
    if (isSpeaking) {
      window.speechSynthesis.cancel()
      setIsSpeaking(false)
    }
  }

  const skipToSetup = () => {
    if (isSpeaking) {
      window.speechSynthesis.cancel()
      setIsSpeaking(false)
    }
    router.push(`/interview/setup/${token}`)
  }

  const repeatMessage = () => {
    if (briefingStep < briefingMessages.length) {
      speakMessage(briefingMessages[briefingStep])
    }
  }

  // Auto-speak when briefing step changes
  useEffect(() => {
    if (hasStartedBriefing && briefingStep < briefingMessages.length) {
      speakMessage(briefingMessages[briefingStep])
    }
  }, [briefingStep, hasStartedBriefing, isAudioEnabled])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p>Loading briefing...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-white mb-2">🎯 Interview Briefing</h1>
          <p className="text-blue-100">Get ready for your AI-powered interview experience</p>
        </div>
      </div>

      <div className="max-w-4xl mx-auto p-6">
        <div className="grid md:grid-cols-3 gap-6">
          {/* AI Assistant */}
          <div className="md:col-span-2">
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center justify-between">
                  <span className="flex items-center">
                    <User className="h-5 w-5 mr-2 text-blue-500" />
                    AI Interview Assistant
                  </span>
                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={toggleAudio}
                      className="border-gray-600"
                    >
                      {isAudioEnabled ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {/* AI Avatar */}
                <div className="flex justify-center mb-6">
                  <div className="relative">
                    <div className={`w-32 h-32 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center transition-all duration-500 ${
                      isSpeaking ? 'scale-110 shadow-lg shadow-blue-500/50' : 'scale-100'
                    }`}>
                      {/* Avatar Face */}
                      <div className="w-24 h-24 rounded-full bg-gradient-to-br from-blue-300 to-purple-400 flex items-center justify-center relative">
                        {/* Eyes */}
                        <div className="absolute top-6 left-6 w-2 h-2 bg-white rounded-full"></div>
                        <div className="absolute top-6 right-6 w-2 h-2 bg-white rounded-full"></div>
                        
                        {/* Mouth */}
                        <div className={`absolute bottom-6 left-1/2 transform -translate-x-1/2 transition-all duration-300 ${
                          isSpeaking 
                            ? 'w-6 h-3 bg-white rounded-full animate-pulse' 
                            : 'w-4 h-1 bg-white rounded-full'
                        }`}></div>
                      </div>
                      
                      {/* Speaking indicator */}
                      {isSpeaking && (
                        <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
                          <div className="flex space-x-1">
                            <div className="w-2 h-2 bg-green-400 rounded-full animate-bounce"></div>
                            <div className="w-2 h-2 bg-green-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                            <div className="w-2 h-2 bg-green-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Speech Bubble */}
                {(hasStartedBriefing || currentMessage) && (
                  <div className="bg-blue-900 border border-blue-700 rounded-lg p-4 mb-4 relative">
                    <div className="absolute -top-2 left-8 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-blue-900"></div>
                    <p className="text-blue-100 text-center">
                      {hasStartedBriefing ? briefingMessages[briefingStep] : "Click 'Start Briefing' to begin!"}
                    </p>
                  </div>
                )}

                {/* Progress Indicator */}
                {hasStartedBriefing && (
                  <div className="mb-4">
                    <div className="flex justify-between text-sm text-gray-400 mb-2">
                      <span>Briefing Progress</span>
                      <span>{briefingStep + 1} / {briefingMessages.length}</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div 
                        className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${((briefingStep + 1) / briefingMessages.length) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                )}

                {/* Controls */}
                <div className="flex justify-center space-x-3">
                  {!hasStartedBriefing ? (
                    <Button onClick={startBriefing} className="bg-blue-600 hover:bg-blue-700">
                      <Play className="h-4 w-4 mr-2" />
                      Start Briefing
                    </Button>
                  ) : (
                    <>
                      <Button 
                        onClick={repeatMessage} 
                        variant="outline"
                        disabled={isSpeaking}
                        className="border-gray-600"
                      >
                        <Volume2 className="h-4 w-4 mr-2" />
                        Repeat
                      </Button>
                      
                      {briefingStep >= briefingMessages.length - 1 && (
                        <Button onClick={skipToSetup} className="bg-green-600 hover:bg-green-700">
                          <ArrowRight className="h-4 w-4 mr-2" />
                          Start Camera Setup
                        </Button>
                      )}
                    </>
                  )}
                  
                  <Button onClick={skipToSetup} variant="outline" className="border-gray-600">
                    Skip Briefing
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Interview Info */}
          <div className="space-y-4">
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white text-sm">Interview Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="text-xs text-gray-400">Position</p>
                  <p className="text-white text-sm font-medium">{invitation?.interviewTitle}</p>
                </div>
                <div>
                  <p className="text-xs text-gray-400">Candidate</p>
                  <p className="text-white text-sm font-medium">{invitation?.studentName}</p>
                </div>
                <div>
                  <p className="text-xs text-gray-400">Duration</p>
                  <p className="text-white text-sm font-medium">30-45 minutes</p>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white text-sm flex items-center">
                  <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                  What's Next
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center space-x-2">
                    <Video className="h-3 w-3 text-blue-400" />
                    <span className="text-gray-300">Camera setup</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Mic className="h-3 w-3 text-blue-400" />
                    <span className="text-gray-300">Microphone test</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <User className="h-3 w-3 text-blue-400" />
                    <span className="text-gray-300">AI interview</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-3 w-3 text-blue-400" />
                    <span className="text-gray-300">Results & feedback</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-orange-900 border-orange-700">
              <CardHeader>
                <CardTitle className="text-white text-sm flex items-center">
                  <AlertTriangle className="h-4 w-4 mr-2 text-orange-400" />
                  Important Tips
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1 text-xs text-orange-100">
                  <li>• Ensure good lighting on your face</li>
                  <li>• Minimize background noise</li>
                  <li>• Maintain eye contact with camera</li>
                  <li>• Speak clearly and at normal pace</li>
                  <li>• Have water nearby if needed</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
