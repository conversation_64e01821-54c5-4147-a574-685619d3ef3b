import OpenAI from 'openai'
import { GoogleGenerativeAI } from '@google/generative-ai'

// OpenAI Configuration
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

// Google Gemini Configuration
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY!)

export interface CodeEvaluationResult {
  score: number
  feedback: string
  suggestions: string[]
  isCorrect: boolean
}

export interface TheoryEvaluationResult {
  score: number
  feedback: string
  keyPoints: string[]
  accuracy: number
}

export class AIService {
  // Evaluate coding answers using OpenAI
  static async evaluateCodingAnswer(
    question: string,
    userCode: string,
    expectedSolution?: string,
    testCases?: any[]
  ): Promise<CodeEvaluationResult> {
    try {
      const prompt = `
        Evaluate the following coding solution:
        
        Question: ${question}
        User's Code: ${userCode}
        ${expectedSolution ? `Expected Solution: ${expectedSolution}` : ''}
        ${testCases ? `Test Cases: ${JSON.stringify(testCases)}` : ''}
        
        Please provide:
        1. A score out of 100
        2. Detailed feedback
        3. Suggestions for improvement
        4. Whether the solution is correct
        
        Respond in JSON format with keys: score, feedback, suggestions, isCorrect
      `

      const response = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.3,
      })

      const result = JSON.parse(response.choices[0].message.content || '{}')
      return result
    } catch (error) {
      console.error('Error evaluating coding answer:', error)
      return {
        score: 0,
        feedback: 'Error evaluating the code. Please try again.',
        suggestions: [],
        isCorrect: false
      }
    }
  }

  // Evaluate theory answers using Google Gemini
  static async evaluateTheoryAnswer(
    question: string,
    userAnswer: string,
    expectedAnswer?: string
  ): Promise<TheoryEvaluationResult> {
    try {
      const model = genAI.getGenerativeModel({ model: 'gemini-pro' })

      const prompt = `
        Evaluate the following theory answer:
        
        Question: ${question}
        User's Answer: ${userAnswer}
        ${expectedAnswer ? `Expected Answer: ${expectedAnswer}` : ''}
        
        Please provide:
        1. A score out of 100
        2. Detailed feedback
        3. Key points covered
        4. Accuracy percentage
        
        Respond in JSON format with keys: score, feedback, keyPoints, accuracy
      `

      const result = await model.generateContent(prompt)
      const response = await result.response
      const text = response.text()
      
      const evaluation = JSON.parse(text)
      return evaluation
    } catch (error) {
      console.error('Error evaluating theory answer:', error)
      return {
        score: 0,
        feedback: 'Error evaluating the answer. Please try again.',
        keyPoints: [],
        accuracy: 0
      }
    }
  }

  // Generate interview questions using AI
  static async generateInterviewQuestions(
    role: string,
    difficulty: string,
    count: number = 5
  ): Promise<any[]> {
    try {
      const prompt = `
        Generate ${count} interview questions for a ${role} position with ${difficulty} difficulty.
        Include a mix of coding, theory, and behavioral questions.
        
        For each question, provide:
        1. Question text
        2. Type (CODING, THEORY, BEHAVIORAL)
        3. Expected answer or solution
        4. Time limit in minutes
        5. Difficulty level
        
        Respond in JSON format as an array of question objects.
      `

      const response = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.7,
      })

      const questions = JSON.parse(response.choices[0].message.content || '[]')
      return questions
    } catch (error) {
      console.error('Error generating questions:', error)
      return []
    }
  }

  // Generate overall interview feedback
  static async generateInterviewFeedback(
    answers: any[],
    overallScore: number
  ): Promise<string> {
    try {
      const prompt = `
        Generate comprehensive interview feedback based on the following:
        
        Answers: ${JSON.stringify(answers)}
        Overall Score: ${overallScore}
        
        Provide constructive feedback covering:
        1. Strengths demonstrated
        2. Areas for improvement
        3. Specific recommendations
        4. Overall assessment
        
        Make it encouraging and actionable.
      `

      const response = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.5,
      })

      return response.choices[0].message.content || 'No feedback available.'
    } catch (error) {
      console.error('Error generating feedback:', error)
      return 'Error generating feedback. Please contact support.'
    }
  }
}
