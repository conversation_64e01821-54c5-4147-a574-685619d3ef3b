// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User roles enum
enum UserRole {
  ADMIN
  TENANT
  STUDENT
}

// Interview status enum
enum InterviewStatus {
  SCHEDULED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

// Question types enum
enum QuestionType {
  CODING
  THEORY
  BEHAVIORAL
}

// Subscription status enum
enum SubscriptionStatus {
  ACTIVE
  INACTIVE
  EXPIRED
  CANCELLED
}

// User model
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  name          String?
  password      String
  role          UserRole  @default(STUDENT)
  isActive      Boolean   @default(true)
  profileImage  String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  tenantId      String?
  tenant        Tenant?   @relation(fields: [tenantId], references: [id])

  // Student specific relations
  studentInterviews Interview[] @relation("StudentInterviews")
  studentResults    Result[]

  // Tenant specific relations
  tenantInterviews  Interview[] @relation("TenantInterviews")
  tenantQuestions   Question[]

  // Admin specific relations
  adminTenants      Tenant[]    @relation("AdminTenants")

  // Subscription
  subscription      Subscription?

  // NextAuth relations
  accounts          Account[]
  sessions          Session[]

  @@map("users")
}

// Tenant model (Organizations/Companies)
model Tenant {
  id          String    @id @default(cuid())
  name        String
  domain      String?   @unique
  description String?
  logo        String?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  adminId     String?
  admin       User?     @relation("AdminTenants", fields: [adminId], references: [id])
  users       User[]
  interviews  Interview[]
  questions   Question[]
  subscription Subscription?

  @@map("tenants")
}

// Subscription model
model Subscription {
  id          String             @id @default(cuid())
  planName    String
  price       Float
  status      SubscriptionStatus @default(ACTIVE)
  startDate   DateTime
  endDate     DateTime
  features    Json // Store plan features as JSON
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt

  // Relations
  userId      String?            @unique
  user        User?              @relation(fields: [userId], references: [id])
  tenantId    String?            @unique
  tenant      Tenant?            @relation(fields: [tenantId], references: [id])

  @@map("subscriptions")
}

// Interview model
model Interview {
  id              String          @id @default(cuid())
  title           String
  description     String?
  scheduledAt     DateTime
  duration        Int             // Duration in minutes
  status          InterviewStatus @default(SCHEDULED)
  recordingUrl    String?
  meetingLink     String?
  notes           String?
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  // Relations
  studentId       String
  student         User            @relation("StudentInterviews", fields: [studentId], references: [id])
  interviewerId   String
  interviewer     User            @relation("TenantInterviews", fields: [interviewerId], references: [id])
  tenantId        String
  tenant          Tenant          @relation(fields: [tenantId], references: [id])

  questions       InterviewQuestion[]
  results         Result[]

  @@map("interviews")
}

// Question model
model Question {
  id          String      @id @default(cuid())
  title       String
  description String
  type        QuestionType
  difficulty  String      // Easy, Medium, Hard
  tags        String[]    // Array of tags
  timeLimit   Int?        // Time limit in minutes
  testCases   Json?       // For coding questions
  solution    String?     // Expected solution
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  createdById String
  createdBy   User        @relation(fields: [createdById], references: [id])
  tenantId    String
  tenant      Tenant      @relation(fields: [tenantId], references: [id])

  interviewQuestions InterviewQuestion[]

  @@map("questions")
}

// Interview Question junction table
model InterviewQuestion {
  id          String    @id @default(cuid())
  order       Int       // Order of question in interview
  createdAt   DateTime  @default(now())

  // Relations
  interviewId String
  interview   Interview @relation(fields: [interviewId], references: [id], onDelete: Cascade)
  questionId  String
  question    Question  @relation(fields: [questionId], references: [id])

  @@unique([interviewId, questionId])
  @@map("interview_questions")
}

// Result model
model Result {
  id          String    @id @default(cuid())
  score       Float     // Overall score
  feedback    String?   // AI generated feedback
  answers     Json      // Store answers as JSON
  timeSpent   Int       // Time spent in minutes
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  interviewId String
  interview   Interview @relation(fields: [interviewId], references: [id])
  studentId   String
  student     User      @relation(fields: [studentId], references: [id])

  @@map("results")
}

// Email Log model
model EmailLog {
  id          String    @id @default(cuid())
  to          String
  subject     String
  body        String
  status      String    // SENT, FAILED, PENDING
  sentAt      DateTime?
  createdAt   DateTime  @default(now())

  @@map("email_logs")
}

// Session model for NextAuth
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}
