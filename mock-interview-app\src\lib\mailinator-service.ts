export interface MailinatorConfig {
  apiToken?: string
  privateDomain?: string
  usePublicDomain: boolean
  webhookToken?: string
}

export interface MailinatorMessage {
  id: string
  from: string
  to: string
  subject: string
  text?: string
  html?: string
  timestamp: number
}

export interface MailinatorInbox {
  name: string
  messages: MailinatorMessage[]
}

export class MailinatorService {
  private static config: MailinatorConfig = {
    usePublicDomain: !process.env.MAILINATOR_API_TOKEN, // Use public if no API token
    privateDomain: process.env.MAILINATOR_PRIVATE_DOMAIN,
    apiToken: process.env.MAILINATOR_API_TOKEN,
    webhookToken: process.env.MAILINATOR_WEBHOOK_TOKEN
  }

  static setConfig(config: Partial<MailinatorConfig>) {
    this.config = { ...this.config, ...config }
  }

  static getConfig(): MailinatorConfig {
    return { ...this.config }
  }

  // Send email via Mailinator API
  static async sendEmail(
    to: string,
    subject: string,
    html: string,
    text?: string
  ): Promise<boolean> {
    try {
      if (this.config.usePublicDomain) {
        return await this.sendToPublicDomain(to, subject, html, text)
      } else if (this.config.privateDomain && this.config.apiToken) {
        return await this.sendToPrivateDomain(to, subject, html, text)
      } else {
        console.log('📧 Mailinator Demo Mode - Email would be sent:')
        console.log(`To: ${to}`)
        console.log(`Subject: ${subject}`)
        console.log(`HTML: ${html.substring(0, 200)}...`)
        return true
      }
    } catch (error) {
      console.error('❌ Failed to send email via Mailinator:', error)
      return false
    }
  }

  // Send to Mailinator public domain (free tier)
  private static async sendToPublicDomain(
    to: string,
    subject: string,
    html: string,
    text?: string
  ): Promise<boolean> {
    try {
      // Extract inbox name from email
      const inboxName = this.extractInboxName(to)
      
      // Use Mailinator's webhook endpoint for public domain
      const webhookUrl = `https://www.mailinator.com/api/v2/domains/public/webhook/${inboxName}/`
      
      const payload = {
        from: '<EMAIL>',
        subject: subject,
        text: text || this.htmlToText(html),
        html: html,
        to: inboxName
      }

      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      })

      if (response.ok) {
        console.log(`✅ Email sent to Mailinator public inbox: ${inboxName}@mailinator.com`)
        console.log(`📬 Check inbox at: https://www.mailinator.com/v4/public/inboxes.jsp?to=${inboxName}`)
        return true
      } else {
        const errorText = await response.text()
        console.error('❌ Failed to send to Mailinator public domain:', response.status, errorText)
        return false
      }
    } catch (error) {
      console.error('❌ Error sending to Mailinator public domain:', error)
      return false
    }
  }

  // Send to Mailinator private domain (requires API token)
  private static async sendToPrivateDomain(
    to: string,
    subject: string,
    html: string,
    text?: string
  ): Promise<boolean> {
    try {
      const inboxName = this.extractInboxName(to)
      
      const apiUrl = `https://api.mailinator.com/api/v2/domains/private/inboxes/${inboxName}/messages`
      
      const payload = {
        from: '<EMAIL>',
        subject: subject,
        text: text || this.htmlToText(html),
        html: html
      }

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': this.config.apiToken!
        },
        body: JSON.stringify(payload)
      })

      if (response.ok) {
        console.log(`✅ Email sent to Mailinator private domain: ${to}`)
        return true
      } else {
        const errorText = await response.text()
        console.error('❌ Failed to send to Mailinator private domain:', response.status, errorText)
        return false
      }
    } catch (error) {
      console.error('❌ Error sending to Mailinator private domain:', error)
      return false
    }
  }

  // Fetch messages from Mailinator inbox
  static async getInboxMessages(inboxName: string): Promise<MailinatorMessage[]> {
    try {
      if (this.config.usePublicDomain) {
        return await this.getPublicInboxMessages(inboxName)
      } else if (this.config.privateDomain && this.config.apiToken) {
        return await this.getPrivateInboxMessages(inboxName)
      } else {
        console.log('📧 Mailinator not configured for message retrieval')
        return []
      }
    } catch (error) {
      console.error('❌ Failed to fetch inbox messages:', error)
      return []
    }
  }

  // Get messages from public domain
  private static async getPublicInboxMessages(inboxName: string): Promise<MailinatorMessage[]> {
    try {
      const response = await fetch(`https://www.mailinator.com/api/v2/domains/public/inboxes/${inboxName}`)
      
      if (response.ok) {
        const data = await response.json()
        return data.msgs || []
      } else {
        console.error('❌ Failed to fetch public inbox messages:', response.statusText)
        return []
      }
    } catch (error) {
      console.error('❌ Error fetching public inbox messages:', error)
      return []
    }
  }

  // Get messages from private domain
  private static async getPrivateInboxMessages(inboxName: string): Promise<MailinatorMessage[]> {
    try {
      const response = await fetch(
        `https://api.mailinator.com/api/v2/domains/private/inboxes/${inboxName}`,
        {
          headers: {
            'Authorization': this.config.apiToken!
          }
        }
      )
      
      if (response.ok) {
        const data = await response.json()
        return data.msgs || []
      } else {
        console.error('❌ Failed to fetch private inbox messages:', response.statusText)
        return []
      }
    } catch (error) {
      console.error('❌ Error fetching private inbox messages:', error)
      return []
    }
  }

  // Utility functions
  private static extractInboxName(email: string): string {
    const parts = email.split('@')
    return parts[0] || 'demo'
  }

  private static htmlToText(html: string): string {
    // Simple HTML to text conversion
    return html
      .replace(/<[^>]*>/g, '')
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .trim()
  }

  // Generate Mailinator email for testing
  static generateTestEmail(name: string): string {
    const cleanName = name.toLowerCase().replace(/[^a-z0-9]/g, '')
    return `${cleanName}@mailinator.com`
  }

  // Create demo Mailinator account setup
  static setupDemoAccount(): {
    testEmails: string[]
    inboxUrls: string[]
    instructions: string[]
  } {
    const testEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ]

    const inboxUrls = testEmails.map(email => {
      const inboxName = this.extractInboxName(email)
      return `https://www.mailinator.com/v4/public/inboxes.jsp?to=${inboxName}`
    })

    const instructions = [
      '1. 📧 Use any of the test emails above for demo purposes',
      '2. 🌐 Visit the inbox URLs to check received emails',
      '3. 🔄 Emails are automatically deleted after a few hours',
      '4. 🆓 No signup required for public Mailinator inboxes',
      '5. 🔗 Click email links to test the full interview flow'
    ]

    return {
      testEmails,
      inboxUrls,
      instructions
    }
  }
}
