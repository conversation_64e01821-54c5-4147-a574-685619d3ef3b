import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { AIService } from '@/lib/ai-services'
import { EmailService } from '@/lib/email-service'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const {
      interviewId,
      questionId,
      answer,
      questionType,
      questionText,
      expectedSolution,
      testCases
    } = await request.json()

    // Validate required fields
    if (!interviewId || !questionId || !answer || !questionType || !questionText) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Verify interview exists and user has access
    const interview = await prisma.interview.findUnique({
      where: { id: interviewId },
      include: {
        student: true,
        interviewer: true
      }
    })

    if (!interview) {
      return NextResponse.json(
        { message: 'Interview not found' },
        { status: 404 }
      )
    }

    if (
      session.user.role !== 'ADMIN' &&
      interview.studentId !== session.user.id &&
      interview.interviewerId !== session.user.id
    ) {
      return NextResponse.json(
        { message: 'Forbidden' },
        { status: 403 }
      )
    }

    let evaluation
    let score = 0

    try {
      if (questionType === 'CODING') {
        evaluation = await AIService.evaluateCodingAnswer(
          questionText,
          answer,
          expectedSolution,
          testCases
        )
        score = evaluation.score
      } else if (questionType === 'THEORY' || questionType === 'BEHAVIORAL') {
        evaluation = await AIService.evaluateTheoryAnswer(
          questionText,
          answer,
          expectedSolution
        )
        score = evaluation.score
      } else {
        return NextResponse.json(
          { message: 'Invalid question type' },
          { status: 400 }
        )
      }

      // Store the result
      const result = await prisma.result.create({
        data: {
          interviewId,
          studentId: interview.studentId,
          score,
          feedback: evaluation.feedback,
          answers: {
            [questionId]: {
              answer,
              evaluation,
              timestamp: new Date().toISOString()
            }
          },
          timeSpent: 0 // This would be calculated based on actual time tracking
        }
      })

      return NextResponse.json({
        message: 'Answer evaluated successfully',
        evaluation,
        result
      })
    } catch (aiError) {
      console.error('AI evaluation error:', aiError)
      return NextResponse.json(
        { message: 'Error evaluating answer with AI' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Error in AI evaluation:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Generate interview feedback
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { interviewId } = await request.json()

    if (!interviewId) {
      return NextResponse.json(
        { message: 'Interview ID is required' },
        { status: 400 }
      )
    }

    // Get interview with results
    const interview = await prisma.interview.findUnique({
      where: { id: interviewId },
      include: {
        student: true,
        interviewer: true,
        results: true
      }
    })

    if (!interview) {
      return NextResponse.json(
        { message: 'Interview not found' },
        { status: 404 }
      )
    }

    if (
      session.user.role !== 'ADMIN' &&
      interview.studentId !== session.user.id &&
      interview.interviewerId !== session.user.id
    ) {
      return NextResponse.json(
        { message: 'Forbidden' },
        { status: 403 }
      )
    }

    // Calculate overall score
    const totalScore = interview.results.reduce((sum, result) => sum + result.score, 0)
    const averageScore = interview.results.length > 0 ? totalScore / interview.results.length : 0

    // Generate comprehensive feedback using AI
    const overallFeedback = await AIService.generateInterviewFeedback(
      interview.results.map(result => result.answers),
      averageScore
    )

    // Update interview with final status and feedback
    const updatedInterview = await prisma.interview.update({
      where: { id: interviewId },
      data: {
        status: 'COMPLETED',
        notes: overallFeedback
      }
    })

    // Send results email to student
    if (interview.student.email) {
      await EmailService.sendInterviewResults(
        interview.student.email,
        interview.student.name || 'Student',
        {
          score: Math.round(averageScore),
          feedback: overallFeedback,
          recordingUrl: interview.recordingUrl
        }
      )
    }

    return NextResponse.json({
      message: 'Interview completed and feedback generated',
      interview: updatedInterview,
      overallScore: averageScore,
      feedback: overallFeedback
    })
  } catch (error) {
    console.error('Error generating interview feedback:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}
