'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { MainNav } from '@/components/navigation/main-nav'
import { 
  Calendar, 
  Clock, 
  TrendingUp, 
  BookOpen, 
  Video,
  Award,
  BarChart3
} from 'lucide-react'

interface DashboardStats {
  totalInterviews: number
  upcomingInterviews: number
  averageScore: number
  completedInterviews: number
}

export default function StudentDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [stats, setStats] = useState<DashboardStats>({
    totalInterviews: 0,
    upcomingInterviews: 0,
    averageScore: 0,
    completedInterviews: 0
  })
  const [upcomingInterviews, setUpcomingInterviews] = useState([])
  const [recentResults, setRecentResults] = useState([])

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/signin')
      return
    }

    if (session.user.role !== 'STUDENT') {
      router.push('/')
      return
    }

    // Fetch dashboard data
    fetchDashboardData()
  }, [session, status, router])

  const fetchDashboardData = async () => {
    try {
      // This would be replaced with actual API calls
      // For now, using mock data
      setStats({
        totalInterviews: 12,
        upcomingInterviews: 2,
        averageScore: 78,
        completedInterviews: 10
      })

      setUpcomingInterviews([
        {
          id: '1',
          title: 'Frontend Developer Interview',
          scheduledAt: new Date('2024-06-20T10:00:00'),
          duration: 60,
          interviewer: 'Tech Corp'
        },
        {
          id: '2',
          title: 'React Developer Assessment',
          scheduledAt: new Date('2024-06-22T14:00:00'),
          duration: 90,
          interviewer: 'StartupXYZ'
        }
      ])

      setRecentResults([
        {
          id: '1',
          title: 'Backend Developer Interview',
          score: 85,
          completedAt: new Date('2024-06-15T16:00:00'),
          feedback: 'Great problem-solving skills, needs improvement in system design'
        },
        {
          id: '2',
          title: 'Full Stack Assessment',
          score: 72,
          completedAt: new Date('2024-06-12T11:00:00'),
          feedback: 'Good coding skills, work on communication during problem explanation'
        }
      ])
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    }
  }

  if (status === 'loading') {
    return <div>Loading...</div>
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <MainNav />
      
      <div className="py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">
              Welcome back, {session?.user?.name}!
            </h1>
            <p className="text-gray-600 mt-2">
              Here's your interview practice overview
            </p>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Interviews</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalInterviews}</div>
                <p className="text-xs text-muted-foreground">
                  All time interviews
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Upcoming</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.upcomingInterviews}</div>
                <p className="text-xs text-muted-foreground">
                  Scheduled interviews
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Average Score</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.averageScore}%</div>
                <p className="text-xs text-muted-foreground">
                  +5% from last month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Completed</CardTitle>
                <Award className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.completedInterviews}</div>
                <p className="text-xs text-muted-foreground">
                  Finished interviews
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Upcoming Interviews */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="h-5 w-5 mr-2" />
                  Upcoming Interviews
                </CardTitle>
                <CardDescription>
                  Your scheduled interview sessions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {upcomingInterviews.map((interview: any) => (
                    <div key={interview.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h3 className="font-medium">{interview.title}</h3>
                        <p className="text-sm text-gray-600">{interview.interviewer}</p>
                        <p className="text-sm text-gray-500">
                          {interview.scheduledAt.toLocaleDateString()} at {interview.scheduledAt.toLocaleTimeString()}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">{interview.duration} min</p>
                        <Button
                          size="sm"
                          className="mt-2"
                          onClick={() => router.push(`/interview/${interview.id}`)}
                        >
                          <Video className="h-4 w-4 mr-1" />
                          Join
                        </Button>
                      </div>
                    </div>
                  ))}
                  {upcomingInterviews.length === 0 && (
                    <p className="text-gray-500 text-center py-4">
                      No upcoming interviews scheduled
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Recent Results */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2" />
                  Recent Results
                </CardTitle>
                <CardDescription>
                  Your latest interview performance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentResults.map((result: any) => (
                    <div key={result.id} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-medium">{result.title}</h3>
                        <span className={`px-2 py-1 rounded text-sm font-medium ${
                          result.score >= 80 ? 'bg-green-100 text-green-800' :
                          result.score >= 60 ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {result.score}%
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{result.feedback}</p>
                      <p className="text-xs text-gray-500">
                        Completed on {result.completedAt.toLocaleDateString()}
                      </p>
                    </div>
                  ))}
                  {recentResults.length === 0 && (
                    <p className="text-gray-500 text-center py-4">
                      No results available yet
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className="mt-8">
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Common tasks and shortcuts
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button
                    className="h-20 flex flex-col items-center justify-center bg-green-600 hover:bg-green-700"
                    onClick={() => router.push('/interview/demo-practice')}
                  >
                    <Video className="h-6 w-6 mb-2" />
                    Start Practice Interview
                  </Button>
                  <Button
                    variant="outline"
                    className="h-20 flex flex-col items-center justify-center"
                    onClick={() => router.push('/student/interviews')}
                  >
                    <Calendar className="h-6 w-6 mb-2" />
                    View All Interviews
                  </Button>
                  <Button
                    variant="outline"
                    className="h-20 flex flex-col items-center justify-center"
                    onClick={() => router.push('/student/results')}
                  >
                    <BarChart3 className="h-6 w-6 mb-2" />
                    View All Results
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
