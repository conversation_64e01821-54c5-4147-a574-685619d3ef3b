{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 500, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 562, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/components/navigation/main-nav.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { useSession, signOut } from 'next-auth/react'\nimport { Button } from '@/components/ui/button'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'\nimport { \n  User, \n  Settings, \n  LogOut, \n  Calendar, \n  Users, \n  BarChart3, \n  BookOpen,\n  Video,\n  CreditCard\n} from 'lucide-react'\n\nexport function MainNav() {\n  const { data: session, status } = useSession()\n\n  if (status === 'loading') {\n    return <div>Loading...</div>\n  }\n\n  if (!session) {\n    return (\n      <nav className=\"border-b\">\n        <div className=\"flex h-16 items-center px-4\">\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <Video className=\"h-6 w-6\" />\n            <span className=\"font-bold\">Mock Interview</span>\n          </Link>\n          <div className=\"ml-auto flex items-center space-x-4\">\n            <Link href=\"/auth/signin\">\n              <Button variant=\"ghost\">Sign In</Button>\n            </Link>\n            <Link href=\"/auth/signup\">\n              <Button>Sign Up</Button>\n            </Link>\n          </div>\n        </div>\n      </nav>\n    )\n  }\n\n  const getNavItems = () => {\n    const role = session.user.role\n    \n    switch (role) {\n      case 'ADMIN':\n        return [\n          { href: '/admin/dashboard', label: 'Dashboard', icon: BarChart3 },\n          { href: '/admin/tenants', label: 'Tenants', icon: Users },\n          { href: '/admin/users', label: 'Users', icon: User },\n          { href: '/admin/analytics', label: 'Analytics', icon: BarChart3 },\n        ]\n      case 'TENANT':\n        return [\n          { href: '/tenant/dashboard', label: 'Dashboard', icon: BarChart3 },\n          { href: '/tenant/interviews', label: 'Interviews', icon: Calendar },\n          { href: '/tenant/students', label: 'Students', icon: Users },\n          { href: '/tenant/questions', label: 'Questions', icon: BookOpen },\n          { href: '/tenant/subscription', label: 'Subscription', icon: CreditCard },\n        ]\n      case 'STUDENT':\n        return [\n          { href: '/student/dashboard', label: 'Dashboard', icon: BarChart3 },\n          { href: '/student/interviews', label: 'Interviews', icon: Calendar },\n          { href: '/student/results', label: 'Results', icon: BarChart3 },\n        ]\n      default:\n        return []\n    }\n  }\n\n  const navItems = getNavItems()\n\n  return (\n    <nav className=\"border-b\">\n      <div className=\"flex h-16 items-center px-4\">\n        <Link href=\"/\" className=\"flex items-center space-x-2\">\n          <Video className=\"h-6 w-6\" />\n          <span className=\"font-bold\">Mock Interview</span>\n        </Link>\n        \n        <div className=\"ml-6 flex items-center space-x-6\">\n          {navItems.map((item) => {\n            const Icon = item.icon\n            return (\n              <Link\n                key={item.href}\n                href={item.href}\n                className=\"flex items-center space-x-2 text-sm font-medium transition-colors hover:text-primary\"\n              >\n                <Icon className=\"h-4 w-4\" />\n                <span>{item.label}</span>\n              </Link>\n            )\n          })}\n        </div>\n\n        <div className=\"ml-auto flex items-center space-x-4\">\n          <DropdownMenu>\n            <DropdownMenuTrigger asChild>\n              <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n                <Avatar className=\"h-8 w-8\">\n                  <AvatarImage src={session.user.image || ''} alt={session.user.name || ''} />\n                  <AvatarFallback>\n                    {session.user.name?.charAt(0) || session.user.email?.charAt(0)}\n                  </AvatarFallback>\n                </Avatar>\n              </Button>\n            </DropdownMenuTrigger>\n            <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n              <DropdownMenuLabel className=\"font-normal\">\n                <div className=\"flex flex-col space-y-1\">\n                  <p className=\"text-sm font-medium leading-none\">{session.user.name}</p>\n                  <p className=\"text-xs leading-none text-muted-foreground\">\n                    {session.user.email}\n                  </p>\n                  <p className=\"text-xs leading-none text-muted-foreground\">\n                    Role: {session.user.role}\n                  </p>\n                </div>\n              </DropdownMenuLabel>\n              <DropdownMenuSeparator />\n              <DropdownMenuItem asChild>\n                <Link href=\"/profile\" className=\"flex items-center\">\n                  <User className=\"mr-2 h-4 w-4\" />\n                  <span>Profile</span>\n                </Link>\n              </DropdownMenuItem>\n              <DropdownMenuItem asChild>\n                <Link href=\"/settings\" className=\"flex items-center\">\n                  <Settings className=\"mr-2 h-4 w-4\" />\n                  <span>Settings</span>\n                </Link>\n              </DropdownMenuItem>\n              <DropdownMenuSeparator />\n              <DropdownMenuItem\n                className=\"cursor-pointer\"\n                onSelect={() => signOut({ callbackUrl: '/' })}\n              >\n                <LogOut className=\"mr-2 h-4 w-4\" />\n                <span>Log out</span>\n              </DropdownMenuItem>\n            </DropdownMenuContent>\n          </DropdownMenu>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAQA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAdA;;;;;;;AA0BO,SAAS;;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAE3C,IAAI,WAAW,WAAW;QACxB,qBAAO,6LAAC;sBAAI;;;;;;IACd;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAY;;;;;;;;;;;;kCAE9B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;8CAAQ;;;;;;;;;;;0CAE1B,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;8CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMpB;IAEA,MAAM,cAAc;QAClB,MAAM,OAAO,QAAQ,IAAI,CAAC,IAAI;QAE9B,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL;wBAAE,MAAM;wBAAoB,OAAO;wBAAa,MAAM,qNAAA,CAAA,YAAS;oBAAC;oBAChE;wBAAE,MAAM;wBAAkB,OAAO;wBAAW,MAAM,uMAAA,CAAA,QAAK;oBAAC;oBACxD;wBAAE,MAAM;wBAAgB,OAAO;wBAAS,MAAM,qMAAA,CAAA,OAAI;oBAAC;oBACnD;wBAAE,MAAM;wBAAoB,OAAO;wBAAa,MAAM,qNAAA,CAAA,YAAS;oBAAC;iBACjE;YACH,KAAK;gBACH,OAAO;oBACL;wBAAE,MAAM;wBAAqB,OAAO;wBAAa,MAAM,qNAAA,CAAA,YAAS;oBAAC;oBACjE;wBAAE,MAAM;wBAAsB,OAAO;wBAAc,MAAM,6MAAA,CAAA,WAAQ;oBAAC;oBAClE;wBAAE,MAAM;wBAAoB,OAAO;wBAAY,MAAM,uMAAA,CAAA,QAAK;oBAAC;oBAC3D;wBAAE,MAAM;wBAAqB,OAAO;wBAAa,MAAM,iNAAA,CAAA,WAAQ;oBAAC;oBAChE;wBAAE,MAAM;wBAAwB,OAAO;wBAAgB,MAAM,qNAAA,CAAA,aAAU;oBAAC;iBACzE;YACH,KAAK;gBACH,OAAO;oBACL;wBAAE,MAAM;wBAAsB,OAAO;wBAAa,MAAM,qNAAA,CAAA,YAAS;oBAAC;oBAClE;wBAAE,MAAM;wBAAuB,OAAO;wBAAc,MAAM,6MAAA,CAAA,WAAQ;oBAAC;oBACnE;wBAAE,MAAM;wBAAoB,OAAO;wBAAW,MAAM,qNAAA,CAAA,YAAS;oBAAC;iBAC/D;YACH;gBACE,OAAO,EAAE;QACb;IACF;IAEA,MAAM,WAAW;IAEjB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAK;oBAAI,WAAU;;sCACvB,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,6LAAC;4BAAK,WAAU;sCAAY;;;;;;;;;;;;8BAG9B,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC;wBACb,MAAM,OAAO,KAAK,IAAI;wBACtB,qBACE,6LAAC,+JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAU;;8CAEV,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;8CAAM,KAAK,KAAK;;;;;;;2BALZ,KAAK,IAAI;;;;;oBAQpB;;;;;;8BAGF,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+IAAA,CAAA,eAAY;;0CACX,6LAAC,+IAAA,CAAA,sBAAmB;gCAAC,OAAO;0CAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,WAAU;8CAChC,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,6LAAC,qIAAA,CAAA,cAAW;gDAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI;gDAAI,KAAK,QAAQ,IAAI,CAAC,IAAI,IAAI;;;;;;0DACtE,6LAAC,qIAAA,CAAA,iBAAc;0DACZ,QAAQ,IAAI,CAAC,IAAI,EAAE,OAAO,MAAM,QAAQ,IAAI,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;0CAKpE,6LAAC,+IAAA,CAAA,sBAAmB;gCAAC,WAAU;gCAAO,OAAM;gCAAM,UAAU;;kDAC1D,6LAAC,+IAAA,CAAA,oBAAiB;wCAAC,WAAU;kDAC3B,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC,QAAQ,IAAI,CAAC,IAAI;;;;;;8DAClE,6LAAC;oDAAE,WAAU;8DACV,QAAQ,IAAI,CAAC,KAAK;;;;;;8DAErB,6LAAC;oDAAE,WAAU;;wDAA6C;wDACjD,QAAQ,IAAI,CAAC,IAAI;;;;;;;;;;;;;;;;;;kDAI9B,6LAAC,+IAAA,CAAA,wBAAqB;;;;;kDACtB,6LAAC,+IAAA,CAAA,mBAAgB;wCAAC,OAAO;kDACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;;8DAC9B,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;kDAGV,6LAAC,+IAAA,CAAA,mBAAgB;wCAAC,OAAO;kDACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAY,WAAU;;8DAC/B,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;kDAGV,6LAAC,+IAAA,CAAA,wBAAqB;;;;;kDACtB,6LAAC,+IAAA,CAAA,mBAAgB;wCACf,WAAU;wCACV,UAAU,IAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;gDAAE,aAAa;4CAAI;;0DAE3C,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB;GAvIgB;;QACoB,iJAAA,CAAA,aAAU;;;KAD9B", "debugId": null}}, {"offset": {"line": 1058, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/app/student/dashboard/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useSession } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport { useEffect, useState } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { MainNav } from '@/components/navigation/main-nav'\nimport { \n  Calendar, \n  Clock, \n  TrendingUp, \n  BookOpen, \n  Video,\n  Award,\n  BarChart3\n} from 'lucide-react'\n\ninterface DashboardStats {\n  totalInterviews: number\n  upcomingInterviews: number\n  averageScore: number\n  completedInterviews: number\n}\n\nexport default function StudentDashboard() {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n  const [stats, setStats] = useState<DashboardStats>({\n    totalInterviews: 0,\n    upcomingInterviews: 0,\n    averageScore: 0,\n    completedInterviews: 0\n  })\n  const [upcomingInterviews, setUpcomingInterviews] = useState([])\n  const [recentResults, setRecentResults] = useState([])\n\n  useEffect(() => {\n    if (status === 'loading') return\n    \n    if (!session) {\n      router.push('/auth/signin')\n      return\n    }\n\n    if (session.user.role !== 'STUDENT') {\n      router.push('/')\n      return\n    }\n\n    // Fetch dashboard data\n    fetchDashboardData()\n  }, [session, status, router])\n\n  const fetchDashboardData = async () => {\n    try {\n      // This would be replaced with actual API calls\n      // For now, using mock data\n      setStats({\n        totalInterviews: 12,\n        upcomingInterviews: 2,\n        averageScore: 78,\n        completedInterviews: 10\n      })\n\n      setUpcomingInterviews([\n        {\n          id: '1',\n          title: 'Frontend Developer Interview',\n          scheduledAt: new Date('2024-06-20T10:00:00'),\n          duration: 60,\n          interviewer: 'Tech Corp'\n        },\n        {\n          id: '2',\n          title: 'React Developer Assessment',\n          scheduledAt: new Date('2024-06-22T14:00:00'),\n          duration: 90,\n          interviewer: 'StartupXYZ'\n        }\n      ])\n\n      setRecentResults([\n        {\n          id: '1',\n          title: 'Backend Developer Interview',\n          score: 85,\n          completedAt: new Date('2024-06-15T16:00:00'),\n          feedback: 'Great problem-solving skills, needs improvement in system design'\n        },\n        {\n          id: '2',\n          title: 'Full Stack Assessment',\n          score: 72,\n          completedAt: new Date('2024-06-12T11:00:00'),\n          feedback: 'Good coding skills, work on communication during problem explanation'\n        }\n      ])\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error)\n    }\n  }\n\n  if (status === 'loading') {\n    return <div>Loading...</div>\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <MainNav />\n      \n      <div className=\"py-8 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"mb-8\">\n            <h1 className=\"text-3xl font-bold text-gray-900\">\n              Welcome back, {session?.user?.name}!\n            </h1>\n            <p className=\"text-gray-600 mt-2\">\n              Here's your interview practice overview\n            </p>\n          </div>\n\n          {/* Stats Cards */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">Total Interviews</CardTitle>\n                <Calendar className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">{stats.totalInterviews}</div>\n                <p className=\"text-xs text-muted-foreground\">\n                  All time interviews\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">Upcoming</CardTitle>\n                <Clock className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">{stats.upcomingInterviews}</div>\n                <p className=\"text-xs text-muted-foreground\">\n                  Scheduled interviews\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">Average Score</CardTitle>\n                <TrendingUp className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">{stats.averageScore}%</div>\n                <p className=\"text-xs text-muted-foreground\">\n                  +5% from last month\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">Completed</CardTitle>\n                <Award className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">{stats.completedInterviews}</div>\n                <p className=\"text-xs text-muted-foreground\">\n                  Finished interviews\n                </p>\n              </CardContent>\n            </Card>\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n            {/* Upcoming Interviews */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <Calendar className=\"h-5 w-5 mr-2\" />\n                  Upcoming Interviews\n                </CardTitle>\n                <CardDescription>\n                  Your scheduled interview sessions\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  {upcomingInterviews.map((interview: any) => (\n                    <div key={interview.id} className=\"flex items-center justify-between p-4 border rounded-lg\">\n                      <div>\n                        <h3 className=\"font-medium\">{interview.title}</h3>\n                        <p className=\"text-sm text-gray-600\">{interview.interviewer}</p>\n                        <p className=\"text-sm text-gray-500\">\n                          {interview.scheduledAt.toLocaleDateString()} at {interview.scheduledAt.toLocaleTimeString()}\n                        </p>\n                      </div>\n                      <div className=\"text-right\">\n                        <p className=\"text-sm font-medium\">{interview.duration} min</p>\n                        <Button\n                          size=\"sm\"\n                          className=\"mt-2\"\n                          onClick={() => router.push(`/interview/${interview.id}`)}\n                        >\n                          <Video className=\"h-4 w-4 mr-1\" />\n                          Join\n                        </Button>\n                      </div>\n                    </div>\n                  ))}\n                  {upcomingInterviews.length === 0 && (\n                    <p className=\"text-gray-500 text-center py-4\">\n                      No upcoming interviews scheduled\n                    </p>\n                  )}\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Recent Results */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <BarChart3 className=\"h-5 w-5 mr-2\" />\n                  Recent Results\n                </CardTitle>\n                <CardDescription>\n                  Your latest interview performance\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  {recentResults.map((result: any) => (\n                    <div key={result.id} className=\"p-4 border rounded-lg\">\n                      <div className=\"flex items-center justify-between mb-2\">\n                        <h3 className=\"font-medium\">{result.title}</h3>\n                        <span className={`px-2 py-1 rounded text-sm font-medium ${\n                          result.score >= 80 ? 'bg-green-100 text-green-800' :\n                          result.score >= 60 ? 'bg-yellow-100 text-yellow-800' :\n                          'bg-red-100 text-red-800'\n                        }`}>\n                          {result.score}%\n                        </span>\n                      </div>\n                      <p className=\"text-sm text-gray-600 mb-2\">{result.feedback}</p>\n                      <p className=\"text-xs text-gray-500\">\n                        Completed on {result.completedAt.toLocaleDateString()}\n                      </p>\n                    </div>\n                  ))}\n                  {recentResults.length === 0 && (\n                    <p className=\"text-gray-500 text-center py-4\">\n                      No results available yet\n                    </p>\n                  )}\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Quick Actions */}\n          <div className=\"mt-8\">\n            <Card>\n              <CardHeader>\n                <CardTitle>Quick Actions</CardTitle>\n                <CardDescription>\n                  Common tasks and shortcuts\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <Button\n                    className=\"h-20 flex flex-col items-center justify-center bg-green-600 hover:bg-green-700\"\n                    onClick={() => router.push('/interview/demo-practice')}\n                  >\n                    <Video className=\"h-6 w-6 mb-2\" />\n                    Start Practice Interview\n                  </Button>\n                  <Button\n                    variant=\"outline\"\n                    className=\"h-20 flex flex-col items-center justify-center\"\n                    onClick={() => router.push('/student/interviews')}\n                  >\n                    <Calendar className=\"h-6 w-6 mb-2\" />\n                    View All Interviews\n                  </Button>\n                  <Button\n                    variant=\"outline\"\n                    className=\"h-20 flex flex-col items-center justify-center\"\n                    onClick={() => router.push('/student/results')}\n                  >\n                    <BarChart3 className=\"h-6 w-6 mb-2\" />\n                    View All Results\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AAyBe,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;QACjD,iBAAiB;QACjB,oBAAoB;QACpB,cAAc;QACd,qBAAqB;IACvB;IACA,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC/D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,WAAW,WAAW;YAE1B,IAAI,CAAC,SAAS;gBACZ,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,WAAW;gBACnC,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,uBAAuB;YACvB;QACF;qCAAG;QAAC;QAAS;QAAQ;KAAO;IAE5B,MAAM,qBAAqB;QACzB,IAAI;YACF,+CAA+C;YAC/C,2BAA2B;YAC3B,SAAS;gBACP,iBAAiB;gBACjB,oBAAoB;gBACpB,cAAc;gBACd,qBAAqB;YACvB;YAEA,sBAAsB;gBACpB;oBACE,IAAI;oBACJ,OAAO;oBACP,aAAa,IAAI,KAAK;oBACtB,UAAU;oBACV,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,aAAa,IAAI,KAAK;oBACtB,UAAU;oBACV,aAAa;gBACf;aACD;YAED,iBAAiB;gBACf;oBACE,IAAI;oBACJ,OAAO;oBACP,OAAO;oBACP,aAAa,IAAI,KAAK;oBACtB,UAAU;gBACZ;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,OAAO;oBACP,aAAa,IAAI,KAAK;oBACtB,UAAU;gBACZ;aACD;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,IAAI,WAAW,WAAW;QACxB,qBAAO,6LAAC;sBAAI;;;;;;IACd;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,kJAAA,CAAA,UAAO;;;;;0BAER,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;wCAAmC;wCAChC,SAAS,MAAM;wCAAK;;;;;;;8CAErC,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;;;;;;;sCAMpC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsB;;;;;;8DAC3C,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;sDAEtB,6LAAC,mIAAA,CAAA,cAAW;;8DACV,6LAAC;oDAAI,WAAU;8DAAsB,MAAM,eAAe;;;;;;8DAC1D,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAMjD,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsB;;;;;;8DAC3C,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;sDAEnB,6LAAC,mIAAA,CAAA,cAAW;;8DACV,6LAAC;oDAAI,WAAU;8DAAsB,MAAM,kBAAkB;;;;;;8DAC7D,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAMjD,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsB;;;;;;8DAC3C,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;sDAExB,6LAAC,mIAAA,CAAA,cAAW;;8DACV,6LAAC;oDAAI,WAAU;;wDAAsB,MAAM,YAAY;wDAAC;;;;;;;8DACxD,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAMjD,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsB;;;;;;8DAC3C,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;sDAEnB,6LAAC,mIAAA,CAAA,cAAW;;8DACV,6LAAC;oDAAI,WAAU;8DAAsB,MAAM,mBAAmB;;;;;;8DAC9D,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;sCAOnD,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGvC,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC;gDAAI,WAAU;;oDACZ,mBAAmB,GAAG,CAAC,CAAC,0BACvB,6LAAC;4DAAuB,WAAU;;8EAChC,6LAAC;;sFACC,6LAAC;4EAAG,WAAU;sFAAe,UAAU,KAAK;;;;;;sFAC5C,6LAAC;4EAAE,WAAU;sFAAyB,UAAU,WAAW;;;;;;sFAC3D,6LAAC;4EAAE,WAAU;;gFACV,UAAU,WAAW,CAAC,kBAAkB;gFAAG;gFAAK,UAAU,WAAW,CAAC,kBAAkB;;;;;;;;;;;;;8EAG7F,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;;gFAAuB,UAAU,QAAQ;gFAAC;;;;;;;sFACvD,6LAAC,qIAAA,CAAA,SAAM;4EACL,MAAK;4EACL,WAAU;4EACV,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,UAAU,EAAE,EAAE;;8FAEvD,6LAAC,uMAAA,CAAA,QAAK;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;;;;;;;;2DAf9B,UAAU,EAAE;;;;;oDAqBvB,mBAAmB,MAAM,KAAK,mBAC7B,6LAAC;wDAAE,WAAU;kEAAiC;;;;;;;;;;;;;;;;;;;;;;;8CAStD,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,qNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGxC,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC;gDAAI,WAAU;;oDACZ,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC;4DAAoB,WAAU;;8EAC7B,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFAAe,OAAO,KAAK;;;;;;sFACzC,6LAAC;4EAAK,WAAW,CAAC,sCAAsC,EACtD,OAAO,KAAK,IAAI,KAAK,gCACrB,OAAO,KAAK,IAAI,KAAK,kCACrB,2BACA;;gFACC,OAAO,KAAK;gFAAC;;;;;;;;;;;;;8EAGlB,6LAAC;oEAAE,WAAU;8EAA8B,OAAO,QAAQ;;;;;;8EAC1D,6LAAC;oEAAE,WAAU;;wEAAwB;wEACrB,OAAO,WAAW,CAAC,kBAAkB;;;;;;;;2DAb7C,OAAO,EAAE;;;;;oDAiBpB,cAAc,MAAM,KAAK,mBACxB,6LAAC;wDAAE,WAAU;kEAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUxD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC,mIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,WAAU;oDACV,SAAS,IAAM,OAAO,IAAI,CAAC;;sEAE3B,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGpC,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS,IAAM,OAAO,IAAI,CAAC;;sEAE3B,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGvC,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS,IAAM,OAAO,IAAI,CAAC;;sEAE3B,6LAAC,qNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW1D;GAxRwB;;QACY,iJAAA,CAAA,aAAU;QAC7B,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}