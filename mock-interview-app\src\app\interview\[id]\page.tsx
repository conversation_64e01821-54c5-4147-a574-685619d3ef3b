'use client'

import { useSession } from 'next-auth/react'
import { useRouter, useParams } from 'next/navigation'
import { useEffect, useState, useRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Video, 
  VideoOff, 
  Mic, 
  MicOff, 
  Monitor, 
  MonitorOff,
  Phone,
  Clock,
  Send,
  Code,
  MessageSquare
} from 'lucide-react'

interface InterviewData {
  id: string
  title: string
  duration: number
  questions: any[]
  startTime: Date
}

export default function InterviewRoom() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const params = useParams()
  const [interview, setInterview] = useState<InterviewData | null>(null)
  const [isVideoOn, setIsVideoOn] = useState(true)
  const [isAudioOn, setIsAudioOn] = useState(true)
  const [isScreenSharing, setIsScreenSharing] = useState(false)
  const [timeElapsed, setTimeElapsed] = useState(0)
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [code, setCode] = useState('')
  const [notes, setNotes] = useState('')
  
  const localVideoRef = useRef<HTMLVideoElement>(null)
  const remoteVideoRef = useRef<HTMLVideoElement>(null)

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/signin')
      return
    }

    // Initialize interview data
    fetchInterviewData()
    initializeMedia()
    startTimer()
  }, [session, status, router, params.id])

  const fetchInterviewData = async () => {
    try {
      const interviewId = params.id as string

      // Demo interview data based on ID
      const demoInterviews = {
        'demo-practice': {
          id: 'demo-practice',
          title: 'Practice Interview - Frontend Developer',
          duration: 30,
          questions: [
            {
              id: 1,
              type: 'CODING',
              title: 'Two Sum Problem',
              description: 'Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target.\n\nExample:\nInput: nums = [2,7,11,15], target = 9\nOutput: [0,1]\nExplanation: Because nums[0] + nums[1] == 9, we return [0, 1].\n\nConstraints:\n- 2 <= nums.length <= 10^4\n- -10^9 <= nums[i] <= 10^9\n- -10^9 <= target <= 10^9\n- Only one valid answer exists.',
              timeLimit: 15
            },
            {
              id: 2,
              type: 'THEORY',
              title: 'React Hooks Explanation',
              description: 'Explain the difference between useState and useEffect hooks in React. Provide examples of when you would use each one and discuss their lifecycle.',
              timeLimit: 10
            },
            {
              id: 3,
              type: 'BEHAVIORAL',
              title: 'Problem Solving Experience',
              description: 'Tell me about a challenging technical problem you solved recently. What was your approach and what did you learn?',
              timeLimit: 5
            }
          ],
          startTime: new Date()
        },
        'demo-1': {
          id: 'demo-1',
          title: 'Frontend Developer Interview',
          duration: 60,
          questions: [
            {
              id: 1,
              type: 'CODING',
              title: 'Array Manipulation',
              description: 'Write a function that removes duplicates from an array while maintaining the original order.',
              timeLimit: 20
            },
            {
              id: 2,
              type: 'THEORY',
              title: 'CSS Flexbox',
              description: 'Explain how CSS Flexbox works and provide examples of common use cases.',
              timeLimit: 15
            }
          ],
          startTime: new Date()
        },
        'demo-2': {
          id: 'demo-2',
          title: 'React Developer Assessment',
          duration: 90,
          questions: [
            {
              id: 1,
              type: 'CODING',
              title: 'React Component Optimization',
              description: 'Create a React component that efficiently renders a large list of items with search functionality.',
              timeLimit: 45
            },
            {
              id: 2,
              type: 'THEORY',
              title: 'State Management',
              description: 'Compare different state management solutions in React (Context, Redux, Zustand). When would you use each?',
              timeLimit: 20
            }
          ],
          startTime: new Date()
        }
      }

      const interviewData = demoInterviews[interviewId as keyof typeof demoInterviews] || demoInterviews['demo-practice']
      setInterview(interviewData)
    } catch (error) {
      console.error('Error fetching interview data:', error)
    }
  }

  const initializeMedia = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: true, 
        audio: true 
      })
      
      if (localVideoRef.current) {
        localVideoRef.current.srcObject = stream
      }
    } catch (error) {
      console.error('Error accessing media devices:', error)
    }
  }

  const startTimer = () => {
    const interval = setInterval(() => {
      setTimeElapsed(prev => prev + 1)
    }, 1000)

    return () => clearInterval(interval)
  }

  const toggleVideo = () => {
    setIsVideoOn(!isVideoOn)
    // Implement actual video toggle logic
  }

  const toggleAudio = () => {
    setIsAudioOn(!isAudioOn)
    // Implement actual audio toggle logic
  }

  const toggleScreenShare = () => {
    setIsScreenSharing(!isScreenSharing)
    // Implement screen sharing logic
  }

  const endInterview = () => {
    // Show completion message and redirect
    const confirmed = window.confirm('Are you sure you want to end the interview? Your progress will be saved.')
    if (confirmed) {
      // In a real app, this would save the interview data
      alert('Interview completed! Check your results in the Results section.')
      router.push('/student/results')
    }
  }

  const nextQuestion = () => {
    if (interview && currentQuestion < interview.questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1)
      setCode('')
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  if (status === 'loading' || !interview) {
    return <div>Loading interview...</div>
  }

  const question = interview.questions[currentQuestion]

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <div className="bg-gray-800 p-4 flex justify-between items-center">
        <div>
          <h1 className="text-xl font-bold">{interview.title}</h1>
          <p className="text-gray-400">Question {currentQuestion + 1} of {interview.questions.length}</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4" />
            <span>{formatTime(timeElapsed)}</span>
          </div>
          <Button variant="destructive" onClick={endInterview}>
            <Phone className="h-4 w-4 mr-2" />
            End Interview
          </Button>
        </div>
      </div>

      <div className="flex h-[calc(100vh-80px)]">
        {/* Video Section */}
        <div className="w-1/3 p-4 space-y-4">
          {/* Remote Video */}
          <Card className="bg-gray-800 border-gray-700">
            <CardContent className="p-4">
              <div className="aspect-video bg-gray-700 rounded-lg flex items-center justify-center mb-2">
                <video
                  ref={remoteVideoRef}
                  className="w-full h-full object-cover rounded-lg"
                  autoPlay
                  playsInline
                />
                <div className="text-gray-400">Interviewer</div>
              </div>
            </CardContent>
          </Card>

          {/* Local Video */}
          <Card className="bg-gray-800 border-gray-700">
            <CardContent className="p-4">
              <div className="aspect-video bg-gray-700 rounded-lg flex items-center justify-center mb-2">
                <video
                  ref={localVideoRef}
                  className="w-full h-full object-cover rounded-lg"
                  autoPlay
                  playsInline
                  muted
                />
              </div>
              
              {/* Controls */}
              <div className="flex justify-center space-x-2">
                <Button
                  size="sm"
                  variant={isVideoOn ? "default" : "destructive"}
                  onClick={toggleVideo}
                >
                  {isVideoOn ? <Video className="h-4 w-4" /> : <VideoOff className="h-4 w-4" />}
                </Button>
                <Button
                  size="sm"
                  variant={isAudioOn ? "default" : "destructive"}
                  onClick={toggleAudio}
                >
                  {isAudioOn ? <Mic className="h-4 w-4" /> : <MicOff className="h-4 w-4" />}
                </Button>
                <Button
                  size="sm"
                  variant={isScreenSharing ? "default" : "outline"}
                  onClick={toggleScreenShare}
                >
                  {isScreenSharing ? <MonitorOff className="h-4 w-4" /> : <Monitor className="h-4 w-4" />}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-4">
          <Card className="h-full bg-gray-800 border-gray-700">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>{question?.title}</span>
                <div className="flex space-x-2">
                  {currentQuestion > 0 && (
                    <Button variant="outline" size="sm">
                      Previous
                    </Button>
                  )}
                  {currentQuestion < interview.questions.length - 1 && (
                    <Button size="sm" onClick={nextQuestion}>
                      Next Question
                    </Button>
                  )}
                </div>
              </CardTitle>
              <CardDescription className="text-gray-300">
                {question?.description}
              </CardDescription>
            </CardHeader>
            <CardContent className="flex-1">
              <Tabs defaultValue="question" className="h-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="question">Question</TabsTrigger>
                  <TabsTrigger value="code">Code Editor</TabsTrigger>
                  <TabsTrigger value="notes">Notes</TabsTrigger>
                </TabsList>
                
                <TabsContent value="question" className="mt-4">
                  <div className="bg-gray-700 p-4 rounded-lg">
                    <h3 className="font-semibold mb-2">Question Details</h3>
                    <p className="text-gray-300 mb-4">{question?.description}</p>
                    <div className="flex items-center space-x-4 text-sm text-gray-400">
                      <span>Type: {question?.type}</span>
                      <span>Time Limit: {question?.timeLimit} minutes</span>
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="code" className="mt-4">
                  <div className="h-96">
                    <textarea
                      className="w-full h-full bg-gray-700 text-white p-4 rounded-lg font-mono text-sm resize-none"
                      placeholder="Write your code here..."
                      value={code}
                      onChange={(e) => setCode(e.target.value)}
                    />
                  </div>
                </TabsContent>
                
                <TabsContent value="notes" className="mt-4">
                  <div className="h-96">
                    <textarea
                      className="w-full h-full bg-gray-700 text-white p-4 rounded-lg resize-none"
                      placeholder="Take notes here..."
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                    />
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        {/* Chat Section */}
        <div className="w-80 p-4">
          <Card className="h-full bg-gray-800 border-gray-700">
            <CardHeader>
              <CardTitle className="flex items-center">
                <MessageSquare className="h-5 w-5 mr-2" />
                Chat
              </CardTitle>
            </CardHeader>
            <CardContent className="flex flex-col h-full">
              <div className="flex-1 bg-gray-700 rounded-lg p-4 mb-4 overflow-y-auto">
                {/* Chat messages would go here */}
                <div className="text-gray-400 text-center">
                  Chat with your interviewer
                </div>
              </div>
              <div className="flex space-x-2">
                <input
                  type="text"
                  placeholder="Type a message..."
                  className="flex-1 bg-gray-700 text-white px-3 py-2 rounded-lg text-sm"
                />
                <Button size="sm">
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
