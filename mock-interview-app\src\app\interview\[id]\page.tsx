'use client'

import { useSession } from 'next-auth/react'
import { useRouter, useParams } from 'next/navigation'
import { useEffect, useState, useRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Video,
  VideoOff,
  Mic,
  MicOff,
  Monitor,
  MonitorOff,
  Phone,
  Clock,
  Send,
  Code,
  MessageSquare,
  Square,
  RotateCcw
} from 'lucide-react'

interface InterviewData {
  id: string
  title: string
  duration: number
  questions: any[]
  startTime: Date
}

export default function InterviewRoom() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const params = useParams()
  const [interview, setInterview] = useState<InterviewData | null>(null)
  const [isVideoOn, setIsVideoOn] = useState(true)
  const [isAudioOn, setIsAudioOn] = useState(true)
  const [isScreenSharing, setIsScreenSharing] = useState(false)
  const [timeElapsed, setTimeElapsed] = useState(0)
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [code, setCode] = useState('')
  const [notes, setNotes] = useState('')
  const [isInterviewerSpeaking, setIsInterviewerSpeaking] = useState(false)
  const [isListening, setIsListening] = useState(false)
  const [studentResponse, setStudentResponse] = useState('')
  const [interviewerMessage, setInterviewerMessage] = useState('')
  
  const localVideoRef = useRef<HTMLVideoElement>(null)
  const remoteVideoRef = useRef<HTMLVideoElement>(null)

  useEffect(() => {
    if (status === 'loading') return

    if (!session) {
      router.push('/auth/signin')
      return
    }

    // Initialize interview data
    fetchInterviewData()
    initializeMedia()
    startTimer()
  }, [session, status, router, params.id])

  // Start interviewer introduction when interview loads
  useEffect(() => {
    if (interview) {
      setTimeout(() => {
        startInterviewerIntroduction()
      }, 3000) // Wait 3 seconds after interview loads
    }
  }, [interview])

  const startFirstQuestion = () => {
    askCurrentQuestion()
  }

  const fetchInterviewData = async () => {
    try {
      const interviewId = params.id as string

      // Demo interview data based on ID
      const demoInterviews = {
        'demo-practice': {
          id: 'demo-practice',
          title: 'Practice Interview - Frontend Developer',
          duration: 30,
          questions: [
            {
              id: 1,
              type: 'CODING',
              title: 'Two Sum Problem',
              description: 'Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target.\n\nExample:\nInput: nums = [2,7,11,15], target = 9\nOutput: [0,1]\nExplanation: Because nums[0] + nums[1] == 9, we return [0, 1].\n\nConstraints:\n- 2 <= nums.length <= 10^4\n- -10^9 <= nums[i] <= 10^9\n- -10^9 <= target <= 10^9\n- Only one valid answer exists.',
              timeLimit: 15
            },
            {
              id: 2,
              type: 'THEORY',
              title: 'React Hooks Explanation',
              description: 'Explain the difference between useState and useEffect hooks in React. Provide examples of when you would use each one and discuss their lifecycle.',
              timeLimit: 10
            },
            {
              id: 3,
              type: 'BEHAVIORAL',
              title: 'Problem Solving Experience',
              description: 'Tell me about a challenging technical problem you solved recently. What was your approach and what did you learn?',
              timeLimit: 5
            }
          ],
          startTime: new Date()
        },
        'demo-1': {
          id: 'demo-1',
          title: 'Frontend Developer Interview',
          duration: 60,
          questions: [
            {
              id: 1,
              type: 'CODING',
              title: 'Array Manipulation',
              description: 'Write a function that removes duplicates from an array while maintaining the original order.',
              timeLimit: 20
            },
            {
              id: 2,
              type: 'THEORY',
              title: 'CSS Flexbox',
              description: 'Explain how CSS Flexbox works and provide examples of common use cases.',
              timeLimit: 15
            }
          ],
          startTime: new Date()
        },
        'demo-2': {
          id: 'demo-2',
          title: 'React Developer Assessment',
          duration: 90,
          questions: [
            {
              id: 1,
              type: 'CODING',
              title: 'React Component Optimization',
              description: 'Create a React component that efficiently renders a large list of items with search functionality.',
              timeLimit: 45
            },
            {
              id: 2,
              type: 'THEORY',
              title: 'State Management',
              description: 'Compare different state management solutions in React (Context, Redux, Zustand). When would you use each?',
              timeLimit: 20
            }
          ],
          startTime: new Date()
        }
      }

      const interviewData = demoInterviews[interviewId as keyof typeof demoInterviews] || demoInterviews['demo-practice']
      setInterview(interviewData)
    } catch (error) {
      console.error('Error fetching interview data:', error)
    }
  }

  const initializeMedia = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: true, 
        audio: true 
      })
      
      if (localVideoRef.current) {
        localVideoRef.current.srcObject = stream
      }
    } catch (error) {
      console.error('Error accessing media devices:', error)
    }
  }

  const startTimer = () => {
    const interval = setInterval(() => {
      setTimeElapsed(prev => prev + 1)
    }, 1000)

    return () => clearInterval(interval)
  }

  const toggleVideo = () => {
    setIsVideoOn(!isVideoOn)
    // Implement actual video toggle logic
  }

  const toggleAudio = () => {
    setIsAudioOn(!isAudioOn)
    // Implement actual audio toggle logic
  }

  const toggleScreenShare = () => {
    setIsScreenSharing(!isScreenSharing)
    // Implement screen sharing logic
  }

  const endInterview = () => {
    // Show completion message and redirect
    const confirmed = window.confirm('Are you sure you want to end the interview? Your progress will be saved.')
    if (confirmed) {
      // In a real app, this would save the interview data
      alert('Interview completed! Check your results in the Results section.')
      router.push('/student/results')
    }
  }

  const nextQuestion = () => {
    if (interview && currentQuestion < interview.questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1)
      setCode('')
      // Interviewer asks the next question
      setTimeout(() => askCurrentQuestion(), 1000)
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  // Interviewer Speech Functions
  const startInterviewerIntroduction = () => {
    const introMessage = `Hello! Welcome to your interview session. I'm your AI interviewer today. We'll be going through ${interview?.questions.length} questions covering different areas. Click "Start First Question" when you're ready to begin!`
    speakInterviewerMessage(introMessage)
    setInterviewerMessage(introMessage)
  }

  const askCurrentQuestion = () => {
    if (interview && interview.questions[currentQuestion]) {
      const question = interview.questions[currentQuestion]
      const questionMessage = `Question ${currentQuestion + 1}: ${question.title}. ${question.description}. You have ${question.timeLimit} minutes for this question. Please take your time to think and provide your answer.`
      speakInterviewerMessage(questionMessage)
      setInterviewerMessage(questionMessage)
    }
  }

  const speakInterviewerMessage = (message: string) => {
    setIsInterviewerSpeaking(true)

    // Use Web Speech API for text-to-speech
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(message)
      utterance.rate = 0.9
      utterance.pitch = 1
      utterance.volume = 0.8

      // Try to use a professional voice
      const voices = speechSynthesis.getVoices()
      const preferredVoice = voices.find(voice =>
        voice.name.includes('Google') ||
        voice.name.includes('Microsoft') ||
        voice.lang.includes('en-US')
      )
      if (preferredVoice) {
        utterance.voice = preferredVoice
      }

      utterance.onend = () => {
        setIsInterviewerSpeaking(false)
        setIsListening(true)
      }

      speechSynthesis.speak(utterance)
    } else {
      // Fallback for browsers without speech synthesis
      setTimeout(() => {
        setIsInterviewerSpeaking(false)
        setIsListening(true)
      }, 3000)
    }
  }

  const startListening = () => {
    setIsListening(true)
    setTimeout(() => {
      setIsListening(false)
    }, 30000) // Stop listening after 30 seconds
  }

  const stopListening = () => {
    setIsListening(false)
  }

  if (status === 'loading' || !interview) {
    return <div>Loading interview...</div>
  }

  const question = interview.questions[currentQuestion]

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <div className="bg-gray-800 p-4 flex justify-between items-center">
        <div>
          <h1 className="text-xl font-bold">{interview.title}</h1>
          <p className="text-gray-400">Question {currentQuestion + 1} of {interview.questions.length}</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4" />
            <span>{formatTime(timeElapsed)}</span>
          </div>
          <Button variant="destructive" onClick={endInterview}>
            <Phone className="h-4 w-4 mr-2" />
            End Interview
          </Button>
        </div>
      </div>

      <div className="flex h-[calc(100vh-80px)]">
        {/* Video Section */}
        <div className="w-1/3 p-4 space-y-4">
          {/* AI Interviewer Avatar */}
          <Card className="bg-gray-800 border-gray-700">
            <CardContent className="p-4">
              <div className="aspect-video bg-gradient-to-br from-blue-900 to-purple-900 rounded-lg flex items-center justify-center mb-2 relative overflow-hidden">
                {/* Animated Avatar */}
                <div className={`relative transition-all duration-500 ${isInterviewerSpeaking ? 'scale-110' : 'scale-100'}`}>
                  {/* Avatar Face */}
                  <div className="w-32 h-32 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center relative">
                    {/* Eyes */}
                    <div className="absolute top-8 left-8 w-3 h-3 bg-white rounded-full"></div>
                    <div className="absolute top-8 right-8 w-3 h-3 bg-white rounded-full"></div>

                    {/* Mouth - animated when speaking */}
                    <div className={`absolute bottom-8 left-1/2 transform -translate-x-1/2 transition-all duration-300 ${
                      isInterviewerSpeaking
                        ? 'w-8 h-4 bg-white rounded-full animate-pulse'
                        : 'w-6 h-2 bg-white rounded-full'
                    }`}></div>

                    {/* Speaking indicator */}
                    {isInterviewerSpeaking && (
                      <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-green-400 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-green-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                          <div className="w-2 h-2 bg-green-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Listening indicator */}
                  {isListening && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <div className="flex items-center space-x-2 bg-red-500 px-3 py-1 rounded-full text-xs">
                        <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                        <span>Listening...</span>
                      </div>
                    </div>
                  )}
                </div>

                {/* Background animation */}
                <div className="absolute inset-0 opacity-20">
                  <div className={`w-full h-full rounded-lg transition-all duration-1000 ${
                    isInterviewerSpeaking
                      ? 'bg-gradient-to-r from-green-400 to-blue-500'
                      : 'bg-gradient-to-r from-blue-600 to-purple-600'
                  }`}></div>
                </div>
              </div>
              <p className="text-sm text-gray-400 text-center font-medium">
                AI Interviewer {isInterviewerSpeaking ? '🗣️' : isListening ? '👂' : '💭'}
              </p>
            </CardContent>
          </Card>

          {/* Interviewer Speech Bubble */}
          {interviewerMessage && (
            <Card className="bg-blue-900 border-blue-700">
              <CardContent className="p-4">
                <div className="relative">
                  <div className="bg-blue-800 p-3 rounded-lg text-sm">
                    <p className="text-blue-100">{interviewerMessage}</p>
                  </div>
                  <div className="absolute -top-2 left-4 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-blue-800"></div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Student Response Controls */}
          <Card className="bg-gray-800 border-gray-700">
            <CardContent className="p-4">
              <h3 className="text-sm font-medium mb-3">Your Response</h3>

              {/* Voice Response */}
              <div className="space-y-3">
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    onClick={startListening}
                    disabled={isListening || isInterviewerSpeaking}
                    className={isListening ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'}
                  >
                    {isListening ? (
                      <>
                        <Square className="h-4 w-4 mr-2" />
                        Stop Recording
                      </>
                    ) : (
                      <>
                        <Mic className="h-4 w-4 mr-2" />
                        Start Speaking
                      </>
                    )}
                  </Button>

                  <Button
                    size="sm"
                    variant="outline"
                    onClick={askCurrentQuestion}
                    disabled={isInterviewerSpeaking}
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Repeat Question
                  </Button>
                </div>

                {/* Text Response */}
                <div>
                  <textarea
                    className="w-full h-20 bg-gray-700 text-white p-3 rounded-lg text-sm resize-none"
                    placeholder="Or type your response here..."
                    value={studentResponse}
                    onChange={(e) => setStudentResponse(e.target.value)}
                  />
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-400">
                    {isListening ? 'Recording your response...' : 'Click "Start Speaking" to give verbal answer'}
                  </span>
                  <div className="flex space-x-2">
                    {currentQuestion === 0 && !interviewerMessage.includes('Question 1') && (
                      <Button size="sm" onClick={startFirstQuestion} className="bg-blue-600 hover:bg-blue-700">
                        Start First Question
                      </Button>
                    )}
                    {currentQuestion < interview.questions.length - 1 && (
                      <Button size="sm" variant="outline" onClick={nextQuestion}>
                        Next Question →
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Local Video */}
          <Card className="bg-gray-800 border-gray-700">
            <CardContent className="p-4">
              <div className="aspect-video bg-gray-700 rounded-lg flex items-center justify-center mb-2">
                <video
                  ref={localVideoRef}
                  className="w-full h-full object-cover rounded-lg"
                  autoPlay
                  playsInline
                  muted
                />
              </div>
              
              {/* Controls */}
              <div className="flex justify-center space-x-2">
                <Button
                  size="sm"
                  variant={isVideoOn ? "default" : "destructive"}
                  onClick={toggleVideo}
                >
                  {isVideoOn ? <Video className="h-4 w-4" /> : <VideoOff className="h-4 w-4" />}
                </Button>
                <Button
                  size="sm"
                  variant={isAudioOn ? "default" : "destructive"}
                  onClick={toggleAudio}
                >
                  {isAudioOn ? <Mic className="h-4 w-4" /> : <MicOff className="h-4 w-4" />}
                </Button>
                <Button
                  size="sm"
                  variant={isScreenSharing ? "default" : "outline"}
                  onClick={toggleScreenShare}
                >
                  {isScreenSharing ? <MonitorOff className="h-4 w-4" /> : <Monitor className="h-4 w-4" />}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-4">
          <Card className="h-full bg-gray-800 border-gray-700">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>{question?.title}</span>
                <div className="flex space-x-2">
                  {currentQuestion > 0 && (
                    <Button variant="outline" size="sm">
                      Previous
                    </Button>
                  )}
                  {currentQuestion < interview.questions.length - 1 && (
                    <Button size="sm" onClick={nextQuestion}>
                      Next Question
                    </Button>
                  )}
                </div>
              </CardTitle>
              <CardDescription className="text-gray-300">
                {question?.description}
              </CardDescription>
            </CardHeader>
            <CardContent className="flex-1">
              <Tabs defaultValue="question" className="h-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="question">Question</TabsTrigger>
                  <TabsTrigger value="code">Code Editor</TabsTrigger>
                  <TabsTrigger value="notes">Notes</TabsTrigger>
                </TabsList>
                
                <TabsContent value="question" className="mt-4">
                  <div className="bg-gray-700 p-4 rounded-lg">
                    <h3 className="font-semibold mb-2">Question Details</h3>
                    <p className="text-gray-300 mb-4">{question?.description}</p>
                    <div className="flex items-center space-x-4 text-sm text-gray-400">
                      <span>Type: {question?.type}</span>
                      <span>Time Limit: {question?.timeLimit} minutes</span>
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="code" className="mt-4">
                  <div className="h-96">
                    <textarea
                      className="w-full h-full bg-gray-700 text-white p-4 rounded-lg font-mono text-sm resize-none"
                      placeholder="Write your code here..."
                      value={code}
                      onChange={(e) => setCode(e.target.value)}
                    />
                  </div>
                </TabsContent>
                
                <TabsContent value="notes" className="mt-4">
                  <div className="h-96">
                    <textarea
                      className="w-full h-full bg-gray-700 text-white p-4 rounded-lg resize-none"
                      placeholder="Take notes here..."
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                    />
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        {/* Chat Section */}
        <div className="w-80 p-4">
          <Card className="h-full bg-gray-800 border-gray-700">
            <CardHeader>
              <CardTitle className="flex items-center">
                <MessageSquare className="h-5 w-5 mr-2" />
                Chat
              </CardTitle>
            </CardHeader>
            <CardContent className="flex flex-col h-full">
              <div className="flex-1 bg-gray-700 rounded-lg p-4 mb-4 overflow-y-auto">
                {/* Chat messages would go here */}
                <div className="text-gray-400 text-center">
                  Chat with your interviewer
                </div>
              </div>
              <div className="flex space-x-2">
                <input
                  type="text"
                  placeholder="Type a message..."
                  className="flex-1 bg-gray-700 text-white px-3 py-2 rounded-lg text-sm"
                />
                <Button size="sm">
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
