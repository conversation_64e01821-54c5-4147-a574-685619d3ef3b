'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { MainNav } from '@/components/navigation/main-nav'
import { 
  Calendar, 
  Clock, 
  Video,
  Play,
  CheckCircle,
  AlertCircle,
  Plus
} from 'lucide-react'

interface Interview {
  id: string
  title: string
  description: string
  scheduledAt: Date
  duration: number
  status: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED'
  interviewer: string
  type: 'CODING' | 'BEHAVIORAL' | 'TECHNICAL'
  score?: number
}

export default function StudentInterviews() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [interviews, setInterviews] = useState<Interview[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/signin')
      return
    }

    if (session.user.role !== 'STUDENT') {
      router.push('/')
      return
    }

    fetchInterviews()
  }, [session, status, router])

  const fetchInterviews = async () => {
    try {
      // Demo data for now
      const demoInterviews: Interview[] = [
        {
          id: 'demo-1',
          title: 'Frontend Developer Interview',
          description: 'React, JavaScript, and CSS fundamentals',
          scheduledAt: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours from now
          duration: 60,
          status: 'SCHEDULED',
          interviewer: 'Tech Corp',
          type: 'CODING'
        },
        {
          id: 'demo-2',
          title: 'React Developer Assessment',
          description: 'Advanced React concepts and state management',
          scheduledAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 1 day from now
          duration: 90,
          status: 'SCHEDULED',
          interviewer: 'StartupXYZ',
          type: 'TECHNICAL'
        },
        {
          id: 'demo-3',
          title: 'Behavioral Interview',
          description: 'Communication and problem-solving skills',
          scheduledAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
          duration: 45,
          status: 'COMPLETED',
          interviewer: 'HR Team',
          type: 'BEHAVIORAL',
          score: 85
        },
        {
          id: 'demo-practice',
          title: 'Practice Interview - Available Now',
          description: 'Start a practice session anytime',
          scheduledAt: new Date(),
          duration: 30,
          status: 'SCHEDULED',
          interviewer: 'AI Interviewer',
          type: 'CODING'
        }
      ]
      
      setInterviews(demoInterviews)
      setLoading(false)
    } catch (error) {
      console.error('Error fetching interviews:', error)
      setLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'SCHEDULED':
        return <Badge variant="outline" className="text-blue-600 border-blue-600">Scheduled</Badge>
      case 'IN_PROGRESS':
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600">In Progress</Badge>
      case 'COMPLETED':
        return <Badge variant="outline" className="text-green-600 border-green-600">Completed</Badge>
      case 'CANCELLED':
        return <Badge variant="outline" className="text-red-600 border-red-600">Cancelled</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'CODING':
        return 'bg-blue-100 text-blue-800'
      case 'TECHNICAL':
        return 'bg-purple-100 text-purple-800'
      case 'BEHAVIORAL':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const canJoinInterview = (interview: Interview) => {
    const now = new Date()
    const scheduledTime = new Date(interview.scheduledAt)
    const timeDiff = scheduledTime.getTime() - now.getTime()
    
    // Can join if it's a practice interview or within 15 minutes of scheduled time
    return interview.id === 'demo-practice' || 
           (timeDiff <= 15 * 60 * 1000 && timeDiff >= -30 * 60 * 1000) // 15 min before to 30 min after
  }

  const startPracticeInterview = () => {
    router.push('/interview/demo-practice')
  }

  if (loading) {
    return <div className="flex justify-center items-center min-h-screen">Loading...</div>
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <MainNav />
      
      <div className="py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="mb-8 flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">My Interviews</h1>
              <p className="text-gray-600 mt-2">
                Manage your scheduled interviews and practice sessions
              </p>
            </div>
            <Button onClick={startPracticeInterview} className="bg-blue-600 hover:bg-blue-700">
              <Play className="h-4 w-4 mr-2" />
              Start Practice Interview
            </Button>
          </div>

          <div className="grid gap-6">
            {interviews.map((interview) => (
              <Card key={interview.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="flex items-center gap-3">
                        {interview.title}
                        {getStatusBadge(interview.status)}
                      </CardTitle>
                      <CardDescription className="mt-2">
                        {interview.description}
                      </CardDescription>
                    </div>
                    <Badge className={getTypeColor(interview.type)}>
                      {interview.type}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between items-center">
                    <div className="space-y-2">
                      <div className="flex items-center text-sm text-gray-600">
                        <Calendar className="h-4 w-4 mr-2" />
                        {interview.scheduledAt.toLocaleDateString()} at {interview.scheduledAt.toLocaleTimeString()}
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <Clock className="h-4 w-4 mr-2" />
                        {interview.duration} minutes
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <span className="font-medium">Interviewer:</span>
                        <span className="ml-1">{interview.interviewer}</span>
                      </div>
                      {interview.score && (
                        <div className="flex items-center text-sm text-green-600">
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Score: {interview.score}%
                        </div>
                      )}
                    </div>
                    
                    <div className="flex space-x-2">
                      {interview.status === 'COMPLETED' && (
                        <Button variant="outline" size="sm">
                          View Results
                        </Button>
                      )}
                      
                      {interview.status === 'SCHEDULED' && (
                        <Button 
                          size="sm"
                          disabled={!canJoinInterview(interview)}
                          onClick={() => router.push(`/interview/${interview.id}`)}
                          className={canJoinInterview(interview) ? 'bg-green-600 hover:bg-green-700' : ''}
                        >
                          <Video className="h-4 w-4 mr-2" />
                          {canJoinInterview(interview) ? 'Join Now' : 'Join Later'}
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {interviews.length === 0 && (
            <Card>
              <CardContent className="text-center py-12">
                <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No interviews scheduled</h3>
                <p className="text-gray-600 mb-4">
                  Start with a practice interview to get familiar with the platform
                </p>
                <Button onClick={startPracticeInterview}>
                  <Play className="h-4 w-4 mr-2" />
                  Start Practice Interview
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
