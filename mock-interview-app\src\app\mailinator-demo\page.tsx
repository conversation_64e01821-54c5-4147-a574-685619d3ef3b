'use client'

import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { ExternalLink, Mail, Send, CheckCircle, AlertCircle } from 'lucide-react'
import { MailinatorService } from '@/lib/mailinator-service'

export default function MailinatorDemoPage() {
  const [email, setEmail] = useState('<EMAIL>')
  const [studentName, setStudentName] = useState('Demo Student')
  const [interviewTitle, setInterviewTitle] = useState('Frontend Developer Position')
  const [customSubject, setCustomSubject] = useState('')
  const [customMessage, setCustomMessage] = useState('')
  const [sending, setSending] = useState(false)
  const [lastSentEmail, setLastSentEmail] = useState<string | null>(null)
  const [emailMode, setEmailMode] = useState<'MAILINATOR' | 'DEMO'>('MAILINATOR')

  // Get demo account setup
  const demoSetup = MailinatorService.setupDemoAccount()

  const handleSendInvitation = async () => {
    setSending(true)
    try {
      // Set email mode via API
      await fetch('/api/email/config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ emailMode })
      })

      // Send invitation via API
      const response = await fetch('/api/email/send-invitation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email,
          studentName,
          interviewTitle,
          scheduledAt: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString()
        })
      })

      const result = await response.json()

      if (result.success) {
        setLastSentEmail(email)
        alert(`✅ Interview invitation sent successfully to ${email}!`)
      } else {
        alert(`❌ Failed to send invitation: ${result.error}`)
      }
    } catch (error) {
      console.error('Error sending invitation:', error)
      alert('❌ Error sending invitation')
    } finally {
      setSending(false)
    }
  }

  const handleSendCustomEmail = async () => {
    if (!customSubject || !customMessage) {
      alert('Please fill in both subject and message')
      return
    }

    setSending(true)
    try {
      // Set email mode via API
      await fetch('/api/email/config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ emailMode })
      })

      // Send custom email via API
      const response = await fetch('/api/email/send-custom', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          to: email,
          subject: customSubject,
          message: customMessage
        })
      })

      const result = await response.json()

      if (result.success) {
        setLastSentEmail(email)
        alert(`✅ Custom email sent successfully to ${email}!`)
      } else {
        alert(`❌ Failed to send custom email: ${result.error}`)
      }
    } catch (error) {
      console.error('Error sending custom email:', error)
      alert('❌ Error sending custom email')
    } finally {
      setSending(false)
    }
  }

  const getInboxUrl = (email: string) => {
    const inboxName = email.split('@')[0]
    return `https://www.mailinator.com/v4/public/inboxes.jsp?to=${inboxName}`
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-gray-900">
            📧 Mailinator Email Integration Demo
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Test the complete email functionality using Mailinator's free email service. 
            Send interview invitations and custom emails, then check the inbox instantly!
          </p>
        </div>

        {/* Email Mode Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Email Service Mode
            </CardTitle>
            <CardDescription>
              Choose how emails should be sent
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <Button
                variant={emailMode === 'MAILINATOR' ? 'default' : 'outline'}
                onClick={() => setEmailMode('MAILINATOR')}
                className="flex items-center gap-2"
              >
                <CheckCircle className="h-4 w-4" />
                Mailinator (Real Emails)
              </Button>
              <Button
                variant={emailMode === 'DEMO' ? 'default' : 'outline'}
                onClick={() => setEmailMode('DEMO')}
                className="flex items-center gap-2"
              >
                <AlertCircle className="h-4 w-4" />
                Demo Mode (Console Only)
              </Button>
            </div>
            <p className="text-sm text-gray-600 mt-2">
              Current mode: <Badge variant="secondary">{emailMode}</Badge>
            </p>
          </CardContent>
        </Card>

        <div className="grid md:grid-cols-2 gap-6">
          {/* Send Interview Invitation */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                🎯 Send Interview Invitation
              </CardTitle>
              <CardDescription>
                Send a complete interview invitation with all features
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="email">Student Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
              
              <div>
                <Label htmlFor="studentName">Student Name</Label>
                <Input
                  id="studentName"
                  value={studentName}
                  onChange={(e) => setStudentName(e.target.value)}
                  placeholder="John Doe"
                />
              </div>
              
              <div>
                <Label htmlFor="interviewTitle">Interview Position</Label>
                <Input
                  id="interviewTitle"
                  value={interviewTitle}
                  onChange={(e) => setInterviewTitle(e.target.value)}
                  placeholder="Frontend Developer"
                />
              </div>

              <Button 
                onClick={handleSendInvitation} 
                disabled={sending}
                className="w-full"
              >
                {sending ? 'Sending...' : 'Send Interview Invitation'}
              </Button>
            </CardContent>
          </Card>

          {/* Send Custom Email */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Send className="h-5 w-5" />
                Send Custom Email
              </CardTitle>
              <CardDescription>
                Send a custom email to test the integration
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="customSubject">Email Subject</Label>
                <Input
                  id="customSubject"
                  value={customSubject}
                  onChange={(e) => setCustomSubject(e.target.value)}
                  placeholder="Test Email Subject"
                />
              </div>
              
              <div>
                <Label htmlFor="customMessage">Email Message</Label>
                <Textarea
                  id="customMessage"
                  value={customMessage}
                  onChange={(e) => setCustomMessage(e.target.value)}
                  placeholder="Enter your custom message here..."
                  rows={4}
                />
              </div>

              <Button 
                onClick={handleSendCustomEmail} 
                disabled={sending || !customSubject || !customMessage}
                className="w-full"
              >
                {sending ? 'Sending...' : 'Send Custom Email'}
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Demo Account Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              🆓 Free Mailinator Test Accounts
            </CardTitle>
            <CardDescription>
              Use these pre-configured test email addresses for instant testing
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-3">📧 Test Email Addresses:</h4>
                <div className="space-y-2">
                  {demoSetup.testEmails.map((testEmail, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <code className="text-sm">{testEmail}</code>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setEmail(testEmail)}
                      >
                        Use
                      </Button>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="font-semibold mb-3">📬 Check Inboxes:</h4>
                <div className="space-y-2">
                  {demoSetup.testEmails.map((testEmail, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span className="text-sm">{testEmail.split('@')[0]}</span>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => window.open(getInboxUrl(testEmail), '_blank')}
                      >
                        <ExternalLink className="h-4 w-4" />
                        Open Inbox
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {lastSentEmail && (
              <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                <h4 className="font-semibold text-green-800 mb-2">✅ Email Sent Successfully!</h4>
                <p className="text-green-700 mb-3">
                  Check the inbox for: <code className="bg-green-100 px-2 py-1 rounded">{lastSentEmail}</code>
                </p>
                <Button
                  size="sm"
                  onClick={() => window.open(getInboxUrl(lastSentEmail), '_blank')}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Open {lastSentEmail.split('@')[0]} Inbox
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>📋 How to Use This Demo</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-3">🚀 Quick Start:</h4>
                <ol className="list-decimal list-inside space-y-2 text-sm">
                  {demoSetup.instructions.map((instruction, index) => (
                    <li key={index}>{instruction}</li>
                  ))}
                </ol>
              </div>

              <div>
                <h4 className="font-semibold mb-3">🔧 Features Tested:</h4>
                <ul className="list-disc list-inside space-y-2 text-sm">
                  <li>✅ Real email sending via Mailinator API</li>
                  <li>✅ Interview invitation templates</li>
                  <li>✅ Custom email composition</li>
                  <li>✅ Instant inbox checking</li>
                  <li>✅ Email link testing</li>
                  <li>✅ Complete interview flow</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
