import { NextRequest, NextResponse } from 'next/server'
import { EmailService } from '@/lib/email-service'
import { MailinatorService } from '@/lib/mailinator-service'

export async function GET() {
  try {
    return NextResponse.json({
      emailMode: EmailService.getEmailMode(),
      mailinatorConfig: MailinatorService.getConfig(),
      demoSetup: MailinatorService.setupDemoAccount()
    })
  } catch (error) {
    console.error('Error getting email config:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { emailMode, mailinatorConfig } = body

    // Update email mode
    if (emailMode && ['NODEMAILER', 'MAILINATOR', 'DEMO'].includes(emailMode)) {
      EmailService.setEmailMode(emailMode)
    }

    // Update Mailinator config
    if (mailinatorConfig) {
      MailinatorService.setConfig(mailinatorConfig)
    }

    return NextResponse.json({
      success: true,
      message: 'Email configuration updated successfully',
      emailMode: EmailService.getEmailMode(),
      mailinatorConfig: MailinatorService.getConfig()
    })
  } catch (error) {
    console.error('Error updating email config:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
