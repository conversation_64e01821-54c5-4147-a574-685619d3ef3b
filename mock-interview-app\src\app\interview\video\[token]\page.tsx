'use client'

import { useEffect, useState, useRef } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { EmailService, InterviewInvitation } from '@/lib/email-service'
import { DemoAuth } from '@/lib/demo-auth'
import { 
  Video, 
  VideoOff, 
  Mic, 
  MicOff, 
  Square,
  Play,
  Pause,
  Clock,
  User,
  RotateCcw,
  ArrowRight,
  Circle
} from 'lucide-react'

// Mock interview questions with coding challenges
const interviewQuestions = [
  {
    id: 1,
    type: 'introduction',
    question: "Hello! Let's start with a brief introduction. Please tell me about yourself, your background, and what interests you about this position.",
    timeLimit: 120,
    hasCode: false
  },
  {
    id: 2,
    type: 'technical',
    question: "Can you explain the difference between let, const, and var in JavaScript? Please write a simple example demonstrating each one.",
    timeLimit: 300,
    hasCode: true,
    codeTemplate: `// Demonstrate the differences between let, const, and var
// Write your examples below:

// Example 1: var
function varExample() {
  // Your code here
}

// Example 2: let
function letExample() {
  // Your code here
}

// Example 3: const
function constExample() {
  // Your code here
}

// Test your examples
console.log("Testing var, let, and const differences");`
  },
  {
    id: 3,
    type: 'coding',
    question: "Write a function that finds the maximum number in an array. Explain your approach and implement it.",
    timeLimit: 360,
    hasCode: true,
    codeTemplate: `// Find the maximum number in an array
// Implement the function below:

function findMaximum(numbers) {
  // Your implementation here

}

// Test cases
const testArray1 = [1, 5, 3, 9, 2];
const testArray2 = [-1, -5, -3, -9, -2];
const testArray3 = [42];

console.log("Test 1:", findMaximum(testArray1)); // Expected: 9
console.log("Test 2:", findMaximum(testArray2)); // Expected: -1
console.log("Test 3:", findMaximum(testArray3)); // Expected: 42`
  },
  {
    id: 4,
    type: 'problem-solving',
    question: "Debug this code: There's a function that should reverse a string, but it's not working correctly. Find and fix the bug.",
    timeLimit: 300,
    hasCode: true,
    codeTemplate: `// Debug this function - it should reverse a string but has a bug
function reverseString(str) {
  let reversed = "";
  for (let i = 0; i <= str.length; i++) {
    reversed += str[str.length - 1 - i];
  }
  return reversed;
}

// Test the function
console.log(reverseString("hello")); // Should output: "olleh"
console.log(reverseString("JavaScript")); // Should output: "tpircSavaJ"

// Fix the bug and explain what was wrong`
  },
  {
    id: 5,
    type: 'closing',
    question: "Do you have any questions about the role or our company? Is there anything else you'd like me to know about you?",
    timeLimit: 120,
    hasCode: false
  }
]

export default function VideoInterviewPage() {
  const params = useParams()
  const router = useRouter()
  const token = params.token as string

  const videoRef = useRef<HTMLVideoElement>(null)
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const recordedChunksRef = useRef<Blob[]>([])

  const [invitation, setInvitation] = useState<InterviewInvitation | null>(null)
  const [loading, setLoading] = useState(true)
  const [stream, setStream] = useState<MediaStream | null>(null)
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [isRecording, setIsRecording] = useState(false)
  const [isPaused, setIsPaused] = useState(false)
  const [timeElapsed, setTimeElapsed] = useState(0)
  const [questionStartTime, setQuestionStartTime] = useState(0)
  const [isInterviewerSpeaking, setIsInterviewerSpeaking] = useState(false)
  const [isListening, setIsListening] = useState(false)
  const [interviewerMessage, setInterviewerMessage] = useState('')
  const [hasStarted, setHasStarted] = useState(false)
  const [recordedVideos, setRecordedVideos] = useState<Blob[]>([])

  // Code editor states
  const [code, setCode] = useState('')
  const [codeOutput, setCodeOutput] = useState('')
  const [isCodeExpanded, setIsCodeExpanded] = useState(false)
  const [isRunning, setIsRunning] = useState(false)

  const currentQuestion = interviewQuestions[currentQuestionIndex]

  useEffect(() => {
    // Check authentication
    const currentUser = DemoAuth.getCurrentUser()
    if (!currentUser) {
      router.push(`/interview/invite/${token}`)
      return
    }

    // Load invitation
    if (token) {
      const invitationData = EmailService.getInvitationByToken(token)
      if (invitationData) {
        setInvitation(invitationData)
      } else {
        router.push('/')
        return
      }
      setLoading(false)
    }
  }, [token, router])

  useEffect(() => {
    // Set up camera stream
    const setupCamera = async () => {
      try {
        const mediaStream = await navigator.mediaDevices.getUserMedia({
          video: true,
          audio: true
        })
        setStream(mediaStream)
        if (videoRef.current) {
          videoRef.current.srcObject = mediaStream
        }
      } catch (error) {
        console.error('Error accessing camera:', error)
      }
    }

    setupCamera()

    return () => {
      if (stream) {
        stream.getTracks().forEach(track => track.stop())
      }
    }
  }, [])

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout
    if (hasStarted && !isPaused) {
      interval = setInterval(() => {
        setTimeElapsed(prev => prev + 1)
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [hasStarted, isPaused])

  const speakQuestion = (question: string) => {
    setIsInterviewerSpeaking(true)
    setInterviewerMessage(question)

    // Load code template if question has code
    if (currentQuestion?.hasCode && currentQuestion?.codeTemplate) {
      setCode(currentQuestion.codeTemplate)
      setCodeOutput('')
    }

    if ('speechSynthesis' in window) {
      window.speechSynthesis.cancel()

      const utterance = new SpeechSynthesisUtterance(question)
      utterance.rate = 0.9
      utterance.pitch = 1
      utterance.volume = 0.8

      const voices = window.speechSynthesis.getVoices()
      const preferredVoice = voices.find(voice =>
        voice.name.includes('Google') ||
        voice.name.includes('Microsoft') ||
        voice.lang.startsWith('en')
      )
      if (preferredVoice) {
        utterance.voice = preferredVoice
      }

      utterance.onend = () => {
        setIsInterviewerSpeaking(false)
        setIsListening(true)
      }

      utterance.onerror = () => {
        setIsInterviewerSpeaking(false)
        setIsListening(true)
      }

      window.speechSynthesis.speak(utterance)
    } else {
      setTimeout(() => {
        setIsInterviewerSpeaking(false)
        setIsListening(true)
      }, 3000)
    }
  }

  const runCode = async () => {
    setIsRunning(true)
    setCodeOutput('Running code...\n')

    try {
      // Create a safe execution environment
      const originalConsoleLog = console.log
      let output = ''

      // Override console.log to capture output
      console.log = (...args) => {
        output += args.map(arg =>
          typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
        ).join(' ') + '\n'
      }

      // Execute the code in a try-catch block
      const func = new Function(code)
      func()

      // Restore original console.log
      console.log = originalConsoleLog

      setCodeOutput(output || 'Code executed successfully (no output)')
    } catch (error) {
      setCodeOutput(`Error: ${error.message}`)
    }

    setIsRunning(false)
  }

  const startRecording = () => {
    if (!stream) return

    try {
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'video/webm;codecs=vp9'
      })

      recordedChunksRef.current = []

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          recordedChunksRef.current.push(event.data)
        }
      }

      mediaRecorder.onstop = () => {
        const blob = new Blob(recordedChunksRef.current, { type: 'video/webm' })
        setRecordedVideos(prev => [...prev, blob])
      }

      mediaRecorder.start(1000) // Record in 1-second chunks
      mediaRecorderRef.current = mediaRecorder
      setIsRecording(true)
    } catch (error) {
      console.error('Error starting recording:', error)
    }
  }

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
      setIsRecording(false)
    }
  }

  const startInterview = () => {
    setHasStarted(true)
    setQuestionStartTime(Date.now())
    startRecording()
    
    // Welcome message
    const welcomeMessage = `Welcome to your video interview! I'll be asking you ${interviewQuestions.length} questions. Let's begin with the first question.`
    speakQuestion(welcomeMessage)
    
    setTimeout(() => {
      speakQuestion(currentQuestion.question)
    }, 4000)
  }

  const nextQuestion = () => {
    stopRecording()
    
    if (currentQuestionIndex < interviewQuestions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1)
      setQuestionStartTime(Date.now())
      setIsListening(false)
      
      setTimeout(() => {
        startRecording()
        speakQuestion(interviewQuestions[currentQuestionIndex + 1].question)
      }, 2000)
    } else {
      // Interview complete
      completeInterview()
    }
  }

  const repeatQuestion = () => {
    if (currentQuestion) {
      speakQuestion(currentQuestion.question)
    }
  }

  const completeInterview = () => {
    setIsListening(false)
    setIsInterviewerSpeaking(true)
    setInterviewerMessage("Congratulations! You've completed the interview. Thank you for your time and thoughtful responses.")
    
    const congratsMessage = "Congratulations! You've completed the interview. Thank you for your time and thoughtful responses. Your recording will be reviewed and you'll receive feedback soon."
    
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(congratsMessage)
      utterance.onend = () => {
        setIsInterviewerSpeaking(false)
        // Navigate to completion page
        setTimeout(() => {
          router.push(`/interview/complete/${token}`)
        }, 2000)
      }
      window.speechSynthesis.speak(utterance)
    } else {
      setTimeout(() => {
        setIsInterviewerSpeaking(false)
        router.push(`/interview/complete/${token}`)
      }, 3000)
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p>Loading interview...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <div className="bg-gray-800 p-4 flex justify-between items-center">
        <div>
          <h1 className="text-xl font-bold text-white">{invitation?.interviewTitle}</h1>
          <p className="text-gray-400">
            Question {currentQuestionIndex + 1} of {interviewQuestions.length}
            {hasStarted && ` • ${formatTime(timeElapsed)}`}
          </p>
        </div>
        <div className="flex items-center space-x-4">
          {isRecording && (
            <div className="flex items-center space-x-2 text-red-400">
              <Circle className="h-3 w-3 fill-current animate-pulse" />
              <span className="text-sm">Recording</span>
            </div>
          )}
          <Button variant="destructive" onClick={completeInterview}>
            End Interview
          </Button>
        </div>
      </div>

      <div className="flex h-[calc(100vh-80px)]">
        {/* AI Interviewer */}
        <div className="w-1/3 p-4 space-y-4">
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader>
              <CardTitle className="text-white text-sm">AI Interviewer</CardTitle>
            </CardHeader>
            <CardContent>
              {/* AI Avatar */}
              <div className="aspect-video bg-gradient-to-br from-blue-900 to-purple-900 rounded-lg flex items-center justify-center mb-4 relative overflow-hidden">
                <div className={`relative transition-all duration-500 ${isInterviewerSpeaking ? 'scale-110' : 'scale-100'}`}>
                  <div className="w-24 h-24 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center relative">
                    {/* Eyes */}
                    <div className="absolute top-6 left-6 w-2 h-2 bg-white rounded-full"></div>
                    <div className="absolute top-6 right-6 w-2 h-2 bg-white rounded-full"></div>
                    
                    {/* Mouth */}
                    <div className={`absolute bottom-6 left-1/2 transform -translate-x-1/2 transition-all duration-300 ${
                      isInterviewerSpeaking 
                        ? 'w-6 h-3 bg-white rounded-full animate-pulse' 
                        : 'w-4 h-1 bg-white rounded-full'
                    }`}></div>
                    
                    {/* Speaking indicator */}
                    {isInterviewerSpeaking && (
                      <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
                        <div className="flex space-x-1">
                          <div className="w-1 h-1 bg-green-400 rounded-full animate-bounce"></div>
                          <div className="w-1 h-1 bg-green-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                          <div className="w-1 h-1 bg-green-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  {/* Listening indicator */}
                  {isListening && (
                    <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                      <div className="flex items-center space-x-1 bg-red-500 px-2 py-1 rounded-full text-xs">
                        <div className="w-1 h-1 bg-white rounded-full animate-pulse"></div>
                        <span className="text-white">Listening</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              
              <p className="text-sm text-gray-400 text-center">
                {isInterviewerSpeaking ? '🗣️ Speaking' : isListening ? '👂 Listening' : '💭 Thinking'}
              </p>
            </CardContent>
          </Card>

          {/* Speech Bubble */}
          {interviewerMessage && (
            <Card className="bg-blue-900 border-blue-700">
              <CardContent className="p-3">
                <div className="relative">
                  <div className="bg-blue-800 p-3 rounded-lg text-sm">
                    <p className="text-blue-100">{interviewerMessage}</p>
                  </div>
                  <div className="absolute -top-1 left-4 w-0 h-0 border-l-2 border-r-2 border-b-2 border-transparent border-b-blue-800"></div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Student Video */}
        <div className="w-1/3 p-4">
          <Card className="bg-gray-800 border-gray-700 h-full">
            <CardHeader>
              <CardTitle className="text-white text-sm">Your Video</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="aspect-video bg-gray-700 rounded-lg overflow-hidden relative">
                <video
                  ref={videoRef}
                  autoPlay
                  playsInline
                  muted
                  className="w-full h-full object-cover"
                />
                
                {/* Recording indicator */}
                {isRecording && (
                  <div className="absolute top-2 right-2 bg-red-600 text-white px-2 py-1 rounded text-xs flex items-center">
                    <Circle className="h-2 w-2 fill-current mr-1 animate-pulse" />
                    REC
                  </div>
                )}
              </div>
              
              <div className="mt-4 text-center">
                <p className="text-sm text-gray-400 mb-2">
                  {invitation?.studentName}
                </p>
                
                {!hasStarted ? (
                  <Button onClick={startInterview} className="bg-green-600 hover:bg-green-700">
                    <Play className="h-4 w-4 mr-2" />
                    Start Interview
                  </Button>
                ) : (
                  <div className="flex justify-center space-x-2">
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={repeatQuestion}
                      disabled={isInterviewerSpeaking}
                      className="border-gray-600"
                    >
                      <RotateCcw className="h-4 w-4 mr-1" />
                      Repeat
                    </Button>
                    
                    {currentQuestionIndex < interviewQuestions.length - 1 ? (
                      <Button 
                        size="sm" 
                        onClick={nextQuestion}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        <ArrowRight className="h-4 w-4 mr-1" />
                        Next
                      </Button>
                    ) : (
                      <Button 
                        size="sm" 
                        onClick={completeInterview}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <Square className="h-4 w-4 mr-1" />
                        Finish
                      </Button>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Question Panel */}
        <div className="w-1/3 p-4">
          <Card className="bg-gray-800 border-gray-700 h-full">
            <CardHeader>
              <CardTitle className="text-white text-sm">Current Question</CardTitle>
              <CardDescription className="text-gray-400">
                {currentQuestion?.type} • {currentQuestion?.timeLimit}s limit
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-700 p-4 rounded-lg mb-4">
                <p className="text-gray-300">{currentQuestion?.question}</p>
              </div>
              
              {hasStarted && (
                <div className="space-y-3">
                  <div className="flex justify-between text-sm text-gray-400">
                    <span>Time for this question</span>
                    <span>{formatTime(Math.floor((Date.now() - questionStartTime) / 1000))}</span>
                  </div>
                  
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-blue-500 h-2 rounded-full transition-all duration-1000"
                      style={{ 
                        width: `${Math.min(((Date.now() - questionStartTime) / 1000 / currentQuestion.timeLimit) * 100, 100)}%` 
                      }}
                    ></div>
                  </div>
                  
                  <div className="text-center">
                    <p className="text-xs text-gray-400">
                      {isListening ? 'You may begin your response' : 'Please wait for the question'}
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
