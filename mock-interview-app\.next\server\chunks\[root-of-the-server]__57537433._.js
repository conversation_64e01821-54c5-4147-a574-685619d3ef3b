module.exports = {

"[project]/.next-internal/server/app/api/email/config/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[project]/src/lib/mailinator-service.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "MailinatorService": (()=>MailinatorService)
});
class MailinatorService {
    static config = {
        usePublicDomain: !process.env.MAILINATOR_API_TOKEN,
        privateDomain: process.env.MAILINATOR_PRIVATE_DOMAIN,
        apiToken: process.env.MAILINATOR_API_TOKEN,
        webhookToken: process.env.MAILINATOR_WEBHOOK_TOKEN
    };
    static setConfig(config) {
        this.config = {
            ...this.config,
            ...config
        };
    }
    static getConfig() {
        return {
            ...this.config
        };
    }
    // Send email via Mailinator API
    static async sendEmail(to, subject, html, text) {
        try {
            if (this.config.usePublicDomain) {
                return await this.sendToPublicDomain(to, subject, html, text);
            } else if (this.config.privateDomain && this.config.apiToken) {
                return await this.sendToPrivateDomain(to, subject, html, text);
            } else {
                console.log('📧 Mailinator Demo Mode - Email would be sent:');
                console.log(`To: ${to}`);
                console.log(`Subject: ${subject}`);
                console.log(`HTML: ${html.substring(0, 200)}...`);
                return true;
            }
        } catch (error) {
            console.error('❌ Failed to send email via Mailinator:', error);
            return false;
        }
    }
    // Send to Mailinator public domain (free tier)
    static async sendToPublicDomain(to, subject, html, text) {
        try {
            // Extract inbox name from email
            const inboxName = this.extractInboxName(to);
            // Use Mailinator's webhook endpoint for public domain
            const webhookUrl = `https://www.mailinator.com/api/v2/domains/public/webhook/${inboxName}/`;
            const payload = {
                from: '<EMAIL>',
                subject: subject,
                text: text || this.htmlToText(html),
                html: html,
                to: inboxName
            };
            const response = await fetch(webhookUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload)
            });
            if (response.ok) {
                console.log(`✅ Email sent to Mailinator public inbox: ${inboxName}@mailinator.com`);
                console.log(`📬 Check inbox at: https://www.mailinator.com/v4/public/inboxes.jsp?to=${inboxName}`);
                return true;
            } else {
                const errorText = await response.text();
                console.error('❌ Failed to send to Mailinator public domain:', response.status, errorText);
                return false;
            }
        } catch (error) {
            console.error('❌ Error sending to Mailinator public domain:', error);
            return false;
        }
    }
    // Send to Mailinator private domain (requires API token)
    static async sendToPrivateDomain(to, subject, html, text) {
        try {
            const inboxName = this.extractInboxName(to);
            const apiUrl = `https://api.mailinator.com/api/v2/domains/private/inboxes/${inboxName}/messages`;
            const payload = {
                from: '<EMAIL>',
                subject: subject,
                text: text || this.htmlToText(html),
                html: html
            };
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': this.config.apiToken
                },
                body: JSON.stringify(payload)
            });
            if (response.ok) {
                console.log(`✅ Email sent to Mailinator private domain: ${to}`);
                return true;
            } else {
                const errorText = await response.text();
                console.error('❌ Failed to send to Mailinator private domain:', response.status, errorText);
                return false;
            }
        } catch (error) {
            console.error('❌ Error sending to Mailinator private domain:', error);
            return false;
        }
    }
    // Fetch messages from Mailinator inbox
    static async getInboxMessages(inboxName) {
        try {
            if (this.config.usePublicDomain) {
                return await this.getPublicInboxMessages(inboxName);
            } else if (this.config.privateDomain && this.config.apiToken) {
                return await this.getPrivateInboxMessages(inboxName);
            } else {
                console.log('📧 Mailinator not configured for message retrieval');
                return [];
            }
        } catch (error) {
            console.error('❌ Failed to fetch inbox messages:', error);
            return [];
        }
    }
    // Get messages from public domain
    static async getPublicInboxMessages(inboxName) {
        try {
            const response = await fetch(`https://www.mailinator.com/api/v2/domains/public/inboxes/${inboxName}`);
            if (response.ok) {
                const data = await response.json();
                return data.msgs || [];
            } else {
                console.error('❌ Failed to fetch public inbox messages:', response.statusText);
                return [];
            }
        } catch (error) {
            console.error('❌ Error fetching public inbox messages:', error);
            return [];
        }
    }
    // Get messages from private domain
    static async getPrivateInboxMessages(inboxName) {
        try {
            const response = await fetch(`https://api.mailinator.com/api/v2/domains/private/inboxes/${inboxName}`, {
                headers: {
                    'Authorization': this.config.apiToken
                }
            });
            if (response.ok) {
                const data = await response.json();
                return data.msgs || [];
            } else {
                console.error('❌ Failed to fetch private inbox messages:', response.statusText);
                return [];
            }
        } catch (error) {
            console.error('❌ Error fetching private inbox messages:', error);
            return [];
        }
    }
    // Utility functions
    static extractInboxName(email) {
        const parts = email.split('@');
        return parts[0] || 'demo';
    }
    static htmlToText(html) {
        // Simple HTML to text conversion
        return html.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ').replace(/&amp;/g, '&').replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&quot;/g, '"').trim();
    }
    // Generate Mailinator email for testing
    static generateTestEmail(name) {
        const cleanName = name.toLowerCase().replace(/[^a-z0-9]/g, '');
        return `${cleanName}@mailinator.com`;
    }
    // Create demo Mailinator account setup
    static setupDemoAccount() {
        const testEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];
        const inboxUrls = testEmails.map((email)=>{
            const inboxName = this.extractInboxName(email);
            return `https://www.mailinator.com/v4/public/inboxes.jsp?to=${inboxName}`;
        });
        const instructions = [
            '1. 📧 Use any of the test emails above for demo purposes',
            '2. 🌐 Visit the inbox URLs to check received emails',
            '3. 🔄 Emails are automatically deleted after a few hours',
            '4. 🆓 No signup required for public Mailinator inboxes',
            '5. 🔗 Click email links to test the full interview flow'
        ];
        return {
            testEmails,
            inboxUrls,
            instructions
        };
    }
}
}}),
"[externals]/nodemailer [external] (nodemailer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("nodemailer", () => require("nodemailer"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/prisma.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prisma": (()=>prisma)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const globalForPrisma = globalThis;
const prisma = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]();
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = prisma;
}}),
"[project]/src/lib/email-service.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "EmailService": (()=>EmailService)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/crypto [external] (crypto, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mailinator$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mailinator-service.ts [app-route] (ecmascript)");
;
;
// Only import nodemailer on server side
let nodemailer = null;
let prisma = null;
if ("TURBOPACK compile-time truthy", 1) {
    // Server-side imports
    try {
        nodemailer = __turbopack_context__.r("[externals]/nodemailer [external] (nodemailer, cjs)");
        prisma = __turbopack_context__.r("[project]/src/lib/prisma.ts [app-route] (ecmascript)").prisma;
    } catch (error) {
        console.log('Server-side dependencies not available');
    }
}
// In-memory storage for demo (replace with database in production)
const invitations = new Map();
class EmailService {
    static transporter = null;
    // Initialize transporter only on server side
    static getTransporter() {
        if (!this.transporter && nodemailer && "undefined" === 'undefined') {
            this.transporter = nodemailer.createTransport({
                host: process.env.EMAIL_HOST,
                port: parseInt(process.env.EMAIL_PORT || '587'),
                secure: false,
                auth: {
                    user: process.env.EMAIL_USER,
                    pass: process.env.EMAIL_PASS
                }
            });
        }
        return this.transporter;
    }
    // Email service mode configuration
    static emailMode = 'MAILINATOR';
    static setEmailMode(mode) {
        this.emailMode = mode;
    }
    static getEmailMode() {
        return this.emailMode;
    }
    // Invitation Token Management
    static generateInvitationToken() {
        return __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].randomBytes(32).toString('hex');
    }
    static createInvitation(email, studentName, interviewTitle, scheduledAt) {
        const token = this.generateInvitationToken();
        const id = __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].randomUUID();
        const expiresAt = new Date(scheduledAt.getTime() + 24 * 60 * 60 * 1000) // 24 hours after scheduled time
        ;
        const invitation = {
            id,
            email,
            studentName,
            interviewTitle,
            scheduledAt,
            token,
            status: 'PENDING',
            createdAt: new Date(),
            expiresAt
        };
        invitations.set(token, invitation);
        return invitation;
    }
    static getInvitationByToken(token) {
        const invitation = invitations.get(token);
        if (!invitation) return null;
        // Check if expired
        if (new Date() > invitation.expiresAt) {
            invitation.status = 'EXPIRED';
        }
        return invitation;
    }
    static updateInvitationStatus(token, status) {
        const invitation = invitations.get(token);
        if (!invitation) return false;
        invitation.status = status;
        return true;
    }
    static generateInvitationLink(token, baseUrl = 'http://localhost:3000') {
        return `${baseUrl}/interview/invite/${token}`;
    }
    static async sendEmail(options) {
        try {
            let success = false;
            // Choose email service based on mode
            switch(this.emailMode){
                case 'MAILINATOR':
                    success = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mailinator$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MailinatorService"].sendEmail(options.to, options.subject, options.html);
                    break;
                case 'NODEMAILER':
                    // Skip email sending if no email configuration or not on server
                    if ("TURBOPACK compile-time falsy", 0) {
                        "TURBOPACK unreachable";
                    }
                    if (!process.env.EMAIL_USER || !process.env.EMAIL_PASS) {
                        console.log('Email service not configured, skipping email send');
                        return true;
                    }
                    const transporter = this.getTransporter();
                    if (!transporter) {
                        console.log('Failed to initialize email transporter');
                        return false;
                    }
                    const info = await transporter.sendMail({
                        from: process.env.EMAIL_USER,
                        to: options.to,
                        subject: options.subject,
                        html: options.html,
                        attachments: options.attachments
                    });
                    success = true;
                    break;
                case 'DEMO':
                default:
                    console.log('📧 Demo Mode - Email would be sent:');
                    console.log(`To: ${options.to}`);
                    console.log(`Subject: ${options.subject}`);
                    success = true;
                    break;
            }
            // Log email (skip if database not available)
            try {
                await prisma.emailLog.create({
                    data: {
                        to: options.to,
                        subject: options.subject,
                        body: options.html,
                        status: success ? 'SENT' : 'FAILED',
                        sentAt: new Date()
                    }
                });
            } catch (dbError) {
                console.log('Database not available for email logging');
            }
            return success;
        } catch (error) {
            console.error('Error sending email:', error);
            // Log failed email (skip if database not available)
            try {
                await prisma.emailLog.create({
                    data: {
                        to: options.to,
                        subject: options.subject,
                        body: options.html,
                        status: 'FAILED'
                    }
                });
            } catch (dbError) {
                console.log('Database not available for email logging');
            }
            return false;
        }
    }
    static async sendInterviewInvitationWithToken(invitation) {
        const invitationLink = this.generateInvitationLink(invitation.token);
        const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0;">🎯 Video Interview Invitation</h1>
        </div>

        <div style="padding: 30px; background: #f8f9fa;">
          <h2 style="color: #333;">Hello ${invitation.studentName}!</h2>

          <p style="font-size: 16px; line-height: 1.6; color: #555;">
            You've been invited to participate in an AI-powered video interview for:
          </p>

          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea;">
            <h3 style="margin: 0 0 10px 0; color: #333;">${invitation.interviewTitle}</h3>
            <p style="margin: 0; color: #666;">
              📅 Scheduled: ${invitation.scheduledAt.toLocaleDateString()} at ${invitation.scheduledAt.toLocaleTimeString()}
            </p>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${invitationLink}"
               style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
              🚀 Join Video Interview
            </a>
          </div>

          <div style="background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h4 style="margin: 0 0 10px 0; color: #1976d2;">📋 What to Expect:</h4>
            <ul style="margin: 0; padding-left: 20px; color: #555;">
              <li>🤖 AI-powered interviewer with voice interaction</li>
              <li>📹 Video recording for review and feedback</li>
              <li>💻 Interactive coding and behavioral questions</li>
              <li>📊 Real-time evaluation and scoring</li>
              <li>🎉 Instant results and congratulations</li>
            </ul>
          </div>

          <div style="background: #fff3e0; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h4 style="margin: 0 0 10px 0; color: #f57c00;">⚠️ Technical Requirements:</h4>
            <ul style="margin: 0; padding-left: 20px; color: #555;">
              <li>🌐 Stable internet connection</li>
              <li>📷 Working camera and microphone</li>
              <li>🌍 Modern web browser (Chrome recommended)</li>
              <li>🔇 Quiet environment for recording</li>
              <li>💾 Allow browser permissions for camera/mic</li>
            </ul>
          </div>

          <p style="font-size: 14px; color: #777; margin-top: 30px;">
            This invitation expires on ${invitation.expiresAt.toLocaleDateString()}.
            If you have any questions, please contact our support team.
          </p>
        </div>

        <div style="background: #333; padding: 20px; text-align: center;">
          <p style="color: #ccc; margin: 0; font-size: 14px;">
            © 2024 AI Interview Platform. All rights reserved.
          </p>
        </div>
      </div>
    `;
        return this.sendEmail({
            to: invitation.email,
            subject: `🎯 Video Interview Invitation - ${invitation.interviewTitle}`,
            html
        });
    }
    static async sendInterviewInvitation(studentEmail, studentName, interviewDetails) {
        const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Interview Invitation</h2>
        <p>Dear ${studentName},</p>
        <p>You have been invited to participate in an interview:</p>
        
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">${interviewDetails.title}</h3>
          <p><strong>Date & Time:</strong> ${interviewDetails.scheduledAt.toLocaleString()}</p>
          <p><strong>Duration:</strong> ${interviewDetails.duration} minutes</p>
          <p><strong>Meeting Link:</strong> <a href="${interviewDetails.meetingLink}">${interviewDetails.meetingLink}</a></p>
        </div>
        
        <p>Please make sure to:</p>
        <ul>
          <li>Test your camera and microphone before the interview</li>
          <li>Ensure you have a stable internet connection</li>
          <li>Prepare for coding and theory questions</li>
          <li>Join the meeting 5 minutes early</li>
        </ul>
        
        <p>Good luck with your interview!</p>
        <p>Best regards,<br>Mock Interview Team</p>
      </div>
    `;
        return this.sendEmail({
            to: studentEmail,
            subject: `Interview Invitation - ${interviewDetails.title}`,
            html
        });
    }
    static async sendInterviewResults(studentEmail, studentName, results) {
        const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Interview Results</h2>
        <p>Dear ${studentName},</p>
        <p>Your interview has been completed. Here are your results:</p>
        
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Overall Score: ${results.score}/100</h3>
          <h4>Feedback:</h4>
          <p>${results.feedback}</p>
          ${results.recordingUrl ? `<p><strong>Recording:</strong> <a href="${results.recordingUrl}">View Recording</a></p>` : ''}
        </div>
        
        <p>Keep practicing and improving your skills!</p>
        <p>Best regards,<br>Mock Interview Team</p>
      </div>
    `;
        return this.sendEmail({
            to: studentEmail,
            subject: 'Your Interview Results',
            html
        });
    }
    static async sendWelcomeEmail(userEmail, userName, role) {
        const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Welcome to Mock Interview Platform!</h2>
        <p>Dear ${userName},</p>
        <p>Welcome to our Mock Interview Platform! Your account has been created successfully.</p>
        
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Account Details</h3>
          <p><strong>Email:</strong> ${userEmail}</p>
          <p><strong>Role:</strong> ${role}</p>
        </div>
        
        <p>You can now log in and start using the platform.</p>
        <p>Best regards,<br>Mock Interview Team</p>
      </div>
    `;
        return this.sendEmail({
            to: userEmail,
            subject: 'Welcome to Mock Interview Platform',
            html
        });
    }
    // Demo function to create sample invitations
    static createDemoInvitations() {
        const demoInvitations = [
            {
                email: '<EMAIL>',
                studentName: 'John Doe',
                interviewTitle: 'Frontend Developer Position',
                scheduledAt: new Date(Date.now() + 2 * 60 * 60 * 1000) // 2 hours from now
            },
            {
                email: '<EMAIL>',
                studentName: 'Jane Smith',
                interviewTitle: 'Full Stack Developer Role',
                scheduledAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 1 day from now
            },
            {
                email: '<EMAIL>',
                studentName: 'Alex Johnson',
                interviewTitle: 'React Developer Interview',
                scheduledAt: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3 days from now
            }
        ];
        return demoInvitations.map((demo)=>this.createInvitation(demo.email, demo.studentName, demo.interviewTitle, demo.scheduledAt));
    }
}
// Initialize demo invitations
if ("TURBOPACK compile-time truthy", 1) {
    EmailService.createDemoInvitations();
}
}}),
"[project]/src/app/api/email/config/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$email$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/email-service.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mailinator$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mailinator-service.ts [app-route] (ecmascript)");
;
;
;
async function GET() {
    try {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            emailMode: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$email$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EmailService"].getEmailMode(),
            mailinatorConfig: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mailinator$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MailinatorService"].getConfig(),
            demoSetup: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mailinator$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MailinatorService"].setupDemoAccount()
        });
    } catch (error) {
        console.error('Error getting email config:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const body = await request.json();
        const { emailMode, mailinatorConfig } = body;
        // Update email mode
        if (emailMode && [
            'NODEMAILER',
            'MAILINATOR',
            'DEMO'
        ].includes(emailMode)) {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$email$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EmailService"].setEmailMode(emailMode);
        }
        // Update Mailinator config
        if (mailinatorConfig) {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mailinator$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MailinatorService"].setConfig(mailinatorConfig);
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            message: 'Email configuration updated successfully',
            emailMode: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$email$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EmailService"].getEmailMode(),
            mailinatorConfig: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mailinator$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MailinatorService"].getConfig()
        });
    } catch (error) {
        console.error('Error updating email config:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__57537433._.js.map