{"version": 3, "file": "transcriptions.d.mts", "sourceRoot": "", "sources": ["../../src/resources/audio/transcriptions.ts"], "names": [], "mappings": "OAEO,EAAE,WAAW,EAAE;OACf,KAAK,iBAAiB;OACtB,KAAK,QAAQ;OACb,EAAE,UAAU,EAAE;OACd,EAAE,MAAM,EAAE;OACV,EAAE,KAAK,UAAU,EAAE;OACnB,EAAE,cAAc,EAAE;AAGzB,qBAAa,cAAe,SAAQ,WAAW;IAC7C;;;;;;;;;;;OAWG;IACH,MAAM,CACJ,IAAI,EAAE,qCAAqC,CAAC,MAAM,GAAG,SAAS,CAAC,EAC/D,OAAO,CAAC,EAAE,cAAc,GACvB,UAAU,CAAC,aAAa,CAAC;IAC5B,MAAM,CACJ,IAAI,EAAE,qCAAqC,CAAC,cAAc,CAAC,EAC3D,OAAO,CAAC,EAAE,cAAc,GACvB,UAAU,CAAC,oBAAoB,CAAC;IACnC,MAAM,CACJ,IAAI,EAAE,qCAAqC,CAAC,KAAK,GAAG,KAAK,GAAG,MAAM,CAAC,EACnE,OAAO,CAAC,EAAE,cAAc,GACvB,UAAU,CAAC,MAAM,CAAC;IACrB,MAAM,CAAC,IAAI,EAAE,qCAAqC,EAAE,OAAO,CAAC,EAAE,cAAc,GAAG,UAAU,CAAC,aAAa,CAAC;IACxG,MAAM,CACJ,IAAI,EAAE,kCAAkC,EACxC,OAAO,CAAC,EAAE,cAAc,GACvB,UAAU,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC;IAC/C,MAAM,CACJ,IAAI,EAAE,kCAAkC,EACxC,OAAO,CAAC,EAAE,cAAc,GACvB,UAAU,CAAC,2BAA2B,GAAG,MAAM,GAAG,MAAM,CAAC,wBAAwB,CAAC,CAAC;CAkBvF;AAED;;;GAGG;AACH,MAAM,WAAW,aAAa;IAC5B;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;;;OAIG;IACH,QAAQ,CAAC,EAAE,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;IAExC;;OAEG;IACH,KAAK,CAAC,EAAE,aAAa,CAAC,MAAM,GAAG,aAAa,CAAC,QAAQ,CAAC;CACvD;AAED,yBAAiB,aAAa,CAAC;IAC7B,UAAiB,OAAO;QACtB;;WAEG;QACH,KAAK,CAAC,EAAE,MAAM,CAAC;QAEf;;WAEG;QACH,KAAK,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;QAEtB;;WAEG;QACH,OAAO,CAAC,EAAE,MAAM,CAAC;KAClB;IAED;;OAEG;IACH,UAAiB,MAAM;QACrB;;WAEG;QACH,YAAY,EAAE,MAAM,CAAC;QAErB;;WAEG;QACH,aAAa,EAAE,MAAM,CAAC;QAEtB;;WAEG;QACH,YAAY,EAAE,MAAM,CAAC;QAErB;;WAEG;QACH,IAAI,EAAE,QAAQ,CAAC;QAEf;;WAEG;QACH,mBAAmB,CAAC,EAAE,MAAM,CAAC,iBAAiB,CAAC;KAChD;IAED,UAAiB,MAAM,CAAC;QACtB;;WAEG;QACH,UAAiB,iBAAiB;YAChC;;eAEG;YACH,YAAY,CAAC,EAAE,MAAM,CAAC;YAEtB;;eAEG;YACH,WAAW,CAAC,EAAE,MAAM,CAAC;SACtB;KACF;IAED;;OAEG;IACH,UAAiB,QAAQ;QACvB;;WAEG;QACH,QAAQ,EAAE,MAAM,CAAC;QAEjB;;WAEG;QACH,IAAI,EAAE,UAAU,CAAC;KAClB;CACF;AAED,MAAM,MAAM,oBAAoB,GAAG,UAAU,CAAC;AAE9C,MAAM,WAAW,oBAAoB;IACnC;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;;OAGG;IACH,WAAW,EAAE,MAAM,CAAC;IAEpB;;;OAGG;IACH,iBAAiB,EAAE,MAAM,CAAC;IAE1B;;OAEG;IACH,GAAG,EAAE,MAAM,CAAC;IAEZ;;;OAGG;IACH,cAAc,EAAE,MAAM,CAAC;IAEvB;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,WAAW,EAAE,MAAM,CAAC;IAEpB;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;CACvB;AAED;;;;;GAKG;AACH,MAAM,MAAM,wBAAwB,GAAG,2BAA2B,GAAG,0BAA0B,CAAC;AAEhG;;;;;GAKG;AACH,MAAM,WAAW,2BAA2B;IAC1C;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,IAAI,EAAE,uBAAuB,CAAC;IAE9B;;;;OAIG;IACH,QAAQ,CAAC,EAAE,KAAK,CAAC,2BAA2B,CAAC,OAAO,CAAC,CAAC;CACvD;AAED,yBAAiB,2BAA2B,CAAC;IAC3C,UAAiB,OAAO;QACtB;;WAEG;QACH,KAAK,CAAC,EAAE,MAAM,CAAC;QAEf;;WAEG;QACH,KAAK,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;QAEtB;;WAEG;QACH,OAAO,CAAC,EAAE,MAAM,CAAC;KAClB;CACF;AAED;;;;;GAKG;AACH,MAAM,WAAW,0BAA0B;IACzC;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,IAAI,EAAE,sBAAsB,CAAC;IAE7B;;;;;OAKG;IACH,QAAQ,CAAC,EAAE,KAAK,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;IAErD;;OAEG;IACH,KAAK,CAAC,EAAE,0BAA0B,CAAC,KAAK,CAAC;CAC1C;AAED,yBAAiB,0BAA0B,CAAC;IAC1C,UAAiB,OAAO;QACtB;;WAEG;QACH,KAAK,CAAC,EAAE,MAAM,CAAC;QAEf;;WAEG;QACH,KAAK,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;QAEtB;;WAEG;QACH,OAAO,CAAC,EAAE,MAAM,CAAC;KAClB;IAED;;OAEG;IACH,UAAiB,KAAK;QACpB;;WAEG;QACH,YAAY,EAAE,MAAM,CAAC;QAErB;;WAEG;QACH,aAAa,EAAE,MAAM,CAAC;QAEtB;;WAEG;QACH,YAAY,EAAE,MAAM,CAAC;QAErB;;WAEG;QACH,IAAI,EAAE,QAAQ,CAAC;QAEf;;WAEG;QACH,mBAAmB,CAAC,EAAE,KAAK,CAAC,iBAAiB,CAAC;KAC/C;IAED,UAAiB,KAAK,CAAC;QACrB;;WAEG;QACH,UAAiB,iBAAiB;YAChC;;eAEG;YACH,YAAY,CAAC,EAAE,MAAM,CAAC;YAEtB;;eAEG;YACH,WAAW,CAAC,EAAE,MAAM,CAAC;SACtB;KACF;CACF;AAED;;;GAGG;AACH,MAAM,WAAW,oBAAoB;IACnC;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IAEjB;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IAEjB;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,QAAQ,CAAC,EAAE,KAAK,CAAC,oBAAoB,CAAC,CAAC;IAEvC;;OAEG;IACH,KAAK,CAAC,EAAE,oBAAoB,CAAC,KAAK,CAAC;IAEnC;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,iBAAiB,CAAC,CAAC;CAClC;AAED,yBAAiB,oBAAoB,CAAC;IACpC;;OAEG;IACH,UAAiB,KAAK;QACpB;;WAEG;QACH,QAAQ,EAAE,MAAM,CAAC;QAEjB;;WAEG;QACH,IAAI,EAAE,UAAU,CAAC;KAClB;CACF;AAED,MAAM,WAAW,iBAAiB;IAChC;;OAEG;IACH,GAAG,EAAE,MAAM,CAAC;IAEZ;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;CACd;AAED;;;GAGG;AACH,MAAM,MAAM,2BAA2B,GAAG,aAAa,GAAG,oBAAoB,CAAC;AAE/E,MAAM,MAAM,yBAAyB,CACnC,cAAc,SAAS,QAAQ,CAAC,mBAAmB,GAAG,SAAS,GAAG,QAAQ,CAAC,mBAAmB,GAAG,SAAS,IACxG,qCAAqC,CAAC,cAAc,CAAC,GAAG,kCAAkC,CAAC;AAE/F,MAAM,WAAW,6BAA6B,CAC5C,cAAc,SAAS,QAAQ,CAAC,mBAAmB,GAAG,SAAS,GAAG,QAAQ,CAAC,mBAAmB,GAAG,SAAS;IAE1G;;;OAGG;IACH,IAAI,EAAE,UAAU,CAAC;IAEjB;;;;OAIG;IACH,KAAK,EAAE,CAAC,MAAM,GAAG,EAAE,CAAC,GAAG,QAAQ,CAAC,UAAU,CAAC;IAE3C;;;;;OAKG;IACH,iBAAiB,CAAC,EAAE,MAAM,GAAG,yBAAyB,CAAC,SAAS,GAAG,IAAI,CAAC;IAExE;;;;;;OAMG;IACH,OAAO,CAAC,EAAE,KAAK,CAAC,oBAAoB,CAAC,CAAC;IAEtC;;;;OAIG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB;;;;;OAKG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;IAEhB;;;;OAIG;IACH,eAAe,CAAC,EAAE,cAAc,CAAC;IAEjC;;;;;;;;;OASG;IACH,MAAM,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IAExB;;;;;;OAMG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;;;;;OAMG;IACH,uBAAuB,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC;CACrD;AAED,yBAAiB,yBAAyB,CAAC;IACzC,UAAiB,SAAS;QACxB;;WAEG;QACH,IAAI,EAAE,YAAY,CAAC;QAEnB;;WAEG;QACH,iBAAiB,CAAC,EAAE,MAAM,CAAC;QAE3B;;;;WAIG;QACH,mBAAmB,CAAC,EAAE,MAAM,CAAC;QAE7B;;;;WAIG;QACH,SAAS,CAAC,EAAE,MAAM,CAAC;KACpB;IAED,KAAY,qCAAqC,GAAG,iBAAiB,CAAC,qCAAqC,CAAC;IAC5G,KAAY,kCAAkC,GAAG,iBAAiB,CAAC,kCAAkC,CAAC;CACvG;AAED,MAAM,WAAW,qCAAqC,CACpD,cAAc,SAAS,QAAQ,CAAC,mBAAmB,GAAG,SAAS,GAAG,QAAQ,CAAC,mBAAmB,GAAG,SAAS,CAC1G,SAAQ,6BAA6B,CAAC,cAAc,CAAC;IACrD;;;;;;;;;OASG;IACH,MAAM,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC;CACvB;AAED,MAAM,WAAW,kCAAmC,SAAQ,6BAA6B;IACvF;;;;;;;;;OASG;IACH,MAAM,EAAE,IAAI,CAAC;CACd;AAED,MAAM,CAAC,OAAO,WAAW,cAAc,CAAC;IACtC,OAAO,EACL,KAAK,aAAa,IAAI,aAAa,EACnC,KAAK,oBAAoB,IAAI,oBAAoB,EACjD,KAAK,oBAAoB,IAAI,oBAAoB,EACjD,KAAK,wBAAwB,IAAI,wBAAwB,EACzD,KAAK,2BAA2B,IAAI,2BAA2B,EAC/D,KAAK,0BAA0B,IAAI,0BAA0B,EAC7D,KAAK,oBAAoB,IAAI,oBAAoB,EACjD,KAAK,iBAAiB,IAAI,iBAAiB,EAC3C,KAAK,2BAA2B,IAAI,2BAA2B,EAC/D,KAAK,yBAAyB,IAAI,yBAAyB,EAC3D,KAAK,qCAAqC,IAAI,qCAAqC,EACnF,KAAK,kCAAkC,IAAI,kCAAkC,GAC9E,CAAC;CACH"}