import { NextRequest, NextResponse } from 'next/server'
import { DemoAuthService } from '@/lib/demo-auth'

export async function POST(request: NextRequest) {
  try {
    const { name, email, password, role } = await request.json()

    // Validate input
    if (!name || !email || !password || !role) {
      return NextResponse.json(
        { message: 'All fields are required' },
        { status: 400 }
      )
    }

    // Check if user already exists in demo system
    if (DemoAuthService.emailExists(email)) {
      return NextResponse.json(
        { message: 'User with this email already exists' },
        { status: 400 }
      )
    }

    // Create user in demo system
    const user = DemoAuthService.addUser({
      name,
      email,
      password, // In demo mode, we store plain password for simplicity
      role: role.toUpperCase() as 'ADMIN' | 'TENANT' | 'STUDENT'
    })

    // Remove password from response
    const { password: _, ...userWithoutPassword } = user

    return NextResponse.json(
      {
        message: 'User created successfully (Demo Mode)',
        user: {
          ...userWithoutPassword,
          createdAt: new Date()
        }
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('Registration error:', error)
    return NextResponse.json(
      { message: 'Internal server error: ' + error.message },
      { status: 500 }
    )
  }
}
