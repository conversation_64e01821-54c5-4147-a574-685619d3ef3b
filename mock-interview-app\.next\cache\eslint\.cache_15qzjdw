[{"C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\app\\admin\\dashboard\\page.tsx": "1", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\app\\api\\ai\\evaluate\\route.ts": "2", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\app\\api\\auth\\register\\route.ts": "3", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\app\\api\\auth\\[...nextauth]\\route.ts": "4", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\app\\api\\interviews\\route.ts": "5", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\app\\auth\\signin\\page.tsx": "6", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\app\\auth\\signup\\page.tsx": "7", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\app\\demo\\page.tsx": "8", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\app\\interview\\[id]\\page.tsx": "9", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\app\\layout.tsx": "10", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\app\\page.tsx": "11", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\app\\student\\dashboard\\page.tsx": "12", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\app\\tenant\\dashboard\\page.tsx": "13", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\app\\tenant\\questions\\page.tsx": "14", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\navigation\\main-nav.tsx": "15", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\providers\\session-provider.tsx": "16", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\ui\\avatar.tsx": "17", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\ui\\button.tsx": "18", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\ui\\card.tsx": "19", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\ui\\dialog.tsx": "20", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\ui\\dropdown-menu.tsx": "21", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\ui\\form.tsx": "22", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\ui\\input.tsx": "23", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\ui\\label.tsx": "24", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\ui\\navigation-menu.tsx": "25", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\ui\\select.tsx": "26", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\ui\\table.tsx": "27", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\ui\\tabs.tsx": "28", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\ui\\textarea.tsx": "29", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\lib\\ai-services.ts": "30", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\lib\\auth.ts": "31", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\lib\\demo-auth.ts": "32", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\lib\\email-service.ts": "33", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\lib\\prisma.ts": "34", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\lib\\utils.ts": "35", "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\types\\next-auth.d.ts": "36"}, {"size": 11059, "mtime": 1749875230286, "results": "37", "hashOfConfig": "38"}, {"size": 5738, "mtime": 1749875326193, "results": "39", "hashOfConfig": "38"}, {"size": 1411, "mtime": 1749877312644, "results": "40", "hashOfConfig": "38"}, {"size": 157, "mtime": 1749874675170, "results": "41", "hashOfConfig": "38"}, {"size": 6429, "mtime": 1749875302180, "results": "42", "hashOfConfig": "38"}, {"size": 6692, "mtime": 1749877403013, "results": "43", "hashOfConfig": "38"}, {"size": 7335, "mtime": 1749874717666, "results": "44", "hashOfConfig": "38"}, {"size": 11832, "mtime": 1749877440148, "results": "45", "hashOfConfig": "38"}, {"size": 11530, "mtime": 1749875272761, "results": "46", "hashOfConfig": "38"}, {"size": 986, "mtime": 1749875479561, "results": "47", "hashOfConfig": "38"}, {"size": 10252, "mtime": 1749877372488, "results": "48", "hashOfConfig": "38"}, {"size": 10537, "mtime": 1749874873036, "results": "49", "hashOfConfig": "38"}, {"size": 11630, "mtime": 1749875190900, "results": "50", "hashOfConfig": "38"}, {"size": 17508, "mtime": 1749875383843, "results": "51", "hashOfConfig": "38"}, {"size": 5546, "mtime": 1749874668517, "results": "52", "hashOfConfig": "38"}, {"size": 288, "mtime": 1749875459735, "results": "53", "hashOfConfig": "38"}, {"size": 1097, "mtime": 1749874625141, "results": "54", "hashOfConfig": "38"}, {"size": 2123, "mtime": 1749874624906, "results": "55", "hashOfConfig": "38"}, {"size": 1989, "mtime": 1749874624955, "results": "56", "hashOfConfig": "38"}, {"size": 3982, "mtime": 1749874625089, "results": "57", "hashOfConfig": "38"}, {"size": 8284, "mtime": 1749874625198, "results": "58", "hashOfConfig": "38"}, {"size": 3759, "mtime": 1749874625052, "results": "59", "hashOfConfig": "38"}, {"size": 967, "mtime": 1749874624965, "results": "60", "hashOfConfig": "38"}, {"size": 611, "mtime": 1749874624973, "results": "61", "hashOfConfig": "38"}, {"size": 6664, "mtime": 1749874625223, "results": "62", "hashOfConfig": "38"}, {"size": 6253, "mtime": 1749874926452, "results": "63", "hashOfConfig": "38"}, {"size": 2448, "mtime": 1749874625115, "results": "64", "hashOfConfig": "38"}, {"size": 1969, "mtime": 1749874625128, "results": "65", "hashOfConfig": "38"}, {"size": 759, "mtime": 1749875403602, "results": "66", "hashOfConfig": "38"}, {"size": 5136, "mtime": 1749874444231, "results": "67", "hashOfConfig": "38"}, {"size": 1330, "mtime": 1749877277641, "results": "68", "hashOfConfig": "38"}, {"size": 1773, "mtime": 1749877201749, "results": "69", "hashOfConfig": "38"}, {"size": 5655, "mtime": 1749876593394, "results": "70", "hashOfConfig": "38"}, {"size": 279, "mtime": 1749874405851, "results": "71", "hashOfConfig": "38"}, {"size": 166, "mtime": 1749874570336, "results": "72", "hashOfConfig": "38"}, {"size": 366, "mtime": 1749874423724, "results": "73", "hashOfConfig": "38"}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "csiagz", {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\app\\admin\\dashboard\\page.tsx", ["182", "183"], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\app\\api\\ai\\evaluate\\route.ts", [], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\app\\api\\auth\\register\\route.ts", ["184"], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\app\\api\\interviews\\route.ts", ["185"], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\app\\auth\\signin\\page.tsx", ["186"], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\app\\auth\\signup\\page.tsx", ["187"], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\app\\demo\\page.tsx", [], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\app\\interview\\[id]\\page.tsx", ["188", "189", "190"], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\app\\layout.tsx", [], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\app\\page.tsx", ["191", "192", "193"], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\app\\student\\dashboard\\page.tsx", ["194", "195", "196"], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\app\\tenant\\dashboard\\page.tsx", ["197", "198"], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\app\\tenant\\questions\\page.tsx", ["199", "200", "201"], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\navigation\\main-nav.tsx", [], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\providers\\session-provider.tsx", [], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\ui\\avatar.tsx", [], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\ui\\button.tsx", [], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\ui\\card.tsx", [], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\ui\\form.tsx", [], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\ui\\input.tsx", [], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\ui\\label.tsx", [], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\ui\\navigation-menu.tsx", [], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\ui\\select.tsx", [], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\ui\\table.tsx", [], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\ui\\tabs.tsx", [], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\lib\\ai-services.ts", ["202", "203", "204"], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\lib\\auth.ts", [], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\lib\\demo-auth.ts", [], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\lib\\email-service.ts", ["205", "206", "207"], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\lib\\prisma.ts", [], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\lib\\utils.ts", [], [], "C:\\Automation\\Buildmockinterview\\mock-interview-app\\src\\types\\next-auth.d.ts", ["208"], [], {"ruleId": "209", "severity": 2, "message": "210", "line": 219, "column": 47, "nodeType": "211", "messageId": "212", "endLine": 219, "endColumn": 50, "suggestions": "213"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 255, "column": 47, "nodeType": "211", "messageId": "212", "endLine": 255, "endColumn": 50, "suggestions": "214"}, {"ruleId": "215", "severity": 2, "message": "216", "line": 33, "column": 23, "nodeType": null, "messageId": "217", "endLine": 33, "endColumn": 24}, {"ruleId": "215", "severity": 2, "message": "218", "line": 18, "column": 13, "nodeType": null, "messageId": "217", "endLine": 18, "endColumn": 25}, {"ruleId": "215", "severity": 2, "message": "219", "line": 45, "column": 14, "nodeType": null, "messageId": "217", "endLine": 45, "endColumn": 19}, {"ruleId": "215", "severity": 2, "message": "219", "line": 64, "column": 14, "nodeType": null, "messageId": "217", "endLine": 64, "endColumn": 19}, {"ruleId": "215", "severity": 2, "message": "220", "line": 19, "column": 3, "nodeType": null, "messageId": "217", "endLine": 19, "endColumn": 7}, {"ruleId": "209", "severity": 2, "message": "210", "line": 27, "column": 14, "nodeType": "211", "messageId": "212", "endLine": 27, "endColumn": 17, "suggestions": "221"}, {"ruleId": "222", "severity": 1, "message": "223", "line": 59, "column": 6, "nodeType": "224", "endLine": 59, "endColumn": 42, "suggestions": "225"}, {"ruleId": "215", "severity": 2, "message": "226", "line": 3, "column": 16, "nodeType": null, "messageId": "217", "endLine": 3, "endColumn": 27}, {"ruleId": "215", "severity": 2, "message": "227", "line": 12, "column": 3, "nodeType": null, "messageId": "217", "endLine": 12, "endColumn": 14}, {"ruleId": "215", "severity": 2, "message": "228", "line": 13, "column": 3, "nodeType": null, "messageId": "217", "endLine": 13, "endColumn": 7}, {"ruleId": "229", "severity": 2, "message": "230", "line": 119, "column": 19, "nodeType": "231", "messageId": "232", "suggestions": "233"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 192, "column": 55, "nodeType": "211", "messageId": "212", "endLine": 192, "endColumn": 58, "suggestions": "234"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 232, "column": 47, "nodeType": "211", "messageId": "212", "endLine": 232, "endColumn": 50, "suggestions": "235"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 216, "column": 53, "nodeType": "211", "messageId": "212", "endLine": 216, "endColumn": 56, "suggestions": "236"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 256, "column": 50, "nodeType": "211", "messageId": "212", "endLine": 256, "endColumn": 53, "suggestions": "237"}, {"ruleId": "215", "severity": 2, "message": "238", "line": 30, "column": 3, "nodeType": null, "messageId": "217", "endLine": 30, "endColumn": 9}, {"ruleId": "209", "severity": 2, "message": "210", "line": 46, "column": 15, "nodeType": "211", "messageId": "212", "endLine": 46, "endColumn": 18, "suggestions": "239"}, {"ruleId": "222", "severity": 1, "message": "240", "line": 89, "column": 6, "nodeType": "224", "endLine": 89, "endColumn": 59, "suggestions": "241"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 32, "column": 17, "nodeType": "211", "messageId": "212", "endLine": 32, "endColumn": 20, "suggestions": "242"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 118, "column": 14, "nodeType": "211", "messageId": "212", "endLine": 118, "endColumn": 17, "suggestions": "243"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 150, "column": 14, "nodeType": "211", "messageId": "212", "endLine": 150, "endColumn": 17, "suggestions": "244"}, {"ruleId": "215", "severity": 2, "message": "245", "line": 33, "column": 13, "nodeType": null, "messageId": "217", "endLine": 33, "endColumn": 17}, {"ruleId": "215", "severity": 2, "message": "246", "line": 52, "column": 16, "nodeType": null, "messageId": "217", "endLine": 52, "endColumn": 23}, {"ruleId": "215", "severity": 2, "message": "246", "line": 70, "column": 16, "nodeType": null, "messageId": "217", "endLine": 70, "endColumn": 23}, {"ruleId": "215", "severity": 2, "message": "247", "line": 1, "column": 8, "nodeType": null, "messageId": "217", "endLine": 1, "endColumn": 16}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["248", "249"], ["250", "251"], "@typescript-eslint/no-unused-vars", "'_' is assigned a value but never used.", "unusedVar", "'searchParams' is assigned a value but never used.", "'error' is defined but never used.", "'Code' is defined but never used.", ["252", "253"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchInterviewData'. Either include it or remove the dependency array.", "ArrayExpression", ["254"], "'CardContent' is defined but never used.", "'CheckCircle' is defined but never used.", "'Star' is defined but never used.", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["255", "256", "257", "258"], ["259", "260"], ["261", "262"], ["263", "264"], ["265", "266"], "'Filter' is defined but never used.", ["267", "268"], "React Hook useEffect has a missing dependency: 'filterQuestions'. Either include it or remove the dependency array.", ["269"], ["270", "271"], ["272", "273"], ["274", "275"], "'info' is assigned a value but never used.", "'dbError' is defined but never used.", "'NextAuth' is defined but never used.", {"messageId": "276", "fix": "277", "desc": "278"}, {"messageId": "279", "fix": "280", "desc": "281"}, {"messageId": "276", "fix": "282", "desc": "278"}, {"messageId": "279", "fix": "283", "desc": "281"}, {"messageId": "276", "fix": "284", "desc": "278"}, {"messageId": "279", "fix": "285", "desc": "281"}, {"desc": "286", "fix": "287"}, {"messageId": "288", "data": "289", "fix": "290", "desc": "291"}, {"messageId": "288", "data": "292", "fix": "293", "desc": "294"}, {"messageId": "288", "data": "295", "fix": "296", "desc": "297"}, {"messageId": "288", "data": "298", "fix": "299", "desc": "300"}, {"messageId": "276", "fix": "301", "desc": "278"}, {"messageId": "279", "fix": "302", "desc": "281"}, {"messageId": "276", "fix": "303", "desc": "278"}, {"messageId": "279", "fix": "304", "desc": "281"}, {"messageId": "276", "fix": "305", "desc": "278"}, {"messageId": "279", "fix": "306", "desc": "281"}, {"messageId": "276", "fix": "307", "desc": "278"}, {"messageId": "279", "fix": "308", "desc": "281"}, {"messageId": "276", "fix": "309", "desc": "278"}, {"messageId": "279", "fix": "310", "desc": "281"}, {"desc": "311", "fix": "312"}, {"messageId": "276", "fix": "313", "desc": "278"}, {"messageId": "279", "fix": "314", "desc": "281"}, {"messageId": "276", "fix": "315", "desc": "278"}, {"messageId": "279", "fix": "316", "desc": "281"}, {"messageId": "276", "fix": "317", "desc": "278"}, {"messageId": "279", "fix": "318", "desc": "281"}, "suggestUnknown", {"range": "319", "text": "320"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "321", "text": "322"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "323", "text": "320"}, {"range": "324", "text": "322"}, {"range": "325", "text": "320"}, {"range": "326", "text": "322"}, "Update the dependencies array to be: [session, status, router, params.id, fetchInterviewData]", {"range": "327", "text": "328"}, "replaceWithAlt", {"alt": "329"}, {"range": "330", "text": "331"}, "Replace with `&apos;`.", {"alt": "332"}, {"range": "333", "text": "334"}, "Replace with `&lsquo;`.", {"alt": "335"}, {"range": "336", "text": "337"}, "Replace with `&#39;`.", {"alt": "338"}, {"range": "339", "text": "340"}, "Replace with `&rsquo;`.", {"range": "341", "text": "320"}, {"range": "342", "text": "322"}, {"range": "343", "text": "320"}, {"range": "344", "text": "322"}, {"range": "345", "text": "320"}, {"range": "346", "text": "322"}, {"range": "347", "text": "320"}, {"range": "348", "text": "322"}, {"range": "349", "text": "320"}, {"range": "350", "text": "322"}, "Update the dependencies array to be: [questions, searchTerm, filterType, filterDifficulty, filterQuestions]", {"range": "351", "text": "352"}, {"range": "353", "text": "320"}, {"range": "354", "text": "322"}, {"range": "355", "text": "320"}, {"range": "356", "text": "322"}, {"range": "357", "text": "320"}, {"range": "358", "text": "322"}, [7239, 7242], "unknown", [7239, 7242], "never", [8807, 8810], [8807, 8810], [629, 632], [629, 632], [1589, 1625], "[session, status, router, params.id, fetchInterviewData]", "&apos;", [3133, 3200], "\n              Here&apos;s your interview practice overview\n            ", "&lsquo;", [3133, 3200], "\n              Here&lsquo;s your interview practice overview\n            ", "&#39;", [3133, 3200], "\n              Here&#39;s your interview practice overview\n            ", "&rsquo;", [3133, 3200], "\n              Here&rsquo;s your interview practice overview\n            ", [6284, 6287], [6284, 6287], [8063, 8066], [8063, 8066], [7192, 7195], [7192, 7195], [9069, 9072], [9069, 9072], [1054, 1057], [1054, 1057], [2120, 2173], "[questions, searchTerm, filterType, filterDifficulty, filterQuestions]", [722, 725], [722, 725], [3230, 3233], [3230, 3233], [4219, 4222], [4219, 4222]]