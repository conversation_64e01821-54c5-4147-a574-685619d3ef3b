import { NextRequest, NextResponse } from 'next/server'
import { EmailService } from '@/lib/email-service'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, studentName, interviewTitle, scheduledAt } = body

    console.log('Received invitation request:', { email, studentName, interviewTitle, scheduledAt })

    // Validate required fields
    if (!email || !studentName || !interviewTitle) {
      return NextResponse.json(
        { error: 'Missing required fields: email, studentName, interviewTitle' },
        { status: 400 }
      )
    }

    // Create invitation
    const invitation = EmailService.createInvitation(
      email,
      studentName,
      interviewTitle,
      scheduledAt ? new Date(scheduledAt) : new Date(Date.now() + 2 * 60 * 60 * 1000) // Default: 2 hours from now
    )

    console.log('Created invitation:', invitation)

    // Send invitation email
    console.log('Attempting to send email with mode:', EmailService.getEmailMode())
    const success = await EmailService.sendInterviewInvitationWithToken(invitation)
    console.log('Email send result:', success)

    if (success) {
      return NextResponse.json({
        success: true,
        message: 'Interview invitation sent successfully',
        invitation: {
          id: invitation.id,
          token: invitation.token,
          email: invitation.email,
          studentName: invitation.studentName,
          interviewTitle: invitation.interviewTitle,
          scheduledAt: invitation.scheduledAt,
          status: invitation.status,
          invitationUrl: `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/interview/invite/${invitation.token}`
        }
      })
    } else {
      return NextResponse.json(
        { error: 'Failed to send invitation email' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Error sending invitation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
