// Create a demo invitation for testing
const crypto = require('crypto');

function generateInvitationToken() {
  return crypto.randomBytes(32).toString('hex');
}

function createDemoInvitation() {
  const token = generateInvitationToken();
  const invitation = {
    id: crypto.randomUUID(),
    token: token,
    email: '<EMAIL>',
    studentName: '<PERSON>',
    interviewTitle: 'Technical Interview - Software Developer Position',
    scheduledAt: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours from now
    status: 'pending',
    createdAt: new Date(),
    expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
  };

  console.log('Demo Invitation Created:');
  console.log('======================');
  console.log(`Email: ${invitation.email}`);
  console.log(`Student: ${invitation.studentName}`);
  console.log(`Position: ${invitation.interviewTitle}`);
  console.log(`Token: ${invitation.token}`);
  console.log(`Scheduled: ${invitation.scheduledAt.toLocaleString()}`);
  console.log('');
  console.log('Direct Links:');
  console.log('=============');
  console.log(`🔗 Invitation Page: http://localhost:3000/interview/invite/${invitation.token}`);
  console.log(`🎥 Video Interview: http://localhost:3000/interview/video/${invitation.token}`);
  console.log(`📧 Mailinator Inbox: https://www.mailinator.com/v4/public/inboxes.jsp?to=ramprasad`);
  console.log('');
  console.log('Demo Instructions:');
  console.log('==================');
  console.log('1. Visit the invitation page to see the email invitation flow');
  console.log('2. Sign up/login with demo credentials');
  console.log('3. Go through the briefing and camera setup');
  console.log('4. Start the video interview with AI interviewer');
  console.log('5. Answer the 5 interview questions');
  console.log('6. Complete the interview and see congratulations');
  console.log('');
  console.log('Demo Credentials:');
  console.log('=================');
  console.log('Email: <EMAIL>');
  console.log('Password: demo123');
  console.log('');

  return invitation;
}

// Create and display demo invitation
const demoInvitation = createDemoInvitation();

// Also create a simple HTML email template
const emailHTML = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Video Interview Invitation</title>
</head>
<body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #f5f5f5; padding: 20px;">
    <div style="background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px;">🎯 Video Interview Invitation</h1>
        </div>
        
        <!-- Content -->
        <div style="padding: 30px;">
            <h2 style="color: #333; margin-top: 0;">Hello ${demoInvitation.studentName},</h2>
            
            <p style="font-size: 16px; line-height: 1.6; color: #555;">
                You have been invited to participate in a video interview for the <strong>${demoInvitation.interviewTitle}</strong>.
            </p>
            
            <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3 style="color: #667eea; margin-top: 0;">📋 Interview Details:</h3>
                <p style="margin: 5px 0;"><strong>Position:</strong> ${demoInvitation.interviewTitle}</p>
                <p style="margin: 5px 0;"><strong>Scheduled:</strong> ${demoInvitation.scheduledAt.toLocaleString()}</p>
                <p style="margin: 5px 0;"><strong>Duration:</strong> 45 minutes</p>
                <p style="margin: 5px 0;"><strong>Format:</strong> AI-powered video interview</p>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="http://localhost:3000/interview/invite/${demoInvitation.token}" 
                   style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block; font-size: 16px;">
                    🚀 Join Interview
                </a>
            </div>
            
            <div style="background: #e8f4fd; padding: 15px; border-radius: 8px; border-left: 4px solid #667eea;">
                <h4 style="margin-top: 0; color: #667eea;">💡 What to Expect:</h4>
                <ul style="margin: 0; padding-left: 20px; color: #555;">
                    <li>AI-powered interviewer with speaking capabilities</li>
                    <li>5 carefully crafted interview questions</li>
                    <li>Video recording for review</li>
                    <li>Real-time feedback and guidance</li>
                </ul>
            </div>
            
            <p style="color: #666; font-size: 14px; margin-top: 20px;">
                This invitation will expire in 7 days. Please ensure you have a stable internet connection and a working camera/microphone.
            </p>
        </div>
        
        <!-- Footer -->
        <div style="background: #333; color: white; padding: 20px; text-align: center;">
            <p style="margin: 0; font-size: 14px;">
                Powered by AI Interview System | Mock Interview Platform
            </p>
        </div>
    </div>
</body>
</html>
`;

console.log('Email HTML Template saved to: demo-email.html');
require('fs').writeFileSync('demo-email.html', emailHTML);
