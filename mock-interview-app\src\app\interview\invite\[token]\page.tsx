'use client'

import { useEffect, useState } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { EmailService, InterviewInvitation } from '@/lib/email-service'
import { DemoAuth } from '@/lib/demo-auth'
import { 
  Calendar, 
  Clock, 
  User, 
  Mail, 
  Lock, 
  CheckCircle, 
  AlertCircle,
  Video,
  Mic,
  Monitor
} from 'lucide-react'

export default function InvitationPage() {
  const params = useParams()
  const router = useRouter()
  const token = params.token as string

  const [invitation, setInvitation] = useState<InterviewInvitation | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [showAuth, setShowAuth] = useState(false)
  const [isLogin, setIsLogin] = useState(true)
  const [authLoading, setAuthLoading] = useState(false)

  // Auth form state
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [fullName, setFullName] = useState('')

  useEffect(() => {
    if (token) {
      const invitationData = EmailService.getInvitationByToken(token)
      if (invitationData) {
        setInvitation(invitationData)
        setEmail(invitationData.email)
        setFullName(invitationData.studentName)
      } else {
        setError('Invalid or expired invitation link')
      }
      setLoading(false)
    }
  }, [token])

  const handleAuthSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setAuthLoading(true)
    setError('')

    try {
      if (isLogin) {
        // Login
        const success = DemoAuth.login(email, password)
        if (success) {
          EmailService.updateInvitationStatus(token, 'ACCEPTED')
          router.push(`/interview/briefing/${token}`)
        } else {
          setError('Invalid email or password')
        }
      } else {
        // Signup
        if (password !== confirmPassword) {
          setError('Passwords do not match')
          setAuthLoading(false)
          return
        }

        if (password.length < 6) {
          setError('Password must be at least 6 characters')
          setAuthLoading(false)
          return
        }

        const success = DemoAuth.register(email, password, fullName, 'STUDENT')
        if (success) {
          EmailService.updateInvitationStatus(token, 'ACCEPTED')
          router.push(`/interview/briefing/${token}`)
        } else {
          setError('Registration failed. Email might already exist.')
        }
      }
    } catch (err) {
      setError('Authentication failed. Please try again.')
    }

    setAuthLoading(false)
  }

  const handleProceedToInterview = () => {
    const currentUser = DemoAuth.getCurrentUser()
    if (currentUser) {
      EmailService.updateInvitationStatus(token, 'ACCEPTED')
      router.push(`/interview/briefing/${token}`)
    } else {
      setShowAuth(true)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p>Loading invitation...</p>
        </div>
      </div>
    )
  }

  if (error && !invitation) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <Card className="w-full max-w-md bg-gray-800 border-gray-700">
          <CardContent className="p-6 text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-bold text-white mb-2">Invalid Invitation</h2>
            <p className="text-gray-400 mb-4">{error}</p>
            <Button onClick={() => router.push('/')} variant="outline">
              Go to Home
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (invitation?.status === 'EXPIRED') {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <Card className="w-full max-w-md bg-gray-800 border-gray-700">
          <CardContent className="p-6 text-center">
            <Clock className="h-12 w-12 text-orange-500 mx-auto mb-4" />
            <h2 className="text-xl font-bold text-white mb-2">Invitation Expired</h2>
            <p className="text-gray-400 mb-4">
              This invitation has expired. Please contact the interviewer for a new invitation.
            </p>
            <Button onClick={() => router.push('/')} variant="outline">
              Go to Home
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-3xl font-bold text-white mb-2">🎯 Video Interview Invitation</h1>
          <p className="text-blue-100">AI-Powered Interview Experience</p>
        </div>
      </div>

      <div className="max-w-4xl mx-auto p-6">
        {!showAuth ? (
          // Invitation Details
          <div className="grid md:grid-cols-2 gap-6">
            {/* Interview Details */}
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Video className="h-5 w-5 mr-2 text-blue-500" />
                  Interview Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <User className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-400">Candidate</p>
                    <p className="text-white font-medium">{invitation?.studentName}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Mail className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-400">Email</p>
                    <p className="text-white font-medium">{invitation?.email}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Calendar className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-400">Position</p>
                    <p className="text-white font-medium">{invitation?.interviewTitle}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Clock className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-400">Scheduled</p>
                    <p className="text-white font-medium">
                      {invitation?.scheduledAt.toLocaleDateString()} at {invitation?.scheduledAt.toLocaleTimeString()}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Technical Requirements */}
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Monitor className="h-5 w-5 mr-2 text-green-500" />
                  Technical Requirements
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-gray-300">Stable internet connection</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Video className="h-4 w-4 text-green-500" />
                    <span className="text-gray-300">Working camera</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Mic className="h-4 w-4 text-green-500" />
                    <span className="text-gray-300">Working microphone</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Monitor className="h-4 w-4 text-green-500" />
                    <span className="text-gray-300">Modern web browser</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-gray-300">Quiet environment</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        ) : (
          // Authentication Form
          <div className="max-w-md mx-auto">
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white text-center">
                  {isLogin ? 'Login to Continue' : 'Create Account'}
                </CardTitle>
                <CardDescription className="text-center text-gray-400">
                  {isLogin ? 'Sign in to join your interview' : 'Create an account to proceed'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleAuthSubmit} className="space-y-4">
                  {!isLogin && (
                    <div>
                      <Label htmlFor="fullName" className="text-gray-300">Full Name</Label>
                      <Input
                        id="fullName"
                        type="text"
                        value={fullName}
                        onChange={(e) => setFullName(e.target.value)}
                        className="bg-gray-700 border-gray-600 text-white"
                        required
                      />
                    </div>
                  )}

                  <div>
                    <Label htmlFor="email" className="text-gray-300">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="bg-gray-700 border-gray-600 text-white"
                      required
                      disabled={!!invitation?.email}
                    />
                  </div>

                  <div>
                    <Label htmlFor="password" className="text-gray-300">Password</Label>
                    <Input
                      id="password"
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="bg-gray-700 border-gray-600 text-white"
                      required
                    />
                  </div>

                  {!isLogin && (
                    <div>
                      <Label htmlFor="confirmPassword" className="text-gray-300">Confirm Password</Label>
                      <Input
                        id="confirmPassword"
                        type="password"
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        className="bg-gray-700 border-gray-600 text-white"
                        required
                      />
                    </div>
                  )}

                  {error && (
                    <div className="text-red-400 text-sm text-center">{error}</div>
                  )}

                  <Button 
                    type="submit" 
                    className="w-full bg-blue-600 hover:bg-blue-700"
                    disabled={authLoading}
                  >
                    {authLoading ? 'Processing...' : (isLogin ? 'Login' : 'Create Account')}
                  </Button>

                  <div className="text-center">
                    <button
                      type="button"
                      onClick={() => setIsLogin(!isLogin)}
                      className="text-blue-400 hover:text-blue-300 text-sm"
                    >
                      {isLogin ? "Don't have an account? Sign up" : "Already have an account? Login"}
                    </button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Action Button */}
        {!showAuth && (
          <div className="mt-8 text-center">
            <Button 
              onClick={handleProceedToInterview}
              size="lg"
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3"
            >
              🚀 Proceed to Interview
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
