import { NextRequest, NextResponse } from 'next/server'
import { OpenAIService, QuestionGenerationRequest } from '@/lib/openai-service'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { jobTitle, experience, skills, difficulty, questionType, count = 5 } = body

    // Validate input
    if (!jobTitle || !experience || !skills || !difficulty || !questionType) {
      return NextResponse.json(
        { error: 'Missing required fields: jobTitle, experience, skills, difficulty, questionType' },
        { status: 400 }
      )
    }

    const request: QuestionGenerationRequest = {
      jobTitle,
      experience,
      skills: Array.isArray(skills) ? skills : [skills],
      difficulty,
      questionType
    }

    // Check if OpenAI is configured
    if (!OpenAIService.isConfigured()) {
      // Mock questions for demo
      const mockQuestions = [
        {
          question: `Tell me about your experience with ${skills[0] || 'technology'}.`,
          type: questionType,
          expectedPoints: ["Specific examples", "Technical depth", "Problem-solving"],
          evaluationCriteria: ["Clarity", "Technical accuracy", "Real-world application"],
          timeLimit: 180,
          difficulty: difficulty
        },
        {
          question: `How would you approach debugging a complex issue in ${jobTitle} work?`,
          type: questionType,
          expectedPoints: ["Systematic approach", "Tools and techniques", "Communication"],
          evaluationCriteria: ["Methodology", "Technical knowledge", "Problem-solving"],
          timeLimit: 240,
          difficulty: difficulty
        },
        {
          question: `Describe a challenging project you worked on as a ${jobTitle}.`,
          type: questionType,
          expectedPoints: ["Project complexity", "Your role", "Outcome and learnings"],
          evaluationCriteria: ["Impact", "Leadership", "Technical skills"],
          timeLimit: 300,
          difficulty: difficulty
        }
      ].slice(0, count)

      return NextResponse.json({
        success: true,
        questions: mockQuestions,
        source: 'mock'
      })
    }

    // Use real OpenAI generation
    const questions = await OpenAIService.generateQuestions(request, count)

    return NextResponse.json({
      success: true,
      questions,
      source: 'openai'
    })

  } catch (error) {
    console.error('Error generating questions:', error)
    return NextResponse.json(
      { error: 'Failed to generate questions' },
      { status: 500 }
    )
  }
}
