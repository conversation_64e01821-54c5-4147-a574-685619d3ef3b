# Mock Interview Platform

A comprehensive AI-powered mock interview platform built with Next.js, featuring live video interviews, AI-based answer evaluation, multi-tenant support, and performance analytics.

## 🚀 Features

### Core Features
- **Multi-tenant Architecture**: Support for admins, organizations (tenants), and students
- **Live Video Interviews**: Real-time video/audio calls with screen sharing
- **AI-Powered Evaluation**: Automated assessment using OpenAI GPT-4 and Google Gemini
- **Question Bank Management**: Create and manage coding, theory, and behavioral questions
- **Performance Analytics**: Detailed reports and progress tracking
- **Email Notifications**: Automated interview invitations and results
- **Payment Integration**: Razorpay for subscription management
- **Video Recording**: Save and share interview recordings

### User Roles
- **Admin**: Platform management, tenant oversight, system analytics
- **Tenant (Organization)**: Manage students, schedule interviews, create questions
- **Student**: Participate in interviews, view results, track progress

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **UI Components**: Shadcn/ui, Radix UI
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: PostgreSQL
- **Authentication**: NextAuth.js
- **AI Services**: OpenAI GPT-4, Google Gemini
- **Video/Audio**: WebRTC, Socket.io
- **Email**: Nodemailer
- **Payment**: Razorpay
- **Deployment**: Vercel (Frontend), Railway/Render (Backend)

## 📋 Prerequisites

- Node.js 18+
- PostgreSQL database
- OpenAI API key
- Google AI API key
- Email service credentials (Gmail/SMTP)
- Razorpay account (for payments)

## 🚀 Getting Started

### 1. Clone the repository
```bash
git clone <repository-url>
cd mock-interview-app
```

### 2. Install dependencies
```bash
npm install
```

### 3. Environment Setup
Create a `.env` file in the root directory:

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/mockinterview?schema=public"

# NextAuth
NEXTAUTH_SECRET="your-secret-key-here"
NEXTAUTH_URL="http://localhost:3000"

# OpenAI API
OPENAI_API_KEY="your-openai-api-key"

# Google Gemini API
GOOGLE_AI_API_KEY="your-google-ai-api-key"

# Email Configuration
EMAIL_HOST="smtp.gmail.com"
EMAIL_PORT=587
EMAIL_USER="<EMAIL>"
EMAIL_PASS="your-app-password"

# Razorpay Configuration
RAZORPAY_KEY_ID="your-razorpay-key-id"
RAZORPAY_KEY_SECRET="your-razorpay-key-secret"

# File Upload
UPLOAD_DIR="./uploads"
MAX_FILE_SIZE=********

# Video Recording
RECORDING_STORAGE_PATH="./recordings"
```

### 4. Database Setup
```bash
# Generate Prisma client
npx prisma generate

# Run database migrations (when you have a database)
npx prisma db push

# Seed the database (optional)
npx prisma db seed
```

### 5. Run the development server
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## 📁 Project Structure

```
src/
├── app/                    # Next.js app directory
│   ├── admin/             # Admin dashboard and pages
│   ├── tenant/            # Tenant/organization pages
│   ├── student/           # Student dashboard and pages
│   ├── interview/         # Interview room interface
│   ├── auth/              # Authentication pages
│   └── api/               # API routes
├── components/            # Reusable UI components
│   ├── ui/               # Shadcn/ui components
│   └── navigation/       # Navigation components
├── lib/                  # Utility libraries
│   ├── prisma.ts         # Database client
│   ├── auth.ts           # Authentication config
│   ├── ai-services.ts    # AI integration
│   └── email-service.ts  # Email utilities
├── types/                # TypeScript type definitions
└── prisma/               # Database schema and migrations
```

## 🔧 Key Components

### Authentication
- Role-based access control (Admin, Tenant, Student)
- NextAuth.js with credentials provider
- Protected routes and API endpoints

### AI Integration
- **OpenAI GPT-4**: Coding question evaluation and feedback
- **Google Gemini**: Theory and behavioral question assessment
- Automated scoring and improvement suggestions

### Video Interview System
- WebRTC for peer-to-peer communication
- Screen sharing capabilities
- Real-time chat during interviews
- Recording functionality

### Multi-tenant Support
- Isolated data per organization
- Tenant-specific question banks
- Organization-level user management

## 🚀 Deployment

### Frontend (Vercel)
1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Database (Railway/Supabase)
1. Create a PostgreSQL database
2. Update DATABASE_URL in environment variables
3. Run migrations: `npx prisma db push`

### Backend Services
- Email service: Configure SMTP settings
- File storage: Set up cloud storage for recordings
- Payment processing: Configure Razorpay webhooks

## 📚 API Documentation

### Authentication Endpoints
- `POST /api/auth/register` - User registration
- `POST /api/auth/signin` - User login
- `POST /api/auth/signout` - User logout

### Interview Management
- `GET /api/interviews` - List interviews
- `POST /api/interviews` - Schedule new interview
- `PUT /api/interviews` - Update interview status

### AI Evaluation
- `POST /api/ai/evaluate` - Evaluate answers
- `PUT /api/ai/evaluate` - Generate final feedback

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit changes: `git commit -am 'Add new feature'`
4. Push to branch: `git push origin feature/new-feature`
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the GitHub repository
- Contact the development team
- Check the documentation wiki

## 🔮 Future Enhancements

- [ ] Mobile app development
- [ ] Advanced analytics dashboard
- [ ] Integration with more AI models
- [ ] Whiteboard collaboration
- [ ] Advanced video features (breakout rooms)
- [ ] API for third-party integrations
- [ ] Advanced reporting and exports
