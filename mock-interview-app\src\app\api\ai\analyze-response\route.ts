import { NextRequest, NextResponse } from 'next/server'
import { OpenAIService } from '@/lib/openai-service'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { question, answer } = body

    // Validate input
    if (!question || !answer) {
      return NextResponse.json(
        { error: 'Question and answer are required' },
        { status: 400 }
      )
    }

    // Check if OpenAI is configured
    if (!OpenAIService.isConfigured()) {
      // Mock response analysis
      const mockAnalysis = {
        score: Math.floor(Math.random() * 30) + 70, // 70-100
        feedback: "Good response! You demonstrated understanding of the concept.",
        suggestions: [
          "Consider providing more specific examples",
          "Try to be more concise in your explanation"
        ]
      }

      return NextResponse.json({
        success: true,
        analysis: mockAnalysis,
        source: 'mock'
      })
    }

    // Use real OpenAI analysis
    const analysis = await OpenAIService.analyzeResponse(question, answer)

    return NextResponse.json({
      success: true,
      analysis,
      source: 'openai'
    })

  } catch (error) {
    console.error('Error analyzing response:', error)
    return NextResponse.json(
      { error: 'Failed to analyze response' },
      { status: 500 }
    )
  }
}
