{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/lib/demo-auth.ts"], "sourcesContent": ["// Demo authentication system for testing without database\nexport interface DemoUser {\n  id: string\n  email: string\n  name: string\n  role: 'ADMIN' | 'TENANT' | 'STUDENT'\n  password: string\n}\n\n// Demo users for testing\nexport const DEMO_USERS: DemoUser[] = [\n  {\n    id: 'admin-1',\n    email: '<EMAIL>',\n    name: 'System Administrator',\n    role: 'ADMIN',\n    password: 'admin123'\n  },\n  {\n    id: 'tenant-1',\n    email: '<EMAIL>',\n    name: 'TechCorp Manager',\n    role: 'TENANT',\n    password: 'tenant123'\n  },\n  {\n    id: 'student-1',\n    email: '<EMAIL>',\n    name: '<PERSON>',\n    role: 'STUDENT',\n    password: 'student123'\n  },\n  {\n    id: 'student-2',\n    email: '<EMAIL>',\n    name: '<PERSON>',\n    role: 'STUDENT',\n    password: 'student123'\n  },\n  {\n    id: 'tenant-2',\n    email: '<EMAIL>',\n    name: 'StartupXYZ HR',\n    role: 'TENANT',\n    password: 'startup123'\n  }\n]\n\nexport class DemoAuthService {\n  // Validate demo user credentials\n  static validateUser(email: string, password: string): DemoUser | null {\n    const user = DEMO_USERS.find(u => u.email === email && u.password === password)\n    return user || null\n  }\n\n  // Check if email exists\n  static emailExists(email: string): boolean {\n    return DEMO_USERS.some(u => u.email === email)\n  }\n\n  // Add new demo user (for signup)\n  static addUser(userData: Omit<DemoUser, 'id'>): DemoUser {\n    const newUser: DemoUser = {\n      id: `demo-${Date.now()}`,\n      ...userData\n    }\n    DEMO_USERS.push(newUser)\n    return newUser\n  }\n\n  // Get user by ID\n  static getUserById(id: string): DemoUser | null {\n    return DEMO_USERS.find(u => u.id === id) || null\n  }\n\n  // Get all users (for admin)\n  static getAllUsers(): DemoUser[] {\n    return DEMO_USERS\n  }\n}\n\n// Client-side authentication helper\nexport class DemoAuth {\n  private static readonly STORAGE_KEY = 'demo_auth_user'\n\n  // Set current user in localStorage\n  static setCurrentUser(user: DemoUser): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(user))\n    }\n  }\n\n  // Get current user from localStorage\n  static getCurrentUser(): DemoUser | null {\n    if (typeof window !== 'undefined') {\n      const stored = localStorage.getItem(this.STORAGE_KEY)\n      if (stored) {\n        try {\n          return JSON.parse(stored)\n        } catch {\n          return null\n        }\n      }\n    }\n    return null\n  }\n\n  // Clear current user\n  static clearCurrentUser(): void {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem(this.STORAGE_KEY)\n    }\n  }\n\n  // Check if user is authenticated\n  static isAuthenticated(): boolean {\n    return this.getCurrentUser() !== null\n  }\n\n  // Login with email and password\n  static login(email: string, password: string): DemoUser | null {\n    const user = DemoAuthService.validateUser(email, password)\n    if (user) {\n      this.setCurrentUser(user)\n    }\n    return user\n  }\n\n  // Logout\n  static logout(): void {\n    this.clearCurrentUser()\n  }\n\n  // Register new user\n  static register(email: string, password: string, name: string, role: 'ADMIN' | 'TENANT' | 'STUDENT' = 'STUDENT'): DemoUser | null {\n    // Check if email already exists\n    if (DemoAuthService.emailExists(email)) {\n      return null\n    }\n\n    // Create new user\n    const newUser = DemoAuthService.addUser({\n      email,\n      password,\n      name,\n      role\n    })\n\n    // Auto-login the new user\n    this.setCurrentUser(newUser)\n    return newUser\n  }\n}\n"], "names": [], "mappings": "AAAA,0DAA0D;;;;;;AAUnD,MAAM,aAAyB;IACpC;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;IACZ;CACD;AAEM,MAAM;IACX,iCAAiC;IACjC,OAAO,aAAa,KAAa,EAAE,QAAgB,EAAmB;QACpE,MAAM,OAAO,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,SAAS,EAAE,QAAQ,KAAK;QACtE,OAAO,QAAQ;IACjB;IAEA,wBAAwB;IACxB,OAAO,YAAY,KAAa,EAAW;QACzC,OAAO,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;IAC1C;IAEA,iCAAiC;IACjC,OAAO,QAAQ,QAA8B,EAAY;QACvD,MAAM,UAAoB;YACxB,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;YACxB,GAAG,QAAQ;QACb;QACA,WAAW,IAAI,CAAC;QAChB,OAAO;IACT;IAEA,iBAAiB;IACjB,OAAO,YAAY,EAAU,EAAmB;QAC9C,OAAO,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO;IAC9C;IAEA,4BAA4B;IAC5B,OAAO,cAA0B;QAC/B,OAAO;IACT;AACF;AAGO,MAAM;IACX,OAAwB,cAAc,iBAAgB;IAEtD,mCAAmC;IACnC,OAAO,eAAe,IAAc,EAAQ;QAC1C,uCAAmC;;QAEnC;IACF;IAEA,qCAAqC;IACrC,OAAO,iBAAkC;QACvC,uCAAmC;;QASnC;QACA,OAAO;IACT;IAEA,qBAAqB;IACrB,OAAO,mBAAyB;QAC9B,uCAAmC;;QAEnC;IACF;IAEA,iCAAiC;IACjC,OAAO,kBAA2B;QAChC,OAAO,IAAI,CAAC,cAAc,OAAO;IACnC;IAEA,gCAAgC;IAChC,OAAO,MAAM,KAAa,EAAE,QAAgB,EAAmB;QAC7D,MAAM,OAAO,gBAAgB,YAAY,CAAC,OAAO;QACjD,IAAI,MAAM;YACR,IAAI,CAAC,cAAc,CAAC;QACtB;QACA,OAAO;IACT;IAEA,SAAS;IACT,OAAO,SAAe;QACpB,IAAI,CAAC,gBAAgB;IACvB;IAEA,oBAAoB;IACpB,OAAO,SAAS,KAAa,EAAE,QAAgB,EAAE,IAAY,EAAE,OAAuC,SAAS,EAAmB;QAChI,gCAAgC;QAChC,IAAI,gBAAgB,WAAW,CAAC,QAAQ;YACtC,OAAO;QACT;QAEA,kBAAkB;QAClB,MAAM,UAAU,gBAAgB,OAAO,CAAC;YACtC;YACA;YACA;YACA;QACF;QAEA,0BAA0B;QAC1B,IAAI,CAAC,cAAc,CAAC;QACpB,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 272, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Credentials<PERSON>rovider from 'next-auth/providers/credentials'\nimport { DemoAuthService } from './demo-auth'\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        // Use demo authentication\n        const user = DemoAuthService.validateUser(\n          credentials.email,\n          credentials.password\n        )\n\n        if (!user) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          role: user.role,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signUp: '/auth/signup'\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,0BAA0B;gBAC1B,MAAM,OAAO,4HAAA,CAAA,kBAAe,CAAC,YAAY,CACvC,YAAY,KAAK,EACjB,YAAY,QAAQ;gBAGtB,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;gBACjB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;YACxB;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;YAChC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from 'next-auth'\nimport { authOptions } from '@/lib/auth'\n\nconst handler = NextAuth(authOptions)\n\nexport { handler as GET, handler as POST }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE,oHAAA,CAAA,cAAW", "debugId": null}}]}