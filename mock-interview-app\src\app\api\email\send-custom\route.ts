import { NextRequest, NextResponse } from 'next/server'
import { EmailService } from '@/lib/email-service'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { to, subject, message } = body

    // Validate required fields
    if (!to || !subject || !message) {
      return NextResponse.json(
        { error: 'Missing required fields: to, subject, message' },
        { status: 400 }
      )
    }

    // Create HTML email content
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #2563eb;">Custom Email from Interview System</h2>
        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; margin: 20px 0;">
          ${message.replace(/\n/g, '<br>')}
        </div>
        <p style="color: #666; font-size: 14px;">
          Sent via Mailinator Integration Demo
        </p>
      </div>
    `

    // Send email
    const success = await EmailService.sendEmail({
      to,
      subject,
      html
    })

    if (success) {
      return NextResponse.json({
        success: true,
        message: 'Custom email sent successfully',
        details: {
          to,
          subject,
          sentAt: new Date().toISOString()
        }
      })
    } else {
      return NextResponse.json(
        { error: 'Failed to send custom email' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Error sending custom email:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
