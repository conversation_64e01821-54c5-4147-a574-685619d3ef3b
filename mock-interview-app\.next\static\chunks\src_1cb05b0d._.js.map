{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/lib/mailinator-service.ts"], "sourcesContent": ["export interface MailinatorConfig {\n  apiToken?: string\n  privateDomain?: string\n  usePublicDomain: boolean\n  webhookToken?: string\n}\n\nexport interface MailinatorMessage {\n  id: string\n  from: string\n  to: string\n  subject: string\n  text?: string\n  html?: string\n  timestamp: number\n}\n\nexport interface MailinatorInbox {\n  name: string\n  messages: MailinatorMessage[]\n}\n\nexport class MailinatorService {\n  private static config: MailinatorConfig = {\n    usePublicDomain: !process.env.MAILINATOR_API_TOKEN, // Use public if no API token\n    privateDomain: process.env.MAILINATOR_PRIVATE_DOMAIN,\n    apiToken: process.env.MAILINATOR_API_TOKEN,\n    webhookToken: process.env.MAILINATOR_WEBHOOK_TOKEN\n  }\n\n  static setConfig(config: Partial<MailinatorConfig>) {\n    this.config = { ...this.config, ...config }\n  }\n\n  static getConfig(): MailinatorConfig {\n    return { ...this.config }\n  }\n\n  // Send email via Mailinator API\n  static async sendEmail(\n    to: string,\n    subject: string,\n    html: string,\n    text?: string\n  ): Promise<boolean> {\n    try {\n      if (this.config.usePublicDomain) {\n        return await this.sendToPublicDomain(to, subject, html, text)\n      } else if (this.config.privateDomain && this.config.apiToken) {\n        return await this.sendToPrivateDomain(to, subject, html, text)\n      } else {\n        console.log('📧 Mailinator Demo Mode - Email would be sent:')\n        console.log(`To: ${to}`)\n        console.log(`Subject: ${subject}`)\n        console.log(`HTML: ${html.substring(0, 200)}...`)\n        return true\n      }\n    } catch (error) {\n      console.error('❌ Failed to send email via Mailinator:', error)\n      return false\n    }\n  }\n\n  // Send to Mailinator public domain (free tier)\n  private static async sendToPublicDomain(\n    to: string,\n    subject: string,\n    html: string,\n    text?: string\n  ): Promise<boolean> {\n    try {\n      // Extract inbox name from email\n      const inboxName = this.extractInboxName(to)\n      \n      // Use Mailinator's webhook endpoint for public domain\n      const webhookUrl = `https://www.mailinator.com/api/v2/domains/public/webhook/${inboxName}/`\n      \n      const payload = {\n        from: '<EMAIL>',\n        subject: subject,\n        text: text || this.htmlToText(html),\n        html: html,\n        to: inboxName\n      }\n\n      const response = await fetch(webhookUrl, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(payload)\n      })\n\n      if (response.ok) {\n        console.log(`✅ Email sent to Mailinator public inbox: ${inboxName}@mailinator.com`)\n        console.log(`📬 Check inbox at: https://www.mailinator.com/v4/public/inboxes.jsp?to=${inboxName}`)\n        return true\n      } else {\n        const errorText = await response.text()\n        console.error('❌ Failed to send to Mailinator public domain:', response.status, errorText)\n        return false\n      }\n    } catch (error) {\n      console.error('❌ Error sending to Mailinator public domain:', error)\n      return false\n    }\n  }\n\n  // Send to Mailinator private domain (requires API token)\n  private static async sendToPrivateDomain(\n    to: string,\n    subject: string,\n    html: string,\n    text?: string\n  ): Promise<boolean> {\n    try {\n      const inboxName = this.extractInboxName(to)\n      \n      const apiUrl = `https://api.mailinator.com/api/v2/domains/private/inboxes/${inboxName}/messages`\n      \n      const payload = {\n        from: '<EMAIL>',\n        subject: subject,\n        text: text || this.htmlToText(html),\n        html: html\n      }\n\n      const response = await fetch(apiUrl, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': this.config.apiToken!\n        },\n        body: JSON.stringify(payload)\n      })\n\n      if (response.ok) {\n        console.log(`✅ Email sent to Mailinator private domain: ${to}`)\n        return true\n      } else {\n        const errorText = await response.text()\n        console.error('❌ Failed to send to Mailinator private domain:', response.status, errorText)\n        return false\n      }\n    } catch (error) {\n      console.error('❌ Error sending to Mailinator private domain:', error)\n      return false\n    }\n  }\n\n  // Fetch messages from Mailinator inbox\n  static async getInboxMessages(inboxName: string): Promise<MailinatorMessage[]> {\n    try {\n      if (this.config.usePublicDomain) {\n        return await this.getPublicInboxMessages(inboxName)\n      } else if (this.config.privateDomain && this.config.apiToken) {\n        return await this.getPrivateInboxMessages(inboxName)\n      } else {\n        console.log('📧 Mailinator not configured for message retrieval')\n        return []\n      }\n    } catch (error) {\n      console.error('❌ Failed to fetch inbox messages:', error)\n      return []\n    }\n  }\n\n  // Get messages from public domain\n  private static async getPublicInboxMessages(inboxName: string): Promise<MailinatorMessage[]> {\n    try {\n      const response = await fetch(`https://www.mailinator.com/api/v2/domains/public/inboxes/${inboxName}`)\n      \n      if (response.ok) {\n        const data = await response.json()\n        return data.msgs || []\n      } else {\n        console.error('❌ Failed to fetch public inbox messages:', response.statusText)\n        return []\n      }\n    } catch (error) {\n      console.error('❌ Error fetching public inbox messages:', error)\n      return []\n    }\n  }\n\n  // Get messages from private domain\n  private static async getPrivateInboxMessages(inboxName: string): Promise<MailinatorMessage[]> {\n    try {\n      const response = await fetch(\n        `https://api.mailinator.com/api/v2/domains/private/inboxes/${inboxName}`,\n        {\n          headers: {\n            'Authorization': this.config.apiToken!\n          }\n        }\n      )\n      \n      if (response.ok) {\n        const data = await response.json()\n        return data.msgs || []\n      } else {\n        console.error('❌ Failed to fetch private inbox messages:', response.statusText)\n        return []\n      }\n    } catch (error) {\n      console.error('❌ Error fetching private inbox messages:', error)\n      return []\n    }\n  }\n\n  // Utility functions\n  private static extractInboxName(email: string): string {\n    const parts = email.split('@')\n    return parts[0] || 'demo'\n  }\n\n  private static htmlToText(html: string): string {\n    // Simple HTML to text conversion\n    return html\n      .replace(/<[^>]*>/g, '')\n      .replace(/&nbsp;/g, ' ')\n      .replace(/&amp;/g, '&')\n      .replace(/&lt;/g, '<')\n      .replace(/&gt;/g, '>')\n      .replace(/&quot;/g, '\"')\n      .trim()\n  }\n\n  // Generate Mailinator email for testing\n  static generateTestEmail(name: string): string {\n    const cleanName = name.toLowerCase().replace(/[^a-z0-9]/g, '')\n    return `${cleanName}@mailinator.com`\n  }\n\n  // Create demo Mailinator account setup\n  static setupDemoAccount(): {\n    testEmails: string[]\n    inboxUrls: string[]\n    instructions: string[]\n  } {\n    const testEmails = [\n      '<EMAIL>',\n      '<EMAIL>',\n      '<EMAIL>',\n      '<EMAIL>'\n    ]\n\n    const inboxUrls = testEmails.map(email => {\n      const inboxName = this.extractInboxName(email)\n      return `https://www.mailinator.com/v4/public/inboxes.jsp?to=${inboxName}`\n    })\n\n    const instructions = [\n      '1. 📧 Use any of the test emails above for demo purposes',\n      '2. 🌐 Visit the inbox URLs to check received emails',\n      '3. 🔄 Emails are automatically deleted after a few hours',\n      '4. 🆓 No signup required for public Mailinator inboxes',\n      '5. 🔗 Click email links to test the full interview flow'\n    ]\n\n    return {\n      testEmails,\n      inboxUrls,\n      instructions\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAwBsB;AAFf,MAAM;IACX,OAAe,SAA2B;QACxC,iBAAiB,CAAC,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,oBAAoB;QAClD,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,yBAAyB;QACpD,UAAU,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,oBAAoB;QAC1C,cAAc,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,wBAAwB;IACpD,EAAC;IAED,OAAO,UAAU,MAAiC,EAAE;QAClD,IAAI,CAAC,MAAM,GAAG;YAAE,GAAG,IAAI,CAAC,MAAM;YAAE,GAAG,MAAM;QAAC;IAC5C;IAEA,OAAO,YAA8B;QACnC,OAAO;YAAE,GAAG,IAAI,CAAC,MAAM;QAAC;IAC1B;IAEA,gCAAgC;IAChC,aAAa,UACX,EAAU,EACV,OAAe,EACf,IAAY,EACZ,IAAa,EACK;QAClB,IAAI;YACF,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;gBAC/B,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,SAAS,MAAM;YAC1D,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;gBAC5D,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,SAAS,MAAM;YAC3D,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI;gBACvB,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS;gBACjC,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC;gBAChD,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,OAAO;QACT;IACF;IAEA,+CAA+C;IAC/C,aAAqB,mBACnB,EAAU,EACV,OAAe,EACf,IAAY,EACZ,IAAa,EACK;QAClB,IAAI;YACF,gCAAgC;YAChC,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;YAExC,sDAAsD;YACtD,MAAM,aAAa,CAAC,yDAAyD,EAAE,UAAU,CAAC,CAAC;YAE3F,MAAM,UAAU;gBACd,MAAM;gBACN,SAAS;gBACT,MAAM,QAAQ,IAAI,CAAC,UAAU,CAAC;gBAC9B,MAAM;gBACN,IAAI;YACN;YAEA,MAAM,WAAW,MAAM,MAAM,YAAY;gBACvC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,UAAU,eAAe,CAAC;gBAClF,QAAQ,GAAG,CAAC,CAAC,uEAAuE,EAAE,WAAW;gBACjG,OAAO;YACT,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,iDAAiD,SAAS,MAAM,EAAE;gBAChF,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gDAAgD;YAC9D,OAAO;QACT;IACF;IAEA,yDAAyD;IACzD,aAAqB,oBACnB,EAAU,EACV,OAAe,EACf,IAAY,EACZ,IAAa,EACK;QAClB,IAAI;YACF,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;YAExC,MAAM,SAAS,CAAC,0DAA0D,EAAE,UAAU,SAAS,CAAC;YAEhG,MAAM,UAAU;gBACd,MAAM;gBACN,SAAS;gBACT,MAAM,QAAQ,IAAI,CAAC,UAAU,CAAC;gBAC9B,MAAM;YACR;YAEA,MAAM,WAAW,MAAM,MAAM,QAAQ;gBACnC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,IAAI,CAAC,MAAM,CAAC,QAAQ;gBACvC;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,IAAI;gBAC9D,OAAO;YACT,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,kDAAkD,SAAS,MAAM,EAAE;gBACjF,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iDAAiD;YAC/D,OAAO;QACT;IACF;IAEA,uCAAuC;IACvC,aAAa,iBAAiB,SAAiB,EAAgC;QAC7E,IAAI;YACF,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;gBAC/B,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC;YAC3C,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;gBAC5D,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC;YAC5C,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,OAAO,EAAE;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO,EAAE;QACX;IACF;IAEA,kCAAkC;IAClC,aAAqB,uBAAuB,SAAiB,EAAgC;QAC3F,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,yDAAyD,EAAE,WAAW;YAEpG,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,OAAO,KAAK,IAAI,IAAI,EAAE;YACxB,OAAO;gBACL,QAAQ,KAAK,CAAC,4CAA4C,SAAS,UAAU;gBAC7E,OAAO,EAAE;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,OAAO,EAAE;QACX;IACF;IAEA,mCAAmC;IACnC,aAAqB,wBAAwB,SAAiB,EAAgC;QAC5F,IAAI;YACF,MAAM,WAAW,MAAM,MACrB,CAAC,0DAA0D,EAAE,WAAW,EACxE;gBACE,SAAS;oBACP,iBAAiB,IAAI,CAAC,MAAM,CAAC,QAAQ;gBACvC;YACF;YAGF,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,OAAO,KAAK,IAAI,IAAI,EAAE;YACxB,OAAO;gBACL,QAAQ,KAAK,CAAC,6CAA6C,SAAS,UAAU;gBAC9E,OAAO,EAAE;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,OAAO,EAAE;QACX;IACF;IAEA,oBAAoB;IACpB,OAAe,iBAAiB,KAAa,EAAU;QACrD,MAAM,QAAQ,MAAM,KAAK,CAAC;QAC1B,OAAO,KAAK,CAAC,EAAE,IAAI;IACrB;IAEA,OAAe,WAAW,IAAY,EAAU;QAC9C,iCAAiC;QACjC,OAAO,KACJ,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,WAAW,KACnB,OAAO,CAAC,UAAU,KAClB,OAAO,CAAC,SAAS,KACjB,OAAO,CAAC,SAAS,KACjB,OAAO,CAAC,WAAW,KACnB,IAAI;IACT;IAEA,wCAAwC;IACxC,OAAO,kBAAkB,IAAY,EAAU;QAC7C,MAAM,YAAY,KAAK,WAAW,GAAG,OAAO,CAAC,cAAc;QAC3D,OAAO,GAAG,UAAU,eAAe,CAAC;IACtC;IAEA,uCAAuC;IACvC,OAAO,mBAIL;QACA,MAAM,aAAa;YACjB;YACA;YACA;YACA;SACD;QAED,MAAM,YAAY,WAAW,GAAG,CAAC,CAAA;YAC/B,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;YACxC,OAAO,CAAC,oDAAoD,EAAE,WAAW;QAC3E;QAEA,MAAM,eAAe;YACnB;YACA;YACA;YACA;YACA;SACD;QAED,OAAO;YACL;YACA;YACA;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 413, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/lib/email-service.ts"], "sourcesContent": ["import crypto from 'crypto'\nimport { MailinatorService } from './mailinator-service'\n\n// Only import nodemailer on server side\nlet nodemailer: any = null\nlet prisma: any = null\n\nif (typeof window === 'undefined') {\n  // Server-side imports\n  try {\n    nodemailer = require('nodemailer')\n    prisma = require('./prisma').prisma\n  } catch (error) {\n    console.log('Server-side dependencies not available')\n  }\n}\n\nexport interface InterviewInvitation {\n  id: string\n  email: string\n  studentName: string\n  interviewTitle: string\n  scheduledAt: Date\n  token: string\n  status: 'PENDING' | 'ACCEPTED' | 'COMPLETED' | 'EXPIRED'\n  createdAt: Date\n  expiresAt: Date\n}\n\n// In-memory storage for demo (replace with database in production)\nconst invitations: Map<string, InterviewInvitation> = new Map()\n\nexport interface EmailOptions {\n  to: string\n  subject: string\n  html: string\n  attachments?: Array<{\n    filename: string\n    path: string\n  }>\n}\n\nexport class EmailService {\n  private static transporter: any = null\n\n  // Initialize transporter only on server side\n  private static getTransporter() {\n    if (!this.transporter && nodemailer && typeof window === 'undefined') {\n      this.transporter = nodemailer.createTransport({\n        host: process.env.EMAIL_HOST,\n        port: parseInt(process.env.EMAIL_PORT || '587'),\n        secure: false,\n        auth: {\n          user: process.env.EMAIL_USER,\n          pass: process.env.EMAIL_PASS,\n        },\n      })\n    }\n    return this.transporter\n  }\n\n  // Email service mode configuration\n  private static emailMode: 'NODEMAILER' | 'MAILINATOR' | 'DEMO' = 'MAILINATOR'\n\n  static setEmailMode(mode: 'NODEMAILER' | 'MAILINATOR' | 'DEMO') {\n    this.emailMode = mode\n  }\n\n  static getEmailMode(): string {\n    return this.emailMode\n  }\n\n  // Create invitation method\n  static createInvitation(\n    email: string,\n    studentName: string,\n    interviewTitle: string,\n    scheduledAt: Date\n  ) {\n    const token = this.generateInvitationToken()\n    const id = crypto.randomUUID()\n\n    return {\n      id,\n      token,\n      email,\n      studentName,\n      interviewTitle,\n      scheduledAt,\n      status: 'pending' as const,\n      createdAt: new Date(),\n      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days\n    }\n  }\n\n  // Invitation Token Management\n  static generateInvitationToken(): string {\n    return crypto.randomBytes(32).toString('hex')\n  }\n\n  static createInvitation(\n    email: string,\n    studentName: string,\n    interviewTitle: string,\n    scheduledAt: Date\n  ): InterviewInvitation {\n    const token = this.generateInvitationToken()\n    const id = crypto.randomUUID()\n    const expiresAt = new Date(scheduledAt.getTime() + 24 * 60 * 60 * 1000) // 24 hours after scheduled time\n\n    const invitation: InterviewInvitation = {\n      id,\n      email,\n      studentName,\n      interviewTitle,\n      scheduledAt,\n      token,\n      status: 'PENDING',\n      createdAt: new Date(),\n      expiresAt\n    }\n\n    invitations.set(token, invitation)\n    return invitation\n  }\n\n  static getInvitationByToken(token: string): InterviewInvitation | null {\n    const invitation = invitations.get(token)\n    if (!invitation) return null\n\n    // Check if expired\n    if (new Date() > invitation.expiresAt) {\n      invitation.status = 'EXPIRED'\n    }\n\n    return invitation\n  }\n\n  static updateInvitationStatus(token: string, status: InterviewInvitation['status']): boolean {\n    const invitation = invitations.get(token)\n    if (!invitation) return false\n\n    invitation.status = status\n    return true\n  }\n\n  static generateInvitationLink(token: string, baseUrl: string = 'http://localhost:3000'): string {\n    return `${baseUrl}/interview/invite/${token}`\n  }\n\n  static async sendEmail(options: EmailOptions): Promise<boolean> {\n    try {\n      let success = false\n\n      // Choose email service based on mode\n      switch (this.emailMode) {\n        case 'MAILINATOR':\n          success = await MailinatorService.sendEmail(\n            options.to,\n            options.subject,\n            options.html\n          )\n          break\n\n        case 'NODEMAILER':\n          // Skip email sending if no email configuration or not on server\n          if (typeof window !== 'undefined') {\n            console.log('NodeMailer only works on server side')\n            return false\n          }\n\n          if (!process.env.EMAIL_USER || !process.env.EMAIL_PASS) {\n            console.log('Email service not configured, skipping email send')\n            return true\n          }\n\n          const transporter = this.getTransporter()\n          if (!transporter) {\n            console.log('Failed to initialize email transporter')\n            return false\n          }\n\n          const info = await transporter.sendMail({\n            from: process.env.EMAIL_USER,\n            to: options.to,\n            subject: options.subject,\n            html: options.html,\n            attachments: options.attachments,\n          })\n          success = true\n          break\n\n        case 'DEMO':\n        default:\n          console.log('📧 Demo Mode - Email would be sent:')\n          console.log(`To: ${options.to}`)\n          console.log(`Subject: ${options.subject}`)\n          success = true\n          break\n      }\n\n      // Log email (skip if database not available)\n      try {\n        await prisma.emailLog.create({\n          data: {\n            to: options.to,\n            subject: options.subject,\n            body: options.html,\n            status: success ? 'SENT' : 'FAILED',\n            sentAt: new Date(),\n          },\n        })\n      } catch (dbError) {\n        console.log('Database not available for email logging')\n      }\n\n      return success\n    } catch (error) {\n      console.error('Error sending email:', error)\n\n      // Log failed email (skip if database not available)\n      try {\n        await prisma.emailLog.create({\n          data: {\n            to: options.to,\n            subject: options.subject,\n            body: options.html,\n            status: 'FAILED',\n          },\n        })\n      } catch (dbError) {\n        console.log('Database not available for email logging')\n      }\n\n      return false\n    }\n  }\n\n  static async sendInterviewInvitationWithToken(invitation: InterviewInvitation): Promise<boolean> {\n    const invitationLink = this.generateInvitationLink(invitation.token)\n\n    const html = `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <div style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;\">\n          <h1 style=\"color: white; margin: 0;\">🎯 Video Interview Invitation</h1>\n        </div>\n\n        <div style=\"padding: 30px; background: #f8f9fa;\">\n          <h2 style=\"color: #333;\">Hello ${invitation.studentName}!</h2>\n\n          <p style=\"font-size: 16px; line-height: 1.6; color: #555;\">\n            You've been invited to participate in an AI-powered video interview for:\n          </p>\n\n          <div style=\"background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea;\">\n            <h3 style=\"margin: 0 0 10px 0; color: #333;\">${invitation.interviewTitle}</h3>\n            <p style=\"margin: 0; color: #666;\">\n              📅 Scheduled: ${invitation.scheduledAt.toLocaleDateString()} at ${invitation.scheduledAt.toLocaleTimeString()}\n            </p>\n          </div>\n\n          <div style=\"text-align: center; margin: 30px 0;\">\n            <a href=\"${invitationLink}\"\n               style=\"background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;\">\n              🚀 Join Video Interview\n            </a>\n          </div>\n\n          <div style=\"background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0;\">\n            <h4 style=\"margin: 0 0 10px 0; color: #1976d2;\">📋 What to Expect:</h4>\n            <ul style=\"margin: 0; padding-left: 20px; color: #555;\">\n              <li>🤖 AI-powered interviewer with voice interaction</li>\n              <li>📹 Video recording for review and feedback</li>\n              <li>💻 Interactive coding and behavioral questions</li>\n              <li>📊 Real-time evaluation and scoring</li>\n              <li>🎉 Instant results and congratulations</li>\n            </ul>\n          </div>\n\n          <div style=\"background: #fff3e0; padding: 15px; border-radius: 5px; margin: 20px 0;\">\n            <h4 style=\"margin: 0 0 10px 0; color: #f57c00;\">⚠️ Technical Requirements:</h4>\n            <ul style=\"margin: 0; padding-left: 20px; color: #555;\">\n              <li>🌐 Stable internet connection</li>\n              <li>📷 Working camera and microphone</li>\n              <li>🌍 Modern web browser (Chrome recommended)</li>\n              <li>🔇 Quiet environment for recording</li>\n              <li>💾 Allow browser permissions for camera/mic</li>\n            </ul>\n          </div>\n\n          <p style=\"font-size: 14px; color: #777; margin-top: 30px;\">\n            This invitation expires on ${invitation.expiresAt.toLocaleDateString()}.\n            If you have any questions, please contact our support team.\n          </p>\n        </div>\n\n        <div style=\"background: #333; padding: 20px; text-align: center;\">\n          <p style=\"color: #ccc; margin: 0; font-size: 14px;\">\n            © 2024 AI Interview Platform. All rights reserved.\n          </p>\n        </div>\n      </div>\n    `\n\n    return this.sendEmail({\n      to: invitation.email,\n      subject: `🎯 Video Interview Invitation - ${invitation.interviewTitle}`,\n      html,\n    })\n  }\n\n  static async sendInterviewInvitation(\n    studentEmail: string,\n    studentName: string,\n    interviewDetails: {\n      title: string\n      scheduledAt: Date\n      duration: number\n      meetingLink: string\n    }\n  ): Promise<boolean> {\n    const html = `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <h2 style=\"color: #2563eb;\">Interview Invitation</h2>\n        <p>Dear ${studentName},</p>\n        <p>You have been invited to participate in an interview:</p>\n        \n        <div style=\"background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n          <h3 style=\"margin-top: 0;\">${interviewDetails.title}</h3>\n          <p><strong>Date & Time:</strong> ${interviewDetails.scheduledAt.toLocaleString()}</p>\n          <p><strong>Duration:</strong> ${interviewDetails.duration} minutes</p>\n          <p><strong>Meeting Link:</strong> <a href=\"${interviewDetails.meetingLink}\">${interviewDetails.meetingLink}</a></p>\n        </div>\n        \n        <p>Please make sure to:</p>\n        <ul>\n          <li>Test your camera and microphone before the interview</li>\n          <li>Ensure you have a stable internet connection</li>\n          <li>Prepare for coding and theory questions</li>\n          <li>Join the meeting 5 minutes early</li>\n        </ul>\n        \n        <p>Good luck with your interview!</p>\n        <p>Best regards,<br>Mock Interview Team</p>\n      </div>\n    `\n\n    return this.sendEmail({\n      to: studentEmail,\n      subject: `Interview Invitation - ${interviewDetails.title}`,\n      html,\n    })\n  }\n\n  static async sendInterviewResults(\n    studentEmail: string,\n    studentName: string,\n    results: {\n      score: number\n      feedback: string\n      recordingUrl?: string\n    }\n  ): Promise<boolean> {\n    const html = `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <h2 style=\"color: #2563eb;\">Interview Results</h2>\n        <p>Dear ${studentName},</p>\n        <p>Your interview has been completed. Here are your results:</p>\n        \n        <div style=\"background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n          <h3 style=\"margin-top: 0;\">Overall Score: ${results.score}/100</h3>\n          <h4>Feedback:</h4>\n          <p>${results.feedback}</p>\n          ${results.recordingUrl ? `<p><strong>Recording:</strong> <a href=\"${results.recordingUrl}\">View Recording</a></p>` : ''}\n        </div>\n        \n        <p>Keep practicing and improving your skills!</p>\n        <p>Best regards,<br>Mock Interview Team</p>\n      </div>\n    `\n\n    return this.sendEmail({\n      to: studentEmail,\n      subject: 'Your Interview Results',\n      html,\n    })\n  }\n\n  static async sendWelcomeEmail(\n    userEmail: string,\n    userName: string,\n    role: string\n  ): Promise<boolean> {\n    const html = `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <h2 style=\"color: #2563eb;\">Welcome to Mock Interview Platform!</h2>\n        <p>Dear ${userName},</p>\n        <p>Welcome to our Mock Interview Platform! Your account has been created successfully.</p>\n        \n        <div style=\"background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n          <h3 style=\"margin-top: 0;\">Account Details</h3>\n          <p><strong>Email:</strong> ${userEmail}</p>\n          <p><strong>Role:</strong> ${role}</p>\n        </div>\n        \n        <p>You can now log in and start using the platform.</p>\n        <p>Best regards,<br>Mock Interview Team</p>\n      </div>\n    `\n\n    return this.sendEmail({\n      to: userEmail,\n      subject: 'Welcome to Mock Interview Platform',\n      html,\n    })\n  }\n\n  // Demo function to create sample invitations\n  static createDemoInvitations(): InterviewInvitation[] {\n    const demoInvitations = [\n      {\n        email: '<EMAIL>',\n        studentName: 'John Doe',\n        interviewTitle: 'Frontend Developer Position',\n        scheduledAt: new Date(Date.now() + 2 * 60 * 60 * 1000) // 2 hours from now\n      },\n      {\n        email: '<EMAIL>',\n        studentName: 'Jane Smith',\n        interviewTitle: 'Full Stack Developer Role',\n        scheduledAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 1 day from now\n      },\n      {\n        email: '<EMAIL>',\n        studentName: 'Alex Johnson',\n        interviewTitle: 'React Developer Interview',\n        scheduledAt: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3 days from now\n      }\n    ]\n\n    return demoInvitations.map(demo =>\n      this.createInvitation(demo.email, demo.studentName, demo.interviewTitle, demo.scheduledAt)\n    )\n  }\n}\n\n// Initialize demo invitations\nif (typeof window === 'undefined') {\n  EmailService.createDemoInvitations()\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,wCAAwC;AACxC,IAAI,aAAkB;AACtB,IAAI,SAAc;AAElB,uCAAmC;;AAQnC;AAcA,mEAAmE;AACnE,MAAM,cAAgD,IAAI;AAYnD,MAAM;IACX,OAAe,cAAmB,KAAI;IAEtC,6CAA6C;IAC7C,OAAe,iBAAiB;QAC9B,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,cAAc,aAAkB,aAAa;;QAUtE;QACA,OAAO,IAAI,CAAC,WAAW;IACzB;IAEA,mCAAmC;IACnC,OAAe,YAAkD,aAAY;IAE7E,OAAO,aAAa,IAA0C,EAAE;QAC9D,IAAI,CAAC,SAAS,GAAG;IACnB;IAEA,OAAO,eAAuB;QAC5B,OAAO,IAAI,CAAC,SAAS;IACvB;IAEA,2BAA2B;IAC3B,OAAO,iBACL,KAAa,EACb,WAAmB,EACnB,cAAsB,EACtB,WAAiB,EACjB;QACA,MAAM,QAAQ,IAAI,CAAC,uBAAuB;QAC1C,MAAM,KAAK,4KAAA,CAAA,UAAM,CAAC,UAAU;QAE5B,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA,QAAQ;YACR,WAAW,IAAI;YACf,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,SAAS;QACrE;IACF;IAEA,8BAA8B;IAC9B,OAAO,0BAAkC;QACvC,OAAO,4KAAA,CAAA,UAAM,CAAC,WAAW,CAAC,IAAI,QAAQ,CAAC;IACzC;IAEA,OAAO,iBACL,KAAa,EACb,WAAmB,EACnB,cAAsB,EACtB,WAAiB,EACI;QACrB,MAAM,QAAQ,IAAI,CAAC,uBAAuB;QAC1C,MAAM,KAAK,4KAAA,CAAA,UAAM,CAAC,UAAU;QAC5B,MAAM,YAAY,IAAI,KAAK,YAAY,OAAO,KAAK,KAAK,KAAK,KAAK,MAAM,gCAAgC;;QAExG,MAAM,aAAkC;YACtC;YACA;YACA;YACA;YACA;YACA;YACA,QAAQ;YACR,WAAW,IAAI;YACf;QACF;QAEA,YAAY,GAAG,CAAC,OAAO;QACvB,OAAO;IACT;IAEA,OAAO,qBAAqB,KAAa,EAA8B;QACrE,MAAM,aAAa,YAAY,GAAG,CAAC;QACnC,IAAI,CAAC,YAAY,OAAO;QAExB,mBAAmB;QACnB,IAAI,IAAI,SAAS,WAAW,SAAS,EAAE;YACrC,WAAW,MAAM,GAAG;QACtB;QAEA,OAAO;IACT;IAEA,OAAO,uBAAuB,KAAa,EAAE,MAAqC,EAAW;QAC3F,MAAM,aAAa,YAAY,GAAG,CAAC;QACnC,IAAI,CAAC,YAAY,OAAO;QAExB,WAAW,MAAM,GAAG;QACpB,OAAO;IACT;IAEA,OAAO,uBAAuB,KAAa,EAAE,UAAkB,uBAAuB,EAAU;QAC9F,OAAO,GAAG,QAAQ,kBAAkB,EAAE,OAAO;IAC/C;IAEA,aAAa,UAAU,OAAqB,EAAoB;QAC9D,IAAI;YACF,IAAI,UAAU;YAEd,qCAAqC;YACrC,OAAQ,IAAI,CAAC,SAAS;gBACpB,KAAK;oBACH,UAAU,MAAM,sIAAA,CAAA,oBAAiB,CAAC,SAAS,CACzC,QAAQ,EAAE,EACV,QAAQ,OAAO,EACf,QAAQ,IAAI;oBAEd;gBAEF,KAAK;oBACH,gEAAgE;oBAChE,wCAAmC;wBACjC,QAAQ,GAAG,CAAC;wBACZ,OAAO;oBACT;;oBAOA,MAAM;oBAMN,MAAM;gBAUR,KAAK;gBACL;oBACE,QAAQ,GAAG,CAAC;oBACZ,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE;oBAC/B,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,QAAQ,OAAO,EAAE;oBACzC,UAAU;oBACV;YACJ;YAEA,6CAA6C;YAC7C,IAAI;gBACF,MAAM,OAAO,QAAQ,CAAC,MAAM,CAAC;oBAC3B,MAAM;wBACJ,IAAI,QAAQ,EAAE;wBACd,SAAS,QAAQ,OAAO;wBACxB,MAAM,QAAQ,IAAI;wBAClB,QAAQ,UAAU,SAAS;wBAC3B,QAAQ,IAAI;oBACd;gBACF;YACF,EAAE,OAAO,SAAS;gBAChB,QAAQ,GAAG,CAAC;YACd;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YAEtC,oDAAoD;YACpD,IAAI;gBACF,MAAM,OAAO,QAAQ,CAAC,MAAM,CAAC;oBAC3B,MAAM;wBACJ,IAAI,QAAQ,EAAE;wBACd,SAAS,QAAQ,OAAO;wBACxB,MAAM,QAAQ,IAAI;wBAClB,QAAQ;oBACV;gBACF;YACF,EAAE,OAAO,SAAS;gBAChB,QAAQ,GAAG,CAAC;YACd;YAEA,OAAO;QACT;IACF;IAEA,aAAa,iCAAiC,UAA+B,EAAoB;QAC/F,MAAM,iBAAiB,IAAI,CAAC,sBAAsB,CAAC,WAAW,KAAK;QAEnE,MAAM,OAAO,CAAC;;;;;;;yCAOuB,EAAE,WAAW,WAAW,CAAC;;;;;;;yDAOT,EAAE,WAAW,cAAc,CAAC;;4BAEzD,EAAE,WAAW,WAAW,CAAC,kBAAkB,GAAG,IAAI,EAAE,WAAW,WAAW,CAAC,kBAAkB,GAAG;;;;;qBAKvG,EAAE,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCA6BC,EAAE,WAAW,SAAS,CAAC,kBAAkB,GAAG;;;;;;;;;;;IAW/E,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,IAAI,WAAW,KAAK;YACpB,SAAS,CAAC,gCAAgC,EAAE,WAAW,cAAc,EAAE;YACvE;QACF;IACF;IAEA,aAAa,wBACX,YAAoB,EACpB,WAAmB,EACnB,gBAKC,EACiB;QAClB,MAAM,OAAO,CAAC;;;gBAGF,EAAE,YAAY;;;;qCAIO,EAAE,iBAAiB,KAAK,CAAC;2CACnB,EAAE,iBAAiB,WAAW,CAAC,cAAc,GAAG;wCACnD,EAAE,iBAAiB,QAAQ,CAAC;qDACf,EAAE,iBAAiB,WAAW,CAAC,EAAE,EAAE,iBAAiB,WAAW,CAAC;;;;;;;;;;;;;;IAcjH,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,IAAI;YACJ,SAAS,CAAC,uBAAuB,EAAE,iBAAiB,KAAK,EAAE;YAC3D;QACF;IACF;IAEA,aAAa,qBACX,YAAoB,EACpB,WAAmB,EACnB,OAIC,EACiB;QAClB,MAAM,OAAO,CAAC;;;gBAGF,EAAE,YAAY;;;;oDAIsB,EAAE,QAAQ,KAAK,CAAC;;aAEvD,EAAE,QAAQ,QAAQ,CAAC;UACtB,EAAE,QAAQ,YAAY,GAAG,CAAC,wCAAwC,EAAE,QAAQ,YAAY,CAAC,wBAAwB,CAAC,GAAG,GAAG;;;;;;IAM9H,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,IAAI;YACJ,SAAS;YACT;QACF;IACF;IAEA,aAAa,iBACX,SAAiB,EACjB,QAAgB,EAChB,IAAY,EACM;QAClB,MAAM,OAAO,CAAC;;;gBAGF,EAAE,SAAS;;;;;qCAKU,EAAE,UAAU;oCACb,EAAE,KAAK;;;;;;IAMvC,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,IAAI;YACJ,SAAS;YACT;QACF;IACF;IAEA,6CAA6C;IAC7C,OAAO,wBAA+C;QACpD,MAAM,kBAAkB;YACtB;gBACE,OAAO;gBACP,aAAa;gBACb,gBAAgB;gBAChB,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,mBAAmB;YAC5E;YACA;gBACE,OAAO;gBACP,aAAa;gBACb,gBAAgB;gBAChB,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,MAAM,iBAAiB;YAC3E;YACA;gBACE,OAAO;gBACP,aAAa;gBACb,gBAAgB;gBAChB,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,kBAAkB;YAChF;SACD;QAED,OAAO,gBAAgB,GAAG,CAAC,CAAA,OACzB,IAAI,CAAC,gBAAgB,CAAC,KAAK,KAAK,EAAE,KAAK,WAAW,EAAE,KAAK,cAAc,EAAE,KAAK,WAAW;IAE7F;AACF;AAEA,8BAA8B;AAC9B,uCAAmC;;AAEnC", "debugId": null}}, {"offset": {"line": 747, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/lib/demo-auth.ts"], "sourcesContent": ["// Demo authentication system for testing without database\nexport interface DemoUser {\n  id: string\n  email: string\n  name: string\n  role: 'ADMIN' | 'TENANT' | 'STUDENT'\n  password: string\n}\n\n// Demo users for testing\nexport const DEMO_USERS: DemoUser[] = [\n  {\n    id: 'admin-1',\n    email: '<EMAIL>',\n    name: 'System Administrator',\n    role: 'ADMIN',\n    password: 'admin123'\n  },\n  {\n    id: 'tenant-1',\n    email: '<EMAIL>',\n    name: 'TechCorp Manager',\n    role: 'TENANT',\n    password: 'tenant123'\n  },\n  {\n    id: 'student-1',\n    email: '<EMAIL>',\n    name: '<PERSON>',\n    role: 'STUDENT',\n    password: 'student123'\n  },\n  {\n    id: 'student-2',\n    email: '<EMAIL>',\n    name: '<PERSON>',\n    role: 'STUDENT',\n    password: 'student123'\n  },\n  {\n    id: 'tenant-2',\n    email: '<EMAIL>',\n    name: 'StartupXYZ HR',\n    role: 'TENANT',\n    password: 'startup123'\n  }\n]\n\nexport class DemoAuthService {\n  // Validate demo user credentials\n  static validateUser(email: string, password: string): DemoUser | null {\n    const user = DEMO_USERS.find(u => u.email === email && u.password === password)\n    return user || null\n  }\n\n  // Check if email exists\n  static emailExists(email: string): boolean {\n    return DEMO_USERS.some(u => u.email === email)\n  }\n\n  // Add new demo user (for signup)\n  static addUser(userData: Omit<DemoUser, 'id'>): DemoUser {\n    const newUser: DemoUser = {\n      id: `demo-${Date.now()}`,\n      ...userData\n    }\n    DEMO_USERS.push(newUser)\n    return newUser\n  }\n\n  // Get user by ID\n  static getUserById(id: string): DemoUser | null {\n    return DEMO_USERS.find(u => u.id === id) || null\n  }\n\n  // Get all users (for admin)\n  static getAllUsers(): DemoUser[] {\n    return DEMO_USERS\n  }\n}\n"], "names": [], "mappings": "AAAA,0DAA0D;;;;;AAUnD,MAAM,aAAyB;IACpC;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;IACZ;CACD;AAEM,MAAM;IACX,iCAAiC;IACjC,OAAO,aAAa,KAAa,EAAE,QAAgB,EAAmB;QACpE,MAAM,OAAO,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,SAAS,EAAE,QAAQ,KAAK;QACtE,OAAO,QAAQ;IACjB;IAEA,wBAAwB;IACxB,OAAO,YAAY,KAAa,EAAW;QACzC,OAAO,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;IAC1C;IAEA,iCAAiC;IACjC,OAAO,QAAQ,QAA8B,EAAY;QACvD,MAAM,UAAoB;YACxB,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;YACxB,GAAG,QAAQ;QACb;QACA,WAAW,IAAI,CAAC;QAChB,OAAO;IACT;IAEA,iBAAiB;IACjB,OAAO,YAAY,EAAU,EAAmB;QAC9C,OAAO,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO;IAC9C;IAEA,4BAA4B;IAC5B,OAAO,cAA0B;QAC/B,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 826, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/app/interview/video/%5Btoken%5D/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState, useRef } from 'react'\nimport { useP<PERSON><PERSON>, useRouter } from 'next/navigation'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { EmailService, InterviewInvitation } from '@/lib/email-service'\nimport { DemoAuth } from '@/lib/demo-auth'\nimport { \n  Video, \n  VideoOff, \n  Mic, \n  MicOff, \n  Square,\n  Play,\n  Pause,\n  Clock,\n  User,\n  RotateCcw,\n  ArrowRight,\n  Circle\n} from 'lucide-react'\n\n// Mock interview questions\nconst interviewQuestions = [\n  {\n    id: 1,\n    type: 'introduction',\n    question: \"Hello! Let's start with a brief introduction. Please tell me about yourself, your background, and what interests you about this position.\",\n    timeLimit: 120\n  },\n  {\n    id: 2,\n    type: 'technical',\n    question: \"Can you explain the difference between let, const, and var in JavaScript? When would you use each one?\",\n    timeLimit: 180\n  },\n  {\n    id: 3,\n    type: 'problem-solving',\n    question: \"Describe how you would approach debugging a web application that's running slowly. Walk me through your process step by step.\",\n    timeLimit: 240\n  },\n  {\n    id: 4,\n    type: 'behavioral',\n    question: \"Tell me about a challenging project you worked on. What obstacles did you face and how did you overcome them?\",\n    timeLimit: 180\n  },\n  {\n    id: 5,\n    type: 'closing',\n    question: \"Do you have any questions about the role or our company? Is there anything else you'd like me to know about you?\",\n    timeLimit: 120\n  }\n]\n\nexport default function VideoInterviewPage() {\n  const params = useParams()\n  const router = useRouter()\n  const token = params.token as string\n\n  const videoRef = useRef<HTMLVideoElement>(null)\n  const mediaRecorderRef = useRef<MediaRecorder | null>(null)\n  const recordedChunksRef = useRef<Blob[]>([])\n\n  const [invitation, setInvitation] = useState<InterviewInvitation | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [stream, setStream] = useState<MediaStream | null>(null)\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)\n  const [isRecording, setIsRecording] = useState(false)\n  const [isPaused, setIsPaused] = useState(false)\n  const [timeElapsed, setTimeElapsed] = useState(0)\n  const [questionStartTime, setQuestionStartTime] = useState(0)\n  const [isInterviewerSpeaking, setIsInterviewerSpeaking] = useState(false)\n  const [isListening, setIsListening] = useState(false)\n  const [interviewerMessage, setInterviewerMessage] = useState('')\n  const [hasStarted, setHasStarted] = useState(false)\n  const [recordedVideos, setRecordedVideos] = useState<Blob[]>([])\n\n  const currentQuestion = interviewQuestions[currentQuestionIndex]\n\n  useEffect(() => {\n    // Check authentication\n    const currentUser = DemoAuth.getCurrentUser()\n    if (!currentUser) {\n      router.push(`/interview/invite/${token}`)\n      return\n    }\n\n    // Load invitation\n    if (token) {\n      const invitationData = EmailService.getInvitationByToken(token)\n      if (invitationData) {\n        setInvitation(invitationData)\n      } else {\n        router.push('/')\n        return\n      }\n      setLoading(false)\n    }\n  }, [token, router])\n\n  useEffect(() => {\n    // Set up camera stream\n    const setupCamera = async () => {\n      try {\n        const mediaStream = await navigator.mediaDevices.getUserMedia({\n          video: true,\n          audio: true\n        })\n        setStream(mediaStream)\n        if (videoRef.current) {\n          videoRef.current.srcObject = mediaStream\n        }\n      } catch (error) {\n        console.error('Error accessing camera:', error)\n      }\n    }\n\n    setupCamera()\n\n    return () => {\n      if (stream) {\n        stream.getTracks().forEach(track => track.stop())\n      }\n    }\n  }, [])\n\n  // Timer effect\n  useEffect(() => {\n    let interval: NodeJS.Timeout\n    if (hasStarted && !isPaused) {\n      interval = setInterval(() => {\n        setTimeElapsed(prev => prev + 1)\n      }, 1000)\n    }\n    return () => clearInterval(interval)\n  }, [hasStarted, isPaused])\n\n  const speakQuestion = (question: string) => {\n    setIsInterviewerSpeaking(true)\n    setInterviewerMessage(question)\n\n    if ('speechSynthesis' in window) {\n      window.speechSynthesis.cancel()\n      \n      const utterance = new SpeechSynthesisUtterance(question)\n      utterance.rate = 0.9\n      utterance.pitch = 1\n      utterance.volume = 0.8\n\n      const voices = window.speechSynthesis.getVoices()\n      const preferredVoice = voices.find(voice => \n        voice.name.includes('Google') || \n        voice.name.includes('Microsoft') ||\n        voice.lang.startsWith('en')\n      )\n      if (preferredVoice) {\n        utterance.voice = preferredVoice\n      }\n\n      utterance.onend = () => {\n        setIsInterviewerSpeaking(false)\n        setIsListening(true)\n      }\n\n      utterance.onerror = () => {\n        setIsInterviewerSpeaking(false)\n        setIsListening(true)\n      }\n\n      window.speechSynthesis.speak(utterance)\n    } else {\n      setTimeout(() => {\n        setIsInterviewerSpeaking(false)\n        setIsListening(true)\n      }, 3000)\n    }\n  }\n\n  const startRecording = () => {\n    if (!stream) return\n\n    try {\n      const mediaRecorder = new MediaRecorder(stream, {\n        mimeType: 'video/webm;codecs=vp9'\n      })\n\n      recordedChunksRef.current = []\n\n      mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          recordedChunksRef.current.push(event.data)\n        }\n      }\n\n      mediaRecorder.onstop = () => {\n        const blob = new Blob(recordedChunksRef.current, { type: 'video/webm' })\n        setRecordedVideos(prev => [...prev, blob])\n      }\n\n      mediaRecorder.start(1000) // Record in 1-second chunks\n      mediaRecorderRef.current = mediaRecorder\n      setIsRecording(true)\n    } catch (error) {\n      console.error('Error starting recording:', error)\n    }\n  }\n\n  const stopRecording = () => {\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop()\n      setIsRecording(false)\n    }\n  }\n\n  const startInterview = () => {\n    setHasStarted(true)\n    setQuestionStartTime(Date.now())\n    startRecording()\n    \n    // Welcome message\n    const welcomeMessage = `Welcome to your video interview! I'll be asking you ${interviewQuestions.length} questions. Let's begin with the first question.`\n    speakQuestion(welcomeMessage)\n    \n    setTimeout(() => {\n      speakQuestion(currentQuestion.question)\n    }, 4000)\n  }\n\n  const nextQuestion = () => {\n    stopRecording()\n    \n    if (currentQuestionIndex < interviewQuestions.length - 1) {\n      setCurrentQuestionIndex(prev => prev + 1)\n      setQuestionStartTime(Date.now())\n      setIsListening(false)\n      \n      setTimeout(() => {\n        startRecording()\n        speakQuestion(interviewQuestions[currentQuestionIndex + 1].question)\n      }, 2000)\n    } else {\n      // Interview complete\n      completeInterview()\n    }\n  }\n\n  const repeatQuestion = () => {\n    if (currentQuestion) {\n      speakQuestion(currentQuestion.question)\n    }\n  }\n\n  const completeInterview = () => {\n    setIsListening(false)\n    setIsInterviewerSpeaking(true)\n    setInterviewerMessage(\"Congratulations! You've completed the interview. Thank you for your time and thoughtful responses.\")\n    \n    const congratsMessage = \"Congratulations! You've completed the interview. Thank you for your time and thoughtful responses. Your recording will be reviewed and you'll receive feedback soon.\"\n    \n    if ('speechSynthesis' in window) {\n      const utterance = new SpeechSynthesisUtterance(congratsMessage)\n      utterance.onend = () => {\n        setIsInterviewerSpeaking(false)\n        // Navigate to completion page\n        setTimeout(() => {\n          router.push(`/interview/complete/${token}`)\n        }, 2000)\n      }\n      window.speechSynthesis.speak(utterance)\n    } else {\n      setTimeout(() => {\n        setIsInterviewerSpeaking(false)\n        router.push(`/interview/complete/${token}`)\n      }, 3000)\n    }\n  }\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60)\n    const secs = seconds % 60\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-900 flex items-center justify-center\">\n        <div className=\"text-white text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"></div>\n          <p>Loading interview...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-900\">\n      {/* Header */}\n      <div className=\"bg-gray-800 p-4 flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-xl font-bold text-white\">{invitation?.interviewTitle}</h1>\n          <p className=\"text-gray-400\">\n            Question {currentQuestionIndex + 1} of {interviewQuestions.length}\n            {hasStarted && ` • ${formatTime(timeElapsed)}`}\n          </p>\n        </div>\n        <div className=\"flex items-center space-x-4\">\n          {isRecording && (\n            <div className=\"flex items-center space-x-2 text-red-400\">\n              <Circle className=\"h-3 w-3 fill-current animate-pulse\" />\n              <span className=\"text-sm\">Recording</span>\n            </div>\n          )}\n          <Button variant=\"destructive\" onClick={completeInterview}>\n            End Interview\n          </Button>\n        </div>\n      </div>\n\n      <div className=\"flex h-[calc(100vh-80px)]\">\n        {/* AI Interviewer */}\n        <div className=\"w-1/3 p-4 space-y-4\">\n          <Card className=\"bg-gray-800 border-gray-700\">\n            <CardHeader>\n              <CardTitle className=\"text-white text-sm\">AI Interviewer</CardTitle>\n            </CardHeader>\n            <CardContent>\n              {/* AI Avatar */}\n              <div className=\"aspect-video bg-gradient-to-br from-blue-900 to-purple-900 rounded-lg flex items-center justify-center mb-4 relative overflow-hidden\">\n                <div className={`relative transition-all duration-500 ${isInterviewerSpeaking ? 'scale-110' : 'scale-100'}`}>\n                  <div className=\"w-24 h-24 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center relative\">\n                    {/* Eyes */}\n                    <div className=\"absolute top-6 left-6 w-2 h-2 bg-white rounded-full\"></div>\n                    <div className=\"absolute top-6 right-6 w-2 h-2 bg-white rounded-full\"></div>\n                    \n                    {/* Mouth */}\n                    <div className={`absolute bottom-6 left-1/2 transform -translate-x-1/2 transition-all duration-300 ${\n                      isInterviewerSpeaking \n                        ? 'w-6 h-3 bg-white rounded-full animate-pulse' \n                        : 'w-4 h-1 bg-white rounded-full'\n                    }`}></div>\n                    \n                    {/* Speaking indicator */}\n                    {isInterviewerSpeaking && (\n                      <div className=\"absolute -bottom-2 left-1/2 transform -translate-x-1/2\">\n                        <div className=\"flex space-x-1\">\n                          <div className=\"w-1 h-1 bg-green-400 rounded-full animate-bounce\"></div>\n                          <div className=\"w-1 h-1 bg-green-400 rounded-full animate-bounce\" style={{animationDelay: '0.1s'}}></div>\n                          <div className=\"w-1 h-1 bg-green-400 rounded-full animate-bounce\" style={{animationDelay: '0.2s'}}></div>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                  \n                  {/* Listening indicator */}\n                  {isListening && (\n                    <div className=\"absolute -top-2 left-1/2 transform -translate-x-1/2\">\n                      <div className=\"flex items-center space-x-1 bg-red-500 px-2 py-1 rounded-full text-xs\">\n                        <div className=\"w-1 h-1 bg-white rounded-full animate-pulse\"></div>\n                        <span className=\"text-white\">Listening</span>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n              \n              <p className=\"text-sm text-gray-400 text-center\">\n                {isInterviewerSpeaking ? '🗣️ Speaking' : isListening ? '👂 Listening' : '💭 Thinking'}\n              </p>\n            </CardContent>\n          </Card>\n\n          {/* Speech Bubble */}\n          {interviewerMessage && (\n            <Card className=\"bg-blue-900 border-blue-700\">\n              <CardContent className=\"p-3\">\n                <div className=\"relative\">\n                  <div className=\"bg-blue-800 p-3 rounded-lg text-sm\">\n                    <p className=\"text-blue-100\">{interviewerMessage}</p>\n                  </div>\n                  <div className=\"absolute -top-1 left-4 w-0 h-0 border-l-2 border-r-2 border-b-2 border-transparent border-b-blue-800\"></div>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n        </div>\n\n        {/* Student Video */}\n        <div className=\"w-1/3 p-4\">\n          <Card className=\"bg-gray-800 border-gray-700 h-full\">\n            <CardHeader>\n              <CardTitle className=\"text-white text-sm\">Your Video</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"aspect-video bg-gray-700 rounded-lg overflow-hidden relative\">\n                <video\n                  ref={videoRef}\n                  autoPlay\n                  playsInline\n                  muted\n                  className=\"w-full h-full object-cover\"\n                />\n                \n                {/* Recording indicator */}\n                {isRecording && (\n                  <div className=\"absolute top-2 right-2 bg-red-600 text-white px-2 py-1 rounded text-xs flex items-center\">\n                    <Circle className=\"h-2 w-2 fill-current mr-1 animate-pulse\" />\n                    REC\n                  </div>\n                )}\n              </div>\n              \n              <div className=\"mt-4 text-center\">\n                <p className=\"text-sm text-gray-400 mb-2\">\n                  {invitation?.studentName}\n                </p>\n                \n                {!hasStarted ? (\n                  <Button onClick={startInterview} className=\"bg-green-600 hover:bg-green-700\">\n                    <Play className=\"h-4 w-4 mr-2\" />\n                    Start Interview\n                  </Button>\n                ) : (\n                  <div className=\"flex justify-center space-x-2\">\n                    <Button \n                      size=\"sm\" \n                      variant=\"outline\"\n                      onClick={repeatQuestion}\n                      disabled={isInterviewerSpeaking}\n                      className=\"border-gray-600\"\n                    >\n                      <RotateCcw className=\"h-4 w-4 mr-1\" />\n                      Repeat\n                    </Button>\n                    \n                    {currentQuestionIndex < interviewQuestions.length - 1 ? (\n                      <Button \n                        size=\"sm\" \n                        onClick={nextQuestion}\n                        className=\"bg-blue-600 hover:bg-blue-700\"\n                      >\n                        <ArrowRight className=\"h-4 w-4 mr-1\" />\n                        Next\n                      </Button>\n                    ) : (\n                      <Button \n                        size=\"sm\" \n                        onClick={completeInterview}\n                        className=\"bg-green-600 hover:bg-green-700\"\n                      >\n                        <Square className=\"h-4 w-4 mr-1\" />\n                        Finish\n                      </Button>\n                    )}\n                  </div>\n                )}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Question Panel */}\n        <div className=\"w-1/3 p-4\">\n          <Card className=\"bg-gray-800 border-gray-700 h-full\">\n            <CardHeader>\n              <CardTitle className=\"text-white text-sm\">Current Question</CardTitle>\n              <CardDescription className=\"text-gray-400\">\n                {currentQuestion?.type} • {currentQuestion?.timeLimit}s limit\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"bg-gray-700 p-4 rounded-lg mb-4\">\n                <p className=\"text-gray-300\">{currentQuestion?.question}</p>\n              </div>\n              \n              {hasStarted && (\n                <div className=\"space-y-3\">\n                  <div className=\"flex justify-between text-sm text-gray-400\">\n                    <span>Time for this question</span>\n                    <span>{formatTime(Math.floor((Date.now() - questionStartTime) / 1000))}</span>\n                  </div>\n                  \n                  <div className=\"w-full bg-gray-700 rounded-full h-2\">\n                    <div \n                      className=\"bg-blue-500 h-2 rounded-full transition-all duration-1000\"\n                      style={{ \n                        width: `${Math.min(((Date.now() - questionStartTime) / 1000 / currentQuestion.timeLimit) * 100, 100)}%` \n                      }}\n                    ></div>\n                  </div>\n                  \n                  <div className=\"text-center\">\n                    <p className=\"text-xs text-gray-400\">\n                      {isListening ? 'You may begin your response' : 'Please wait for the question'}\n                    </p>\n                  </div>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AAuBA,2BAA2B;AAC3B,MAAM,qBAAqB;IACzB;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,WAAW;IACb;CACD;AAEc,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,QAAQ,OAAO,KAAK;IAE1B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAwB;IACtD,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU,EAAE;IAE3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B;IACzE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACzD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAE/D,MAAM,kBAAkB,kBAAkB,CAAC,qBAAqB;IAEhE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,uBAAuB;YACvB,MAAM,cAAc,6HAAA,CAAA,WAAQ,CAAC,cAAc;YAC3C,IAAI,CAAC,aAAa;gBAChB,OAAO,IAAI,CAAC,CAAC,kBAAkB,EAAE,OAAO;gBACxC;YACF;YAEA,kBAAkB;YAClB,IAAI,OAAO;gBACT,MAAM,iBAAiB,iIAAA,CAAA,eAAY,CAAC,oBAAoB,CAAC;gBACzD,IAAI,gBAAgB;oBAClB,cAAc;gBAChB,OAAO;oBACL,OAAO,IAAI,CAAC;oBACZ;gBACF;gBACA,WAAW;YACb;QACF;uCAAG;QAAC;QAAO;KAAO;IAElB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,uBAAuB;YACvB,MAAM;4DAAc;oBAClB,IAAI;wBACF,MAAM,cAAc,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;4BAC5D,OAAO;4BACP,OAAO;wBACT;wBACA,UAAU;wBACV,IAAI,SAAS,OAAO,EAAE;4BACpB,SAAS,OAAO,CAAC,SAAS,GAAG;wBAC/B;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,2BAA2B;oBAC3C;gBACF;;YAEA;YAEA;gDAAO;oBACL,IAAI,QAAQ;wBACV,OAAO,SAAS,GAAG,OAAO;4DAAC,CAAA,QAAS,MAAM,IAAI;;oBAChD;gBACF;;QACF;uCAAG,EAAE;IAEL,eAAe;IACf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI;YACJ,IAAI,cAAc,CAAC,UAAU;gBAC3B,WAAW;oDAAY;wBACrB;4DAAe,CAAA,OAAQ,OAAO;;oBAChC;mDAAG;YACL;YACA;gDAAO,IAAM,cAAc;;QAC7B;uCAAG;QAAC;QAAY;KAAS;IAEzB,MAAM,gBAAgB,CAAC;QACrB,yBAAyB;QACzB,sBAAsB;QAEtB,IAAI,qBAAqB,QAAQ;YAC/B,OAAO,eAAe,CAAC,MAAM;YAE7B,MAAM,YAAY,IAAI,yBAAyB;YAC/C,UAAU,IAAI,GAAG;YACjB,UAAU,KAAK,GAAG;YAClB,UAAU,MAAM,GAAG;YAEnB,MAAM,SAAS,OAAO,eAAe,CAAC,SAAS;YAC/C,MAAM,iBAAiB,OAAO,IAAI,CAAC,CAAA,QACjC,MAAM,IAAI,CAAC,QAAQ,CAAC,aACpB,MAAM,IAAI,CAAC,QAAQ,CAAC,gBACpB,MAAM,IAAI,CAAC,UAAU,CAAC;YAExB,IAAI,gBAAgB;gBAClB,UAAU,KAAK,GAAG;YACpB;YAEA,UAAU,KAAK,GAAG;gBAChB,yBAAyB;gBACzB,eAAe;YACjB;YAEA,UAAU,OAAO,GAAG;gBAClB,yBAAyB;gBACzB,eAAe;YACjB;YAEA,OAAO,eAAe,CAAC,KAAK,CAAC;QAC/B,OAAO;YACL,WAAW;gBACT,yBAAyB;gBACzB,eAAe;YACjB,GAAG;QACL;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,QAAQ;QAEb,IAAI;YACF,MAAM,gBAAgB,IAAI,cAAc,QAAQ;gBAC9C,UAAU;YACZ;YAEA,kBAAkB,OAAO,GAAG,EAAE;YAE9B,cAAc,eAAe,GAAG,CAAC;gBAC/B,IAAI,MAAM,IAAI,CAAC,IAAI,GAAG,GAAG;oBACvB,kBAAkB,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI;gBAC3C;YACF;YAEA,cAAc,MAAM,GAAG;gBACrB,MAAM,OAAO,IAAI,KAAK,kBAAkB,OAAO,EAAE;oBAAE,MAAM;gBAAa;gBACtE,kBAAkB,CAAA,OAAQ;2BAAI;wBAAM;qBAAK;YAC3C;YAEA,cAAc,KAAK,CAAC,MAAM,4BAA4B;;YACtD,iBAAiB,OAAO,GAAG;YAC3B,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,iBAAiB,OAAO,IAAI,aAAa;YAC3C,iBAAiB,OAAO,CAAC,IAAI;YAC7B,eAAe;QACjB;IACF;IAEA,MAAM,iBAAiB;QACrB,cAAc;QACd,qBAAqB,KAAK,GAAG;QAC7B;QAEA,kBAAkB;QAClB,MAAM,iBAAiB,CAAC,oDAAoD,EAAE,mBAAmB,MAAM,CAAC,gDAAgD,CAAC;QACzJ,cAAc;QAEd,WAAW;YACT,cAAc,gBAAgB,QAAQ;QACxC,GAAG;IACL;IAEA,MAAM,eAAe;QACnB;QAEA,IAAI,uBAAuB,mBAAmB,MAAM,GAAG,GAAG;YACxD,wBAAwB,CAAA,OAAQ,OAAO;YACvC,qBAAqB,KAAK,GAAG;YAC7B,eAAe;YAEf,WAAW;gBACT;gBACA,cAAc,kBAAkB,CAAC,uBAAuB,EAAE,CAAC,QAAQ;YACrE,GAAG;QACL,OAAO;YACL,qBAAqB;YACrB;QACF;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,iBAAiB;YACnB,cAAc,gBAAgB,QAAQ;QACxC;IACF;IAEA,MAAM,oBAAoB;QACxB,eAAe;QACf,yBAAyB;QACzB,sBAAsB;QAEtB,MAAM,kBAAkB;QAExB,IAAI,qBAAqB,QAAQ;YAC/B,MAAM,YAAY,IAAI,yBAAyB;YAC/C,UAAU,KAAK,GAAG;gBAChB,yBAAyB;gBACzB,8BAA8B;gBAC9B,WAAW;oBACT,OAAO,IAAI,CAAC,CAAC,oBAAoB,EAAE,OAAO;gBAC5C,GAAG;YACL;YACA,OAAO,eAAe,CAAC,KAAK,CAAC;QAC/B,OAAO;YACL,WAAW;gBACT,yBAAyB;gBACzB,OAAO,IAAI,CAAC,CAAC,oBAAoB,EAAE,OAAO;YAC5C,GAAG;QACL;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAClF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;kCAAE;;;;;;;;;;;;;;;;;IAIX;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAgC,YAAY;;;;;;0CAC1D,6LAAC;gCAAE,WAAU;;oCAAgB;oCACjB,uBAAuB;oCAAE;oCAAK,mBAAmB,MAAM;oCAChE,cAAc,CAAC,GAAG,EAAE,WAAW,cAAc;;;;;;;;;;;;;kCAGlD,6LAAC;wBAAI,WAAU;;4BACZ,6BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAG9B,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAc,SAAS;0CAAmB;;;;;;;;;;;;;;;;;;0BAM9D,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAqB;;;;;;;;;;;kDAE5C,6LAAC,mIAAA,CAAA,cAAW;;0DAEV,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAW,CAAC,qCAAqC,EAAE,wBAAwB,cAAc,aAAa;;sEACzG,6LAAC;4DAAI,WAAU;;8EAEb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;;;;;;8EAGf,6LAAC;oEAAI,WAAW,CAAC,kFAAkF,EACjG,wBACI,gDACA,iCACJ;;;;;;gEAGD,uCACC,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;;;;;0FACf,6LAAC;gFAAI,WAAU;gFAAmD,OAAO;oFAAC,gBAAgB;gFAAM;;;;;;0FAChG,6LAAC;gFAAI,WAAU;gFAAmD,OAAO;oFAAC,gBAAgB;gFAAM;;;;;;;;;;;;;;;;;;;;;;;wDAOvG,6BACC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;wEAAK,WAAU;kFAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAOvC,6LAAC;gDAAE,WAAU;0DACV,wBAAwB,iBAAiB,cAAc,iBAAiB;;;;;;;;;;;;;;;;;;4BAM9E,oCACC,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;8DAAiB;;;;;;;;;;;0DAEhC,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQzB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAqB;;;;;;;;;;;8CAE5C,6LAAC,mIAAA,CAAA,cAAW;;sDACV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,KAAK;oDACL,QAAQ;oDACR,WAAW;oDACX,KAAK;oDACL,WAAU;;;;;;gDAIX,6BACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAA4C;;;;;;;;;;;;;sDAMpE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DACV,YAAY;;;;;;gDAGd,CAAC,2BACA,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAS;oDAAgB,WAAU;;sEACzC,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;yEAInC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS;4DACT,UAAU;4DACV,WAAU;;8EAEV,6LAAC,mNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;wDAIvC,uBAAuB,mBAAmB,MAAM,GAAG,kBAClD,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAS;4DACT,WAAU;;8EAEV,6LAAC,qNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;gEAAiB;;;;;;iFAIzC,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAS;4DACT,WAAU;;8EAEV,6LAAC,yMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYnD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAqB;;;;;;sDAC1C,6LAAC,mIAAA,CAAA,kBAAe;4CAAC,WAAU;;gDACxB,iBAAiB;gDAAK;gDAAI,iBAAiB;gDAAU;;;;;;;;;;;;;8CAG1D,6LAAC,mIAAA,CAAA,cAAW;;sDACV,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;0DAAiB,iBAAiB;;;;;;;;;;;wCAGhD,4BACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;sEAAM,WAAW,KAAK,KAAK,CAAC,CAAC,KAAK,GAAG,KAAK,iBAAiB,IAAI;;;;;;;;;;;;8DAGlE,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,WAAU;wDACV,OAAO;4DACL,OAAO,GAAG,KAAK,GAAG,CAAC,AAAC,CAAC,KAAK,GAAG,KAAK,iBAAiB,IAAI,OAAO,gBAAgB,SAAS,GAAI,KAAK,KAAK,CAAC,CAAC;wDACzG;;;;;;;;;;;8DAIJ,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;kEACV,cAAc,gCAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWrE;GAjcwB;;QACP,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}