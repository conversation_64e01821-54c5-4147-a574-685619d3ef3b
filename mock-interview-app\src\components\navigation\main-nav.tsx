'use client'

import Link from 'next/link'
import { useSession, signOut } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  User, 
  Settings, 
  LogOut, 
  Calendar, 
  Users, 
  BarChart3, 
  BookOpen,
  Video,
  CreditCard
} from 'lucide-react'

export function MainNav() {
  const { data: session, status } = useSession()

  if (status === 'loading') {
    return <div>Loading...</div>
  }

  if (!session) {
    return (
      <nav className="border-b">
        <div className="flex h-16 items-center px-4">
          <Link href="/" className="flex items-center space-x-2">
            <Video className="h-6 w-6" />
            <span className="font-bold">Mock Interview</span>
          </Link>
          <div className="ml-auto flex items-center space-x-4">
            <Link href="/auth/signin">
              <Button variant="ghost">Sign In</Button>
            </Link>
            <Link href="/auth/signup">
              <Button>Sign Up</Button>
            </Link>
          </div>
        </div>
      </nav>
    )
  }

  const getNavItems = () => {
    const role = session.user.role
    
    switch (role) {
      case 'ADMIN':
        return [
          { href: '/admin/dashboard', label: 'Dashboard', icon: BarChart3 },
          { href: '/admin/tenants', label: 'Tenants', icon: Users },
          { href: '/admin/users', label: 'Users', icon: User },
          { href: '/admin/analytics', label: 'Analytics', icon: BarChart3 },
        ]
      case 'TENANT':
        return [
          { href: '/tenant/dashboard', label: 'Dashboard', icon: BarChart3 },
          { href: '/tenant/interviews', label: 'Interviews', icon: Calendar },
          { href: '/tenant/students', label: 'Students', icon: Users },
          { href: '/tenant/questions', label: 'Questions', icon: BookOpen },
          { href: '/tenant/subscription', label: 'Subscription', icon: CreditCard },
        ]
      case 'STUDENT':
        return [
          { href: '/student/dashboard', label: 'Dashboard', icon: BarChart3 },
          { href: '/student/interviews', label: 'Interviews', icon: Calendar },
          { href: '/student/results', label: 'Results', icon: BarChart3 },
        ]
      default:
        return []
    }
  }

  const navItems = getNavItems()

  return (
    <nav className="border-b">
      <div className="flex h-16 items-center px-4">
        <Link href="/" className="flex items-center space-x-2">
          <Video className="h-6 w-6" />
          <span className="font-bold">Mock Interview</span>
        </Link>
        
        <div className="ml-6 flex items-center space-x-6">
          {navItems.map((item) => {
            const Icon = item.icon
            return (
              <Link
                key={item.href}
                href={item.href}
                className="flex items-center space-x-2 text-sm font-medium transition-colors hover:text-primary"
              >
                <Icon className="h-4 w-4" />
                <span>{item.label}</span>
              </Link>
            )
          })}
        </div>

        <div className="ml-auto flex items-center space-x-4">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={session.user.image || ''} alt={session.user.name || ''} />
                  <AvatarFallback>
                    {session.user.name?.charAt(0) || session.user.email?.charAt(0)}
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">{session.user.name}</p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {session.user.email}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    Role: {session.user.role}
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href="/profile" className="flex items-center">
                  <User className="mr-2 h-4 w-4" />
                  <span>Profile</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/settings" className="flex items-center">
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Settings</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="cursor-pointer"
                onSelect={() => signOut({ callbackUrl: '/' })}
              >
                <LogOut className="mr-2 h-4 w-4" />
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </nav>
  )
}
