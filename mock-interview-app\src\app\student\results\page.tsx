'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { MainNav } from '@/components/navigation/main-nav'
import { 
  TrendingUp, 
  TrendingDown,
  Award,
  Calendar,
  Clock,
  Target,
  BarChart3,
  Eye
} from 'lucide-react'

interface InterviewResult {
  id: string
  interviewTitle: string
  completedAt: Date
  duration: number
  overallScore: number
  questionResults: {
    question: string
    type: 'CODING' | 'THEORY' | 'BEHAVIORAL'
    score: number
    feedback: string
  }[]
  interviewer: string
  strengths: string[]
  improvements: string[]
}

export default function StudentResults() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [results, setResults] = useState<InterviewResult[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/signin')
      return
    }

    if (session.user.role !== 'STUDENT') {
      router.push('/')
      return
    }

    fetchResults()
  }, [session, status, router])

  const fetchResults = async () => {
    try {
      // Demo results data
      const demoResults: InterviewResult[] = [
        {
          id: 'result-1',
          interviewTitle: 'Frontend Developer Interview',
          completedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
          duration: 60,
          overallScore: 85,
          questionResults: [
            {
              question: 'Two Sum Problem',
              type: 'CODING',
              score: 90,
              feedback: 'Excellent solution with optimal time complexity. Good explanation of the approach.'
            },
            {
              question: 'React Hooks',
              type: 'THEORY',
              score: 80,
              feedback: 'Good understanding of hooks. Could improve on lifecycle explanations.'
            }
          ],
          interviewer: 'Tech Corp',
          strengths: ['Problem-solving skills', 'Code optimization', 'Clear communication'],
          improvements: ['System design concepts', 'Testing strategies']
        },
        {
          id: 'result-2',
          interviewTitle: 'Behavioral Interview',
          completedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 1 week ago
          duration: 45,
          overallScore: 78,
          questionResults: [
            {
              question: 'Team Collaboration',
              type: 'BEHAVIORAL',
              score: 85,
              feedback: 'Great examples of teamwork and conflict resolution.'
            },
            {
              question: 'Leadership Experience',
              type: 'BEHAVIORAL',
              score: 70,
              feedback: 'Good examples but could provide more specific details about impact.'
            }
          ],
          interviewer: 'HR Team',
          strengths: ['Communication skills', 'Team collaboration', 'Adaptability'],
          improvements: ['Leadership examples', 'Quantifying achievements']
        }
      ]
      
      setResults(demoResults)
      setLoading(false)
    } catch (error) {
      console.error('Error fetching results:', error)
      setLoading(false)
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreBadge = (score: number) => {
    if (score >= 80) return <Badge className="bg-green-100 text-green-800">Excellent</Badge>
    if (score >= 60) return <Badge className="bg-yellow-100 text-yellow-800">Good</Badge>
    return <Badge className="bg-red-100 text-red-800">Needs Improvement</Badge>
  }

  const averageScore = results.length > 0 
    ? Math.round(results.reduce((sum, result) => sum + result.overallScore, 0) / results.length)
    : 0

  if (loading) {
    return <div className="flex justify-center items-center min-h-screen">Loading...</div>
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <MainNav />
      
      <div className="py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Interview Results</h1>
            <p className="text-gray-600 mt-2">
              Track your progress and review feedback from completed interviews
            </p>
          </div>

          {/* Summary Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Interviews</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{results.length}</div>
                <p className="text-xs text-muted-foreground">
                  Completed sessions
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Average Score</CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${getScoreColor(averageScore)}`}>
                  {averageScore}%
                </div>
                <p className="text-xs text-muted-foreground">
                  Overall performance
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Best Score</CardTitle>
                <Award className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {results.length > 0 ? Math.max(...results.map(r => r.overallScore)) : 0}%
                </div>
                <p className="text-xs text-muted-foreground">
                  Highest achievement
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Time</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {results.reduce((sum, result) => sum + result.duration, 0)} min
                </div>
                <p className="text-xs text-muted-foreground">
                  Practice time
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Results List */}
          <div className="space-y-6">
            {results.map((result) => (
              <Card key={result.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="flex items-center gap-3">
                        {result.interviewTitle}
                        {getScoreBadge(result.overallScore)}
                      </CardTitle>
                      <CardDescription className="mt-2">
                        Completed on {result.completedAt.toLocaleDateString()} • {result.duration} minutes
                      </CardDescription>
                    </div>
                    <div className="text-right">
                      <div className={`text-3xl font-bold ${getScoreColor(result.overallScore)}`}>
                        {result.overallScore}%
                      </div>
                      <p className="text-sm text-gray-600">Overall Score</p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Question Results */}
                    <div>
                      <h4 className="font-semibold mb-3">Question Performance</h4>
                      <div className="space-y-3">
                        {result.questionResults.map((qResult, index) => (
                          <div key={index} className="border rounded-lg p-3">
                            <div className="flex justify-between items-center mb-2">
                              <span className="font-medium">{qResult.question}</span>
                              <div className="flex items-center gap-2">
                                <Badge variant="outline">{qResult.type}</Badge>
                                <span className={`font-bold ${getScoreColor(qResult.score)}`}>
                                  {qResult.score}%
                                </span>
                              </div>
                            </div>
                            <p className="text-sm text-gray-600">{qResult.feedback}</p>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Strengths & Improvements */}
                    <div>
                      <div className="mb-4">
                        <h4 className="font-semibold mb-2 text-green-700">Strengths</h4>
                        <ul className="space-y-1">
                          {result.strengths.map((strength, index) => (
                            <li key={index} className="text-sm text-green-600 flex items-center">
                              <TrendingUp className="h-3 w-3 mr-2" />
                              {strength}
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div>
                        <h4 className="font-semibold mb-2 text-orange-700">Areas for Improvement</h4>
                        <ul className="space-y-1">
                          {result.improvements.map((improvement, index) => (
                            <li key={index} className="text-sm text-orange-600 flex items-center">
                              <TrendingDown className="h-3 w-3 mr-2" />
                              {improvement}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="mt-4 pt-4 border-t flex justify-between items-center">
                    <span className="text-sm text-gray-600">
                      Interviewer: {result.interviewer}
                    </span>
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4 mr-2" />
                      View Detailed Report
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {results.length === 0 && (
            <Card>
              <CardContent className="text-center py-12">
                <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No results yet</h3>
                <p className="text-gray-600 mb-4">
                  Complete your first interview to see results and feedback
                </p>
                <Button onClick={() => router.push('/interview/demo-practice')}>
                  Start Practice Interview
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
