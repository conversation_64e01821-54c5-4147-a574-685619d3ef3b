{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/lib/email-service.ts"], "sourcesContent": ["import nodemailer from 'nodemailer'\nimport { prisma } from './prisma'\n\nexport interface EmailOptions {\n  to: string\n  subject: string\n  html: string\n  attachments?: Array<{\n    filename: string\n    path: string\n  }>\n}\n\nexport class EmailService {\n  private static transporter = nodemailer.createTransporter({\n    host: process.env.EMAIL_HOST,\n    port: parseInt(process.env.EMAIL_PORT || '587'),\n    secure: false,\n    auth: {\n      user: process.env.EMAIL_USER,\n      pass: process.env.EMAIL_PASS,\n    },\n  })\n\n  static async sendEmail(options: EmailOptions): Promise<boolean> {\n    try {\n      const info = await this.transporter.sendMail({\n        from: process.env.EMAIL_USER,\n        to: options.to,\n        subject: options.subject,\n        html: options.html,\n        attachments: options.attachments,\n      })\n\n      // Log email\n      await prisma.emailLog.create({\n        data: {\n          to: options.to,\n          subject: options.subject,\n          body: options.html,\n          status: 'SENT',\n          sentAt: new Date(),\n        },\n      })\n\n      return true\n    } catch (error) {\n      console.error('Error sending email:', error)\n      \n      // Log failed email\n      await prisma.emailLog.create({\n        data: {\n          to: options.to,\n          subject: options.subject,\n          body: options.html,\n          status: 'FAILED',\n        },\n      })\n\n      return false\n    }\n  }\n\n  static async sendInterviewInvitation(\n    studentEmail: string,\n    studentName: string,\n    interviewDetails: {\n      title: string\n      scheduledAt: Date\n      duration: number\n      meetingLink: string\n    }\n  ): Promise<boolean> {\n    const html = `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <h2 style=\"color: #2563eb;\">Interview Invitation</h2>\n        <p>Dear ${studentName},</p>\n        <p>You have been invited to participate in an interview:</p>\n        \n        <div style=\"background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n          <h3 style=\"margin-top: 0;\">${interviewDetails.title}</h3>\n          <p><strong>Date & Time:</strong> ${interviewDetails.scheduledAt.toLocaleString()}</p>\n          <p><strong>Duration:</strong> ${interviewDetails.duration} minutes</p>\n          <p><strong>Meeting Link:</strong> <a href=\"${interviewDetails.meetingLink}\">${interviewDetails.meetingLink}</a></p>\n        </div>\n        \n        <p>Please make sure to:</p>\n        <ul>\n          <li>Test your camera and microphone before the interview</li>\n          <li>Ensure you have a stable internet connection</li>\n          <li>Prepare for coding and theory questions</li>\n          <li>Join the meeting 5 minutes early</li>\n        </ul>\n        \n        <p>Good luck with your interview!</p>\n        <p>Best regards,<br>Mock Interview Team</p>\n      </div>\n    `\n\n    return this.sendEmail({\n      to: studentEmail,\n      subject: `Interview Invitation - ${interviewDetails.title}`,\n      html,\n    })\n  }\n\n  static async sendInterviewResults(\n    studentEmail: string,\n    studentName: string,\n    results: {\n      score: number\n      feedback: string\n      recordingUrl?: string\n    }\n  ): Promise<boolean> {\n    const html = `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <h2 style=\"color: #2563eb;\">Interview Results</h2>\n        <p>Dear ${studentName},</p>\n        <p>Your interview has been completed. Here are your results:</p>\n        \n        <div style=\"background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n          <h3 style=\"margin-top: 0;\">Overall Score: ${results.score}/100</h3>\n          <h4>Feedback:</h4>\n          <p>${results.feedback}</p>\n          ${results.recordingUrl ? `<p><strong>Recording:</strong> <a href=\"${results.recordingUrl}\">View Recording</a></p>` : ''}\n        </div>\n        \n        <p>Keep practicing and improving your skills!</p>\n        <p>Best regards,<br>Mock Interview Team</p>\n      </div>\n    `\n\n    return this.sendEmail({\n      to: studentEmail,\n      subject: 'Your Interview Results',\n      html,\n    })\n  }\n\n  static async sendWelcomeEmail(\n    userEmail: string,\n    userName: string,\n    role: string\n  ): Promise<boolean> {\n    const html = `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <h2 style=\"color: #2563eb;\">Welcome to Mock Interview Platform!</h2>\n        <p>Dear ${userName},</p>\n        <p>Welcome to our Mock Interview Platform! Your account has been created successfully.</p>\n        \n        <div style=\"background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n          <h3 style=\"margin-top: 0;\">Account Details</h3>\n          <p><strong>Email:</strong> ${userEmail}</p>\n          <p><strong>Role:</strong> ${role}</p>\n        </div>\n        \n        <p>You can now log in and start using the platform.</p>\n        <p>Best regards,<br>Mock Interview Team</p>\n      </div>\n    `\n\n    return this.sendEmail({\n      to: userEmail,\n      subject: 'Welcome to Mock Interview Platform',\n      html,\n    })\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAYO,MAAM;IACX,OAAe,cAAc,iJAAA,CAAA,UAAU,CAAC,iBAAiB,CAAC;QACxD,MAAM,QAAQ,GAAG,CAAC,UAAU;QAC5B,MAAM,SAAS,QAAQ,GAAG,CAAC,UAAU,IAAI;QACzC,QAAQ;QACR,MAAM;YACJ,MAAM,QAAQ,GAAG,CAAC,UAAU;YAC5B,MAAM,QAAQ,GAAG,CAAC,UAAU;QAC9B;IACF,GAAE;IAEF,aAAa,UAAU,OAAqB,EAAoB;QAC9D,IAAI;YACF,MAAM,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;gBAC3C,MAAM,QAAQ,GAAG,CAAC,UAAU;gBAC5B,IAAI,QAAQ,EAAE;gBACd,SAAS,QAAQ,OAAO;gBACxB,MAAM,QAAQ,IAAI;gBAClB,aAAa,QAAQ,WAAW;YAClC;YAEA,YAAY;YACZ,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAC3B,MAAM;oBACJ,IAAI,QAAQ,EAAE;oBACd,SAAS,QAAQ,OAAO;oBACxB,MAAM,QAAQ,IAAI;oBAClB,QAAQ;oBACR,QAAQ,IAAI;gBACd;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YAEtC,mBAAmB;YACnB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAC3B,MAAM;oBACJ,IAAI,QAAQ,EAAE;oBACd,SAAS,QAAQ,OAAO;oBACxB,MAAM,QAAQ,IAAI;oBAClB,QAAQ;gBACV;YACF;YAEA,OAAO;QACT;IACF;IAEA,aAAa,wBACX,YAAoB,EACpB,WAAmB,EACnB,gBAKC,EACiB;QAClB,MAAM,OAAO,CAAC;;;gBAGF,EAAE,YAAY;;;;qCAIO,EAAE,iBAAiB,KAAK,CAAC;2CACnB,EAAE,iBAAiB,WAAW,CAAC,cAAc,GAAG;wCACnD,EAAE,iBAAiB,QAAQ,CAAC;qDACf,EAAE,iBAAiB,WAAW,CAAC,EAAE,EAAE,iBAAiB,WAAW,CAAC;;;;;;;;;;;;;;IAcjH,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,IAAI;YACJ,SAAS,CAAC,uBAAuB,EAAE,iBAAiB,KAAK,EAAE;YAC3D;QACF;IACF;IAEA,aAAa,qBACX,YAAoB,EACpB,WAAmB,EACnB,OAIC,EACiB;QAClB,MAAM,OAAO,CAAC;;;gBAGF,EAAE,YAAY;;;;oDAIsB,EAAE,QAAQ,KAAK,CAAC;;aAEvD,EAAE,QAAQ,QAAQ,CAAC;UACtB,EAAE,QAAQ,YAAY,GAAG,CAAC,wCAAwC,EAAE,QAAQ,YAAY,CAAC,wBAAwB,CAAC,GAAG,GAAG;;;;;;IAM9H,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,IAAI;YACJ,SAAS;YACT;QACF;IACF;IAEA,aAAa,iBACX,SAAiB,EACjB,QAAgB,EAChB,IAAY,EACM;QAClB,MAAM,OAAO,CAAC;;;gBAGF,EAAE,SAAS;;;;;qCAKU,EAAE,UAAU;oCACb,EAAE,KAAK;;;;;;IAMvC,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,IAAI;YACJ,SAAS;YACT;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/app/api/auth/register/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport bcrypt from 'bcryptjs'\nimport { prisma } from '@/lib/prisma'\nimport { EmailService } from '@/lib/email-service'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { name, email, password, role } = await request.json()\n\n    // Validate input\n    if (!name || !email || !password || !role) {\n      return NextResponse.json(\n        { message: 'All fields are required' },\n        { status: 400 }\n      )\n    }\n\n    // Check if user already exists\n    const existingUser = await prisma.user.findUnique({\n      where: { email }\n    })\n\n    if (existingUser) {\n      return NextResponse.json(\n        { message: 'User with this email already exists' },\n        { status: 400 }\n      )\n    }\n\n    // Hash password\n    const hashedPassword = await bcrypt.hash(password, 12)\n\n    // Create user\n    const user = await prisma.user.create({\n      data: {\n        name,\n        email,\n        password: hashedPassword,\n        role: role.toUpperCase(),\n      }\n    })\n\n    // Send welcome email\n    await EmailService.sendWelcomeEmail(email, name, role)\n\n    // Remove password from response\n    const { password: _, ...userWithoutPassword } = user\n\n    return NextResponse.json(\n      { \n        message: 'User created successfully',\n        user: userWithoutPassword\n      },\n      { status: 201 }\n    )\n  } catch (error) {\n    console.error('Registration error:', error)\n    return NextResponse.json(\n      { message: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE1D,iBAAiB;QACjB,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM;YACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAA0B,GACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,+BAA+B;QAC/B,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAChD,OAAO;gBAAE;YAAM;QACjB;QAEA,IAAI,cAAc;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAsC,GACjD;gBAAE,QAAQ;YAAI;QAElB;QAEA,gBAAgB;QAChB,MAAM,iBAAiB,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;QAEnD,cAAc;QACd,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV,MAAM,KAAK,WAAW;YACxB;QACF;QAEA,qBAAqB;QACrB,MAAM,gIAAA,CAAA,eAAY,CAAC,gBAAgB,CAAC,OAAO,MAAM;QAEjD,gCAAgC;QAChC,MAAM,EAAE,UAAU,CAAC,EAAE,GAAG,qBAAqB,GAAG;QAEhD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,MAAM;QACR,GACA;YAAE,QAAQ;QAAI;IAElB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAAwB,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}