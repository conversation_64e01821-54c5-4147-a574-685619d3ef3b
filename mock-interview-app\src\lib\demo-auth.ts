// Demo authentication system for testing without database
export interface DemoUser {
  id: string
  email: string
  name: string
  role: 'ADMIN' | 'TENANT' | 'STUDENT'
  password: string
}

// Demo users for testing
export const DEMO_USERS: DemoUser[] = [
  {
    id: 'admin-1',
    email: '<EMAIL>',
    name: 'System Administrator',
    role: 'ADMIN',
    password: 'admin123'
  },
  {
    id: 'tenant-1',
    email: '<EMAIL>',
    name: 'TechCorp Manager',
    role: 'TENANT',
    password: 'tenant123'
  },
  {
    id: 'student-1',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: 'STUDENT',
    password: 'student123'
  },
  {
    id: 'student-2',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: 'STUDENT',
    password: 'student123'
  },
  {
    id: 'tenant-2',
    email: '<EMAIL>',
    name: 'StartupXYZ HR',
    role: 'TENANT',
    password: 'startup123'
  }
]

export class DemoAuthService {
  // Validate demo user credentials
  static validateUser(email: string, password: string): DemoUser | null {
    const user = DEMO_USERS.find(u => u.email === email && u.password === password)
    return user || null
  }

  // Check if email exists
  static emailExists(email: string): boolean {
    return DEMO_USERS.some(u => u.email === email)
  }

  // Add new demo user (for signup)
  static addUser(userData: Omit<DemoUser, 'id'>): DemoUser {
    const newUser: DemoUser = {
      id: `demo-${Date.now()}`,
      ...userData
    }
    DEMO_USERS.push(newUser)
    return newUser
  }

  // Get user by ID
  static getUserById(id: string): DemoUser | null {
    return DEMO_USERS.find(u => u.id === id) || null
  }

  // Get all users (for admin)
  static getAllUsers(): DemoUser[] {
    return DEMO_USERS
  }
}
