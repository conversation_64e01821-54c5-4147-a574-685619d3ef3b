'use client'

import { useEffect, useState, useRef } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { EmailService, InterviewInvitation } from '@/lib/email-service'
import { DemoAuth } from '@/lib/demo-auth'
import { 
  Video, 
  VideoOff, 
  Mic, 
  MicOff, 
  Settings, 
  CheckCircle, 
  AlertCircle,
  Volume2,
  VolumeX,
  RefreshCw,
  ArrowRight,
  Camera
} from 'lucide-react'

export default function CameraSetupPage() {
  const params = useParams()
  const router = useRouter()
  const token = params.token as string

  const videoRef = useRef<HTMLVideoElement>(null)
  const audioRef = useRef<HTMLAudioElement>(null)

  const [invitation, setInvitation] = useState<InterviewInvitation | null>(null)
  const [loading, setLoading] = useState(true)
  const [stream, setStream] = useState<MediaStream | null>(null)
  const [videoDevices, setVideoDevices] = useState<MediaDeviceInfo[]>([])
  const [audioDevices, setAudioDevices] = useState<MediaDeviceInfo[]>([])
  const [selectedVideoDevice, setSelectedVideoDevice] = useState<string>('')
  const [selectedAudioDevice, setSelectedAudioDevice] = useState<string>('')
  const [isVideoEnabled, setIsVideoEnabled] = useState(true)
  const [isAudioEnabled, setIsAudioEnabled] = useState(true)
  const [permissionStatus, setPermissionStatus] = useState<'pending' | 'granted' | 'denied'>('pending')
  const [setupComplete, setSetupComplete] = useState(false)
  const [audioLevel, setAudioLevel] = useState(0)
  const [isTestingAudio, setIsTestingAudio] = useState(false)

  useEffect(() => {
    // Check authentication
    const currentUser = DemoAuth.getCurrentUser()
    if (!currentUser) {
      router.push(`/interview/invite/${token}`)
      return
    }

    // Load invitation
    if (token) {
      const invitationData = EmailService.getInvitationByToken(token)
      if (invitationData) {
        setInvitation(invitationData)
      } else {
        router.push('/')
        return
      }
      setLoading(false)
    }
  }, [token, router])

  const getMediaDevices = async () => {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices()
      const videoInputs = devices.filter(device => device.kind === 'videoinput')
      const audioInputs = devices.filter(device => device.kind === 'audioinput')
      
      setVideoDevices(videoInputs)
      setAudioDevices(audioInputs)
      
      if (videoInputs.length > 0 && !selectedVideoDevice) {
        setSelectedVideoDevice(videoInputs[0].deviceId)
      }
      if (audioInputs.length > 0 && !selectedAudioDevice) {
        setSelectedAudioDevice(audioInputs[0].deviceId)
      }
    } catch (error) {
      console.error('Error getting media devices:', error)
    }
  }

  const requestPermissions = async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: { deviceId: selectedVideoDevice || undefined },
        audio: { deviceId: selectedAudioDevice || undefined }
      })
      
      setStream(mediaStream)
      setPermissionStatus('granted')
      
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream
      }
      
      // Get updated device list with labels
      await getMediaDevices()
      
      // Set up audio level monitoring
      setupAudioLevelMonitoring(mediaStream)
      
    } catch (error) {
      console.error('Error accessing media devices:', error)
      setPermissionStatus('denied')
    }
  }

  const setupAudioLevelMonitoring = (mediaStream: MediaStream) => {
    const audioContext = new AudioContext()
    const analyser = audioContext.createAnalyser()
    const microphone = audioContext.createMediaStreamSource(mediaStream)
    const dataArray = new Uint8Array(analyser.frequencyBinCount)

    microphone.connect(analyser)
    analyser.fftSize = 256

    const updateAudioLevel = () => {
      analyser.getByteFrequencyData(dataArray)
      const average = dataArray.reduce((a, b) => a + b) / dataArray.length
      setAudioLevel(average)
      requestAnimationFrame(updateAudioLevel)
    }

    updateAudioLevel()
  }

  const toggleVideo = () => {
    if (stream) {
      const videoTrack = stream.getVideoTracks()[0]
      if (videoTrack) {
        videoTrack.enabled = !isVideoEnabled
        setIsVideoEnabled(!isVideoEnabled)
      }
    }
  }

  const toggleAudio = () => {
    if (stream) {
      const audioTrack = stream.getAudioTracks()[0]
      if (audioTrack) {
        audioTrack.enabled = !isAudioEnabled
        setIsAudioEnabled(!isAudioEnabled)
      }
    }
  }

  const changeVideoDevice = async (deviceId: string) => {
    setSelectedVideoDevice(deviceId)
    if (stream) {
      // Stop current stream
      stream.getTracks().forEach(track => track.stop())
      
      // Start new stream with selected device
      try {
        const newStream = await navigator.mediaDevices.getUserMedia({
          video: { deviceId },
          audio: { deviceId: selectedAudioDevice || undefined }
        })
        setStream(newStream)
        if (videoRef.current) {
          videoRef.current.srcObject = newStream
        }
        setupAudioLevelMonitoring(newStream)
      } catch (error) {
        console.error('Error changing video device:', error)
      }
    }
  }

  const changeAudioDevice = async (deviceId: string) => {
    setSelectedAudioDevice(deviceId)
    if (stream) {
      // Stop current stream
      stream.getTracks().forEach(track => track.stop())
      
      // Start new stream with selected device
      try {
        const newStream = await navigator.mediaDevices.getUserMedia({
          video: { deviceId: selectedVideoDevice || undefined },
          audio: { deviceId }
        })
        setStream(newStream)
        if (videoRef.current) {
          videoRef.current.srcObject = newStream
        }
        setupAudioLevelMonitoring(newStream)
      } catch (error) {
        console.error('Error changing audio device:', error)
      }
    }
  }

  const testAudio = () => {
    setIsTestingAudio(true)
    const utterance = new SpeechSynthesisUtterance("This is a test of your audio setup. Can you hear this message clearly?")
    utterance.onend = () => setIsTestingAudio(false)
    window.speechSynthesis.speak(utterance)
  }

  const completeSetup = () => {
    setSetupComplete(true)
    // Store stream for interview
    if (stream) {
      sessionStorage.setItem('interviewStream', 'ready')
    }
  }

  const proceedToInterview = () => {
    router.push(`/interview/video/${token}`)
  }

  const retrySetup = () => {
    setPermissionStatus('pending')
    setSetupComplete(false)
    if (stream) {
      stream.getTracks().forEach(track => track.stop())
      setStream(null)
    }
  }

  useEffect(() => {
    getMediaDevices()
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p>Loading setup...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-white mb-2">📹 Camera & Microphone Setup</h1>
          <p className="text-blue-100">Configure your devices for the best interview experience</p>
        </div>
      </div>

      <div className="max-w-4xl mx-auto p-6">
        <div className="grid md:grid-cols-2 gap-6">
          {/* Video Preview */}
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center justify-between">
                <span className="flex items-center">
                  <Camera className="h-5 w-5 mr-2 text-blue-500" />
                  Video Preview
                </span>
                <div className="flex items-center space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={toggleVideo}
                    className="border-gray-600"
                  >
                    {isVideoEnabled ? <Video className="h-4 w-4" /> : <VideoOff className="h-4 w-4" />}
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="aspect-video bg-gray-700 rounded-lg overflow-hidden mb-4 relative">
                {permissionStatus === 'granted' && stream ? (
                  <video
                    ref={videoRef}
                    autoPlay
                    playsInline
                    muted
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <div className="text-center">
                      <Camera className="h-16 w-16 text-gray-500 mx-auto mb-4" />
                      <p className="text-gray-400">
                        {permissionStatus === 'pending' ? 'Click "Enable Camera" to start' : 
                         permissionStatus === 'denied' ? 'Camera access denied' : 'Loading...'}
                      </p>
                    </div>
                  </div>
                )}
                
                {/* Status indicators */}
                <div className="absolute top-4 right-4 flex space-x-2">
                  {!isVideoEnabled && (
                    <div className="bg-red-600 text-white px-2 py-1 rounded text-xs">
                      Video Off
                    </div>
                  )}
                  {!isAudioEnabled && (
                    <div className="bg-red-600 text-white px-2 py-1 rounded text-xs">
                      Audio Off
                    </div>
                  )}
                </div>
              </div>

              {/* Device Selection */}
              {permissionStatus === 'granted' && (
                <div className="space-y-3">
                  <div>
                    <label className="text-sm text-gray-300 mb-2 block">Camera</label>
                    <Select value={selectedVideoDevice} onValueChange={changeVideoDevice}>
                      <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                        <SelectValue placeholder="Select camera" />
                      </SelectTrigger>
                      <SelectContent>
                        {videoDevices.map(device => (
                          <SelectItem key={device.deviceId} value={device.deviceId}>
                            {device.label || `Camera ${device.deviceId.slice(0, 8)}`}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm text-gray-300 mb-2 block">Microphone</label>
                    <Select value={selectedAudioDevice} onValueChange={changeAudioDevice}>
                      <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                        <SelectValue placeholder="Select microphone" />
                      </SelectTrigger>
                      <SelectContent>
                        {audioDevices.map(device => (
                          <SelectItem key={device.deviceId} value={device.deviceId}>
                            {device.label || `Microphone ${device.deviceId.slice(0, 8)}`}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Audio & Controls */}
          <div className="space-y-4">
            {/* Audio Test */}
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Mic className="h-5 w-5 mr-2 text-green-500" />
                  Audio Test
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Audio Level Indicator */}
                <div>
                  <div className="flex justify-between text-sm text-gray-300 mb-2">
                    <span>Microphone Level</span>
                    <span>{Math.round(audioLevel)}%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-3">
                    <div 
                      className={`h-3 rounded-full transition-all duration-100 ${
                        audioLevel > 50 ? 'bg-green-500' : 
                        audioLevel > 20 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${Math.min(audioLevel * 2, 100)}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-400 mt-1">
                    Speak normally to test your microphone
                  </p>
                </div>

                {/* Audio Controls */}
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={toggleAudio}
                    className="border-gray-600"
                  >
                    {isAudioEnabled ? <Mic className="h-4 w-4" /> : <MicOff className="h-4 w-4" />}
                  </Button>
                  
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={testAudio}
                    disabled={isTestingAudio}
                    className="border-gray-600"
                  >
                    {isTestingAudio ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                    Test Audio
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Setup Status */}
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Settings className="h-5 w-5 mr-2 text-blue-500" />
                  Setup Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Camera Access</span>
                    {permissionStatus === 'granted' ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : permissionStatus === 'denied' ? (
                      <AlertCircle className="h-5 w-5 text-red-500" />
                    ) : (
                      <div className="h-5 w-5 border-2 border-gray-500 rounded-full"></div>
                    )}
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Audio Working</span>
                    {audioLevel > 10 ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <div className="h-5 w-5 border-2 border-gray-500 rounded-full"></div>
                    )}
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Setup Complete</span>
                    {setupComplete ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <div className="h-5 w-5 border-2 border-gray-500 rounded-full"></div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <Card className="bg-gray-800 border-gray-700">
              <CardContent className="p-4">
                <div className="space-y-3">
                  {permissionStatus === 'pending' && (
                    <Button onClick={requestPermissions} className="w-full bg-blue-600 hover:bg-blue-700">
                      <Camera className="h-4 w-4 mr-2" />
                      Enable Camera & Microphone
                    </Button>
                  )}
                  
                  {permissionStatus === 'denied' && (
                    <Button onClick={retrySetup} className="w-full bg-orange-600 hover:bg-orange-700">
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Retry Setup
                    </Button>
                  )}
                  
                  {permissionStatus === 'granted' && !setupComplete && (
                    <Button onClick={completeSetup} className="w-full bg-green-600 hover:bg-green-700">
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Complete Setup
                    </Button>
                  )}
                  
                  {setupComplete && (
                    <Button onClick={proceedToInterview} className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                      <ArrowRight className="h-4 w-4 mr-2" />
                      Start Video Interview
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
