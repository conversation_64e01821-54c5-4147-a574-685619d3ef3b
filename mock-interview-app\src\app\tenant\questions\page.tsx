'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { MainNav } from '@/components/navigation/main-nav'
import { 
  Plus, 
  Search, 
  Filter,
  Edit,
  Trash2,
  Code,
  MessageSquare,
  Users
} from 'lucide-react'

interface Question {
  id: string
  title: string
  description: string
  type: 'CODING' | 'THEORY' | 'BEHAVIORAL'
  difficulty: string
  tags: string[]
  timeLimit?: number
  testCases?: any
  solution?: string
  createdAt: Date
}

export default function QuestionsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [questions, setQuestions] = useState<Question[]>([])
  const [filteredQuestions, setFilteredQuestions] = useState<Question[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState('ALL')
  const [filterDifficulty, setFilterDifficulty] = useState('ALL')
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [newQuestion, setNewQuestion] = useState({
    title: '',
    description: '',
    type: 'CODING',
    difficulty: 'EASY',
    tags: '',
    timeLimit: 15,
    testCases: '',
    solution: ''
  })

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/signin')
      return
    }

    if (session.user.role !== 'TENANT') {
      router.push('/')
      return
    }

    fetchQuestions()
  }, [session, status, router])

  useEffect(() => {
    filterQuestions()
  }, [questions, searchTerm, filterType, filterDifficulty])

  const fetchQuestions = async () => {
    try {
      // Mock data - replace with actual API call
      const mockQuestions: Question[] = [
        {
          id: '1',
          title: 'Two Sum Problem',
          description: 'Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target.',
          type: 'CODING',
          difficulty: 'EASY',
          tags: ['arrays', 'hash-table'],
          timeLimit: 15,
          testCases: JSON.stringify([
            { input: [2, 7, 11, 15], target: 9, output: [0, 1] },
            { input: [3, 2, 4], target: 6, output: [1, 2] }
          ]),
          solution: 'Use a hash map to store complements',
          createdAt: new Date('2024-06-01')
        },
        {
          id: '2',
          title: 'React Hooks Explanation',
          description: 'Explain the difference between useState and useEffect hooks in React. Provide examples.',
          type: 'THEORY',
          difficulty: 'MEDIUM',
          tags: ['react', 'hooks', 'frontend'],
          timeLimit: 10,
          createdAt: new Date('2024-06-02')
        },
        {
          id: '3',
          title: 'Team Conflict Resolution',
          description: 'Describe a situation where you had to resolve a conflict within your team. What was your approach?',
          type: 'BEHAVIORAL',
          difficulty: 'MEDIUM',
          tags: ['leadership', 'communication', 'teamwork'],
          timeLimit: 8,
          createdAt: new Date('2024-06-03')
        }
      ]
      setQuestions(mockQuestions)
    } catch (error) {
      console.error('Error fetching questions:', error)
    }
  }

  const filterQuestions = () => {
    let filtered = questions

    if (searchTerm) {
      filtered = filtered.filter(q => 
        q.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        q.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        q.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    }

    if (filterType !== 'ALL') {
      filtered = filtered.filter(q => q.type === filterType)
    }

    if (filterDifficulty !== 'ALL') {
      filtered = filtered.filter(q => q.difficulty === filterDifficulty)
    }

    setFilteredQuestions(filtered)
  }

  const handleAddQuestion = async () => {
    try {
      // Mock implementation - replace with actual API call
      const question: Question = {
        id: Date.now().toString(),
        title: newQuestion.title,
        description: newQuestion.description,
        type: newQuestion.type as 'CODING' | 'THEORY' | 'BEHAVIORAL',
        difficulty: newQuestion.difficulty,
        tags: newQuestion.tags.split(',').map(tag => tag.trim()),
        timeLimit: newQuestion.timeLimit,
        testCases: newQuestion.testCases ? JSON.parse(newQuestion.testCases) : undefined,
        solution: newQuestion.solution,
        createdAt: new Date()
      }

      setQuestions([...questions, question])
      setIsAddDialogOpen(false)
      setNewQuestion({
        title: '',
        description: '',
        type: 'CODING',
        difficulty: 'EASY',
        tags: '',
        timeLimit: 15,
        testCases: '',
        solution: ''
      })
    } catch (error) {
      console.error('Error adding question:', error)
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'CODING':
        return <Code className="h-4 w-4" />
      case 'THEORY':
        return <MessageSquare className="h-4 w-4" />
      case 'BEHAVIORAL':
        return <Users className="h-4 w-4" />
      default:
        return <MessageSquare className="h-4 w-4" />
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY':
        return 'bg-green-100 text-green-800'
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800'
      case 'HARD':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (status === 'loading') {
    return <div>Loading...</div>
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <MainNav />
      
      <div className="py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="mb-8 flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Question Bank</h1>
              <p className="text-gray-600 mt-2">
                Manage your interview questions and create new ones
              </p>
            </div>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Question
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Add New Question</DialogTitle>
                  <DialogDescription>
                    Create a new interview question for your question bank
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="title">Title</Label>
                    <Input
                      id="title"
                      value={newQuestion.title}
                      onChange={(e) => setNewQuestion({...newQuestion, title: e.target.value})}
                      placeholder="Question title"
                    />
                  </div>
                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={newQuestion.description}
                      onChange={(e) => setNewQuestion({...newQuestion, description: e.target.value})}
                      placeholder="Question description and requirements"
                      rows={4}
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="type">Type</Label>
                      <Select value={newQuestion.type} onValueChange={(value) => setNewQuestion({...newQuestion, type: value})}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="CODING">Coding</SelectItem>
                          <SelectItem value="THEORY">Theory</SelectItem>
                          <SelectItem value="BEHAVIORAL">Behavioral</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="difficulty">Difficulty</Label>
                      <Select value={newQuestion.difficulty} onValueChange={(value) => setNewQuestion({...newQuestion, difficulty: value})}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="EASY">Easy</SelectItem>
                          <SelectItem value="MEDIUM">Medium</SelectItem>
                          <SelectItem value="HARD">Hard</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="tags">Tags (comma separated)</Label>
                      <Input
                        id="tags"
                        value={newQuestion.tags}
                        onChange={(e) => setNewQuestion({...newQuestion, tags: e.target.value})}
                        placeholder="react, javascript, arrays"
                      />
                    </div>
                    <div>
                      <Label htmlFor="timeLimit">Time Limit (minutes)</Label>
                      <Input
                        id="timeLimit"
                        type="number"
                        value={newQuestion.timeLimit}
                        onChange={(e) => setNewQuestion({...newQuestion, timeLimit: parseInt(e.target.value)})}
                      />
                    </div>
                  </div>
                  {newQuestion.type === 'CODING' && (
                    <>
                      <div>
                        <Label htmlFor="testCases">Test Cases (JSON format)</Label>
                        <Textarea
                          id="testCases"
                          value={newQuestion.testCases}
                          onChange={(e) => setNewQuestion({...newQuestion, testCases: e.target.value})}
                          placeholder='[{"input": [2,7,11,15], "target": 9, "output": [0,1]}]'
                          rows={3}
                        />
                      </div>
                      <div>
                        <Label htmlFor="solution">Expected Solution</Label>
                        <Textarea
                          id="solution"
                          value={newQuestion.solution}
                          onChange={(e) => setNewQuestion({...newQuestion, solution: e.target.value})}
                          placeholder="Brief description of the expected solution approach"
                          rows={3}
                        />
                      </div>
                    </>
                  )}
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleAddQuestion}>
                      Add Question
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          {/* Filters */}
          <Card className="mb-6">
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search questions..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Select value={filterType} onValueChange={setFilterType}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ALL">All Types</SelectItem>
                      <SelectItem value="CODING">Coding</SelectItem>
                      <SelectItem value="THEORY">Theory</SelectItem>
                      <SelectItem value="BEHAVIORAL">Behavioral</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={filterDifficulty} onValueChange={setFilterDifficulty}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ALL">All Levels</SelectItem>
                      <SelectItem value="EASY">Easy</SelectItem>
                      <SelectItem value="MEDIUM">Medium</SelectItem>
                      <SelectItem value="HARD">Hard</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Questions List */}
          <div className="grid gap-6">
            {filteredQuestions.map((question) => (
              <Card key={question.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div className="flex items-center space-x-3">
                      {getTypeIcon(question.type)}
                      <div>
                        <CardTitle className="text-lg">{question.title}</CardTitle>
                        <CardDescription className="mt-1">
                          {question.description.substring(0, 150)}...
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getDifficultyColor(question.difficulty)}`}>
                        {question.difficulty}
                      </span>
                      <span className="text-sm text-gray-600">
                        {question.timeLimit} min
                      </span>
                      <div className="flex space-x-1">
                        {question.tags.map((tag, index) => (
                          <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                    <span className="text-sm text-gray-500">
                      Created {question.createdAt.toLocaleDateString()}
                    </span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredQuestions.length === 0 && (
            <Card>
              <CardContent className="p-12 text-center">
                <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No questions found</h3>
                <p className="text-gray-600 mb-4">
                  {searchTerm || filterType !== 'ALL' || filterDifficulty !== 'ALL'
                    ? 'Try adjusting your search or filters'
                    : 'Get started by adding your first question'
                  }
                </p>
                <Button onClick={() => setIsAddDialogOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Question
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
