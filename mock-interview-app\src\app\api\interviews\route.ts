import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { EmailService } from '@/lib/email-service'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const role = session.user.role
    const userId = session.user.id

    let interviews

    if (role === 'STUDENT') {
      interviews = await prisma.interview.findMany({
        where: { studentId: userId },
        include: {
          interviewer: {
            select: { name: true, email: true }
          },
          tenant: {
            select: { name: true }
          },
          questions: {
            include: {
              question: true
            }
          }
        },
        orderBy: { scheduledAt: 'desc' }
      })
    } else if (role === 'TENANT') {
      interviews = await prisma.interview.findMany({
        where: { interviewerId: userId },
        include: {
          student: {
            select: { name: true, email: true }
          },
          tenant: {
            select: { name: true }
          },
          questions: {
            include: {
              question: true
            }
          }
        },
        orderBy: { scheduledAt: 'desc' }
      })
    } else if (role === 'ADMIN') {
      interviews = await prisma.interview.findMany({
        include: {
          student: {
            select: { name: true, email: true }
          },
          interviewer: {
            select: { name: true, email: true }
          },
          tenant: {
            select: { name: true }
          }
        },
        orderBy: { scheduledAt: 'desc' }
      })
    } else {
      return NextResponse.json(
        { message: 'Forbidden' },
        { status: 403 }
      )
    }

    return NextResponse.json({ interviews })
  } catch (error) {
    console.error('Error fetching interviews:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      )
    }

    if (session.user.role !== 'TENANT' && session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Forbidden' },
        { status: 403 }
      )
    }

    const {
      title,
      description,
      studentId,
      scheduledAt,
      duration,
      questionIds,
      tenantId
    } = await request.json()

    // Validate required fields
    if (!title || !studentId || !scheduledAt || !duration) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Create interview
    const interview = await prisma.interview.create({
      data: {
        title,
        description,
        studentId,
        interviewerId: session.user.id,
        tenantId: tenantId || session.user.tenantId,
        scheduledAt: new Date(scheduledAt),
        duration,
        meetingLink: `${process.env.NEXTAUTH_URL}/interview/${Date.now()}`, // Generate unique meeting link
      },
      include: {
        student: true,
        interviewer: true,
        tenant: true
      }
    })

    // Add questions to interview if provided
    if (questionIds && questionIds.length > 0) {
      await Promise.all(
        questionIds.map((questionId: string, index: number) =>
          prisma.interviewQuestion.create({
            data: {
              interviewId: interview.id,
              questionId,
              order: index + 1
            }
          })
        )
      )
    }

    // Send invitation email to student
    await EmailService.sendInterviewInvitation(
      interview.student.email,
      interview.student.name || 'Student',
      {
        title: interview.title,
        scheduledAt: interview.scheduledAt,
        duration: interview.duration,
        meetingLink: interview.meetingLink || ''
      }
    )

    return NextResponse.json(
      { 
        message: 'Interview scheduled successfully',
        interview
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('Error creating interview:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { interviewId, status, notes, recordingUrl } = await request.json()

    if (!interviewId) {
      return NextResponse.json(
        { message: 'Interview ID is required' },
        { status: 400 }
      )
    }

    // Check if user has permission to update this interview
    const interview = await prisma.interview.findUnique({
      where: { id: interviewId },
      include: { student: true, interviewer: true }
    })

    if (!interview) {
      return NextResponse.json(
        { message: 'Interview not found' },
        { status: 404 }
      )
    }

    if (
      session.user.role !== 'ADMIN' &&
      interview.studentId !== session.user.id &&
      interview.interviewerId !== session.user.id
    ) {
      return NextResponse.json(
        { message: 'Forbidden' },
        { status: 403 }
      )
    }

    // Update interview
    const updatedInterview = await prisma.interview.update({
      where: { id: interviewId },
      data: {
        ...(status && { status }),
        ...(notes && { notes }),
        ...(recordingUrl && { recordingUrl })
      },
      include: {
        student: true,
        interviewer: true,
        tenant: true
      }
    })

    return NextResponse.json({
      message: 'Interview updated successfully',
      interview: updatedInterview
    })
  } catch (error) {
    console.error('Error updating interview:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}
