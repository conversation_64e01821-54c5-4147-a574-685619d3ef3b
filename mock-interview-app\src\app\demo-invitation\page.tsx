'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { EmailService } from '@/lib/email-service'
import { ExternalLink, Mail, User, Calendar, Clock } from 'lucide-react'

export default function DemoInvitationPage() {
  const [studentName, setStudentName] = useState('<PERSON> Doe')
  const [email, setEmail] = useState('<EMAIL>')
  const [interviewTitle, setInterviewTitle] = useState('Frontend Developer Position')
  const [generatedToken, setGeneratedToken] = useState('')
  const [invitationUrl, setInvitationUrl] = useState('')

  const generateInvitation = () => {
    const scheduledAt = new Date(Date.now() + 24 * 60 * 60 * 1000) // Tomorrow
    const invitation = EmailService.createInvitation(email, studentName, interviewTitle, scheduledAt)
    
    setGeneratedToken(invitation.token)
    setInvitationUrl(`${window.location.origin}/interview/invite/${invitation.token}`)
    
    console.log('Generated invitation:', invitation)
  }

  const openInvitation = () => {
    if (invitationUrl) {
      window.open(invitationUrl, '_blank')
    }
  }

  return (
    <div className="min-h-screen bg-gray-900 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">🎯 AI Interview Demo</h1>
          <p className="text-gray-400 text-lg">
            Generate a demo interview invitation to test the complete AI-powered video interview system
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          {/* Invitation Generator */}
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Mail className="h-5 w-5 mr-2 text-blue-500" />
                Generate Demo Invitation
              </CardTitle>
              <CardDescription className="text-gray-400">
                Create a test invitation to experience the full interview flow
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="studentName" className="text-gray-300">Student Name</Label>
                <Input
                  id="studentName"
                  value={studentName}
                  onChange={(e) => setStudentName(e.target.value)}
                  className="bg-gray-700 border-gray-600 text-white"
                  placeholder="Enter student name"
                />
              </div>

              <div>
                <Label htmlFor="email" className="text-gray-300">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="bg-gray-700 border-gray-600 text-white"
                  placeholder="Enter email address"
                />
              </div>

              <div>
                <Label htmlFor="interviewTitle" className="text-gray-300">Interview Position</Label>
                <Input
                  id="interviewTitle"
                  value={interviewTitle}
                  onChange={(e) => setInterviewTitle(e.target.value)}
                  className="bg-gray-700 border-gray-600 text-white"
                  placeholder="Enter position title"
                />
              </div>

              <Button onClick={generateInvitation} className="w-full bg-blue-600 hover:bg-blue-700">
                <Mail className="h-4 w-4 mr-2" />
                Generate Invitation
              </Button>

              {invitationUrl && (
                <div className="mt-4 p-4 bg-green-900 border border-green-700 rounded-lg">
                  <p className="text-green-100 text-sm mb-2">✅ Invitation generated successfully!</p>
                  <Button onClick={openInvitation} variant="outline" className="w-full border-green-600 text-green-400">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Open Interview Invitation
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Demo Flow Overview */}
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader>
              <CardTitle className="text-white">🚀 Demo Flow Overview</CardTitle>
              <CardDescription className="text-gray-400">
                What you'll experience in this demo
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-xs font-bold">1</div>
                  <div>
                    <h4 className="text-white font-medium">Email Invitation</h4>
                    <p className="text-gray-400 text-sm">Click the generated link to start the interview process</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-xs font-bold">2</div>
                  <div>
                    <h4 className="text-white font-medium">Authentication</h4>
                    <p className="text-gray-400 text-sm">Sign up or log in with demo credentials</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-xs font-bold">3</div>
                  <div>
                    <h4 className="text-white font-medium">AI Briefing</h4>
                    <p className="text-gray-400 text-sm">Listen to voice instructions and interview overview</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-xs font-bold">4</div>
                  <div>
                    <h4 className="text-white font-medium">Camera Setup</h4>
                    <p className="text-gray-400 text-sm">Configure camera and microphone settings</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-xs font-bold">5</div>
                  <div>
                    <h4 className="text-white font-medium">AI Video Interview</h4>
                    <p className="text-gray-400 text-sm">Interactive interview with speaking AI interviewer</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-xs font-bold">6</div>
                  <div>
                    <h4 className="text-white font-medium">Recording & Upload</h4>
                    <p className="text-gray-400 text-sm">Automatic video recording and cloud upload</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center text-white text-xs font-bold">7</div>
                  <div>
                    <h4 className="text-white font-medium">Results & Feedback</h4>
                    <p className="text-gray-400 text-sm">AI-powered analysis and congratulations</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Demo Credentials */}
        <Card className="bg-gray-800 border-gray-700 mt-6">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <User className="h-5 w-5 mr-2 text-green-500" />
              Demo Credentials
            </CardTitle>
            <CardDescription className="text-gray-400">
              Use these credentials during the authentication step
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-4">
              <div className="bg-gray-700 p-4 rounded-lg">
                <h4 className="text-white font-medium mb-2">For Login (Existing User)</h4>
                <div className="space-y-1 text-sm">
                  <p className="text-gray-300"><strong>Email:</strong> <EMAIL></p>
                  <p className="text-gray-300"><strong>Password:</strong> demo123</p>
                </div>
              </div>
              
              <div className="bg-gray-700 p-4 rounded-lg">
                <h4 className="text-white font-medium mb-2">For Signup (New User)</h4>
                <div className="space-y-1 text-sm">
                  <p className="text-gray-300">Use any email and password</p>
                  <p className="text-gray-300">The system will create a demo account</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Additional Demo Links */}
        <Card className="bg-gray-800 border-gray-700 mt-6">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <Mail className="h-5 w-5 mr-2 text-green-500" />
              Email Integration Demo
            </CardTitle>
            <CardDescription className="text-gray-400">
              Test the complete email functionality with real email sending
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <Button
                onClick={() => window.open('/mailinator-demo', '_blank')}
                className="w-full bg-green-600 hover:bg-green-700"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Open Mailinator Email Demo
              </Button>

              <Button
                onClick={() => window.open('/openai-demo', '_blank')}
                className="w-full bg-purple-600 hover:bg-purple-700"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Open OpenAI Integration Demo
              </Button>
            </div>
            <p className="text-gray-400 text-sm mt-2">
              Test email notifications and AI-powered interview evaluation features
            </p>
          </CardContent>
        </Card>

        {/* Features Highlight */}
        <Card className="bg-gradient-to-r from-blue-900 to-purple-900 border-blue-700 mt-6">
          <CardHeader>
            <CardTitle className="text-white">✨ Key Features Demonstrated</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-2">
                  <User className="h-6 w-6 text-white" />
                </div>
                <h4 className="text-white font-medium">AI Interviewer</h4>
                <p className="text-blue-200 text-sm">Animated avatar with speech synthesis</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-2">
                  <Calendar className="h-6 w-6 text-white" />
                </div>
                <h4 className="text-white font-medium">Video Recording</h4>
                <p className="text-blue-200 text-sm">Real-time recording with upload</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-2">
                  <Clock className="h-6 w-6 text-white" />
                </div>
                <h4 className="text-white font-medium">Smart Analysis</h4>
                <p className="text-blue-200 text-sm">AI-powered feedback and scoring</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
