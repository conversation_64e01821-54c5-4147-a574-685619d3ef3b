import nodemailer from 'nodemailer'
import { prisma } from './prisma'
import crypto from 'crypto'

export interface InterviewInvitation {
  id: string
  email: string
  studentName: string
  interviewTitle: string
  scheduledAt: Date
  token: string
  status: 'PENDING' | 'ACCEPTED' | 'COMPLETED' | 'EXPIRED'
  createdAt: Date
  expiresAt: Date
}

// In-memory storage for demo (replace with database in production)
const invitations: Map<string, InterviewInvitation> = new Map()

export interface EmailOptions {
  to: string
  subject: string
  html: string
  attachments?: Array<{
    filename: string
    path: string
  }>
}

export class EmailService {
  private static transporter = nodemailer.createTransport({
    host: process.env.EMAIL_HOST,
    port: parseInt(process.env.EMAIL_PORT || '587'),
    secure: false,
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
  })

  // Invitation Token Management
  static generateInvitationToken(): string {
    return crypto.randomBytes(32).toString('hex')
  }

  static createInvitation(
    email: string,
    studentName: string,
    interviewTitle: string,
    scheduledAt: Date
  ): InterviewInvitation {
    const token = this.generateInvitationToken()
    const id = crypto.randomUUID()
    const expiresAt = new Date(scheduledAt.getTime() + 24 * 60 * 60 * 1000) // 24 hours after scheduled time

    const invitation: InterviewInvitation = {
      id,
      email,
      studentName,
      interviewTitle,
      scheduledAt,
      token,
      status: 'PENDING',
      createdAt: new Date(),
      expiresAt
    }

    invitations.set(token, invitation)
    return invitation
  }

  static getInvitationByToken(token: string): InterviewInvitation | null {
    const invitation = invitations.get(token)
    if (!invitation) return null

    // Check if expired
    if (new Date() > invitation.expiresAt) {
      invitation.status = 'EXPIRED'
    }

    return invitation
  }

  static updateInvitationStatus(token: string, status: InterviewInvitation['status']): boolean {
    const invitation = invitations.get(token)
    if (!invitation) return false

    invitation.status = status
    return true
  }

  static generateInvitationLink(token: string, baseUrl: string = 'http://localhost:3000'): string {
    return `${baseUrl}/interview/invite/${token}`
  }

  static async sendEmail(options: EmailOptions): Promise<boolean> {
    try {
      // Skip email sending if no email configuration
      if (!process.env.EMAIL_USER || !process.env.EMAIL_PASS) {
        console.log('Email service not configured, skipping email send')
        return true
      }

      const info = await this.transporter.sendMail({
        from: process.env.EMAIL_USER,
        to: options.to,
        subject: options.subject,
        html: options.html,
        attachments: options.attachments,
      })

      // Log email (skip if database not available)
      try {
        await prisma.emailLog.create({
          data: {
            to: options.to,
            subject: options.subject,
            body: options.html,
            status: 'SENT',
            sentAt: new Date(),
          },
        })
      } catch (dbError) {
        console.log('Database not available for email logging')
      }

      return true
    } catch (error) {
      console.error('Error sending email:', error)

      // Log failed email (skip if database not available)
      try {
        await prisma.emailLog.create({
          data: {
            to: options.to,
            subject: options.subject,
            body: options.html,
            status: 'FAILED',
          },
        })
      } catch (dbError) {
        console.log('Database not available for email logging')
      }

      return false
    }
  }

  static async sendInterviewInvitationWithToken(invitation: InterviewInvitation): Promise<boolean> {
    const invitationLink = this.generateInvitationLink(invitation.token)

    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0;">🎯 Video Interview Invitation</h1>
        </div>

        <div style="padding: 30px; background: #f8f9fa;">
          <h2 style="color: #333;">Hello ${invitation.studentName}!</h2>

          <p style="font-size: 16px; line-height: 1.6; color: #555;">
            You've been invited to participate in an AI-powered video interview for:
          </p>

          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea;">
            <h3 style="margin: 0 0 10px 0; color: #333;">${invitation.interviewTitle}</h3>
            <p style="margin: 0; color: #666;">
              📅 Scheduled: ${invitation.scheduledAt.toLocaleDateString()} at ${invitation.scheduledAt.toLocaleTimeString()}
            </p>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${invitationLink}"
               style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
              🚀 Join Video Interview
            </a>
          </div>

          <div style="background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h4 style="margin: 0 0 10px 0; color: #1976d2;">📋 What to Expect:</h4>
            <ul style="margin: 0; padding-left: 20px; color: #555;">
              <li>🤖 AI-powered interviewer with voice interaction</li>
              <li>📹 Video recording for review and feedback</li>
              <li>💻 Interactive coding and behavioral questions</li>
              <li>📊 Real-time evaluation and scoring</li>
              <li>🎉 Instant results and congratulations</li>
            </ul>
          </div>

          <div style="background: #fff3e0; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h4 style="margin: 0 0 10px 0; color: #f57c00;">⚠️ Technical Requirements:</h4>
            <ul style="margin: 0; padding-left: 20px; color: #555;">
              <li>🌐 Stable internet connection</li>
              <li>📷 Working camera and microphone</li>
              <li>🌍 Modern web browser (Chrome recommended)</li>
              <li>🔇 Quiet environment for recording</li>
              <li>💾 Allow browser permissions for camera/mic</li>
            </ul>
          </div>

          <p style="font-size: 14px; color: #777; margin-top: 30px;">
            This invitation expires on ${invitation.expiresAt.toLocaleDateString()}.
            If you have any questions, please contact our support team.
          </p>
        </div>

        <div style="background: #333; padding: 20px; text-align: center;">
          <p style="color: #ccc; margin: 0; font-size: 14px;">
            © 2024 AI Interview Platform. All rights reserved.
          </p>
        </div>
      </div>
    `

    return this.sendEmail({
      to: invitation.email,
      subject: `🎯 Video Interview Invitation - ${invitation.interviewTitle}`,
      html,
    })
  }

  static async sendInterviewInvitation(
    studentEmail: string,
    studentName: string,
    interviewDetails: {
      title: string
      scheduledAt: Date
      duration: number
      meetingLink: string
    }
  ): Promise<boolean> {
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Interview Invitation</h2>
        <p>Dear ${studentName},</p>
        <p>You have been invited to participate in an interview:</p>
        
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">${interviewDetails.title}</h3>
          <p><strong>Date & Time:</strong> ${interviewDetails.scheduledAt.toLocaleString()}</p>
          <p><strong>Duration:</strong> ${interviewDetails.duration} minutes</p>
          <p><strong>Meeting Link:</strong> <a href="${interviewDetails.meetingLink}">${interviewDetails.meetingLink}</a></p>
        </div>
        
        <p>Please make sure to:</p>
        <ul>
          <li>Test your camera and microphone before the interview</li>
          <li>Ensure you have a stable internet connection</li>
          <li>Prepare for coding and theory questions</li>
          <li>Join the meeting 5 minutes early</li>
        </ul>
        
        <p>Good luck with your interview!</p>
        <p>Best regards,<br>Mock Interview Team</p>
      </div>
    `

    return this.sendEmail({
      to: studentEmail,
      subject: `Interview Invitation - ${interviewDetails.title}`,
      html,
    })
  }

  static async sendInterviewResults(
    studentEmail: string,
    studentName: string,
    results: {
      score: number
      feedback: string
      recordingUrl?: string
    }
  ): Promise<boolean> {
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Interview Results</h2>
        <p>Dear ${studentName},</p>
        <p>Your interview has been completed. Here are your results:</p>
        
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Overall Score: ${results.score}/100</h3>
          <h4>Feedback:</h4>
          <p>${results.feedback}</p>
          ${results.recordingUrl ? `<p><strong>Recording:</strong> <a href="${results.recordingUrl}">View Recording</a></p>` : ''}
        </div>
        
        <p>Keep practicing and improving your skills!</p>
        <p>Best regards,<br>Mock Interview Team</p>
      </div>
    `

    return this.sendEmail({
      to: studentEmail,
      subject: 'Your Interview Results',
      html,
    })
  }

  static async sendWelcomeEmail(
    userEmail: string,
    userName: string,
    role: string
  ): Promise<boolean> {
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Welcome to Mock Interview Platform!</h2>
        <p>Dear ${userName},</p>
        <p>Welcome to our Mock Interview Platform! Your account has been created successfully.</p>
        
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Account Details</h3>
          <p><strong>Email:</strong> ${userEmail}</p>
          <p><strong>Role:</strong> ${role}</p>
        </div>
        
        <p>You can now log in and start using the platform.</p>
        <p>Best regards,<br>Mock Interview Team</p>
      </div>
    `

    return this.sendEmail({
      to: userEmail,
      subject: 'Welcome to Mock Interview Platform',
      html,
    })
  }

  // Demo function to create sample invitations
  static createDemoInvitations(): InterviewInvitation[] {
    const demoInvitations = [
      {
        email: '<EMAIL>',
        studentName: 'John Doe',
        interviewTitle: 'Frontend Developer Position',
        scheduledAt: new Date(Date.now() + 2 * 60 * 60 * 1000) // 2 hours from now
      },
      {
        email: '<EMAIL>',
        studentName: 'Jane Smith',
        interviewTitle: 'Full Stack Developer Role',
        scheduledAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 1 day from now
      },
      {
        email: '<EMAIL>',
        studentName: 'Alex Johnson',
        interviewTitle: 'React Developer Interview',
        scheduledAt: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3 days from now
      }
    ]

    return demoInvitations.map(demo =>
      this.createInvitation(demo.email, demo.studentName, demo.interviewTitle, demo.scheduledAt)
    )
  }
}

// Initialize demo invitations
if (typeof window === 'undefined') {
  EmailService.createDemoInvitations()
}
