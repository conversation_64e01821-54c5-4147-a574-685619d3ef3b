import nodemailer from 'nodemailer'
import { prisma } from './prisma'

export interface EmailOptions {
  to: string
  subject: string
  html: string
  attachments?: Array<{
    filename: string
    path: string
  }>
}

export class EmailService {
  private static transporter = nodemailer.createTransport({
    host: process.env.EMAIL_HOST,
    port: parseInt(process.env.EMAIL_PORT || '587'),
    secure: false,
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
  })

  static async sendEmail(options: EmailOptions): Promise<boolean> {
    try {
      // Skip email sending if no email configuration
      if (!process.env.EMAIL_USER || !process.env.EMAIL_PASS) {
        console.log('Email service not configured, skipping email send')
        return true
      }

      const info = await this.transporter.sendMail({
        from: process.env.EMAIL_USER,
        to: options.to,
        subject: options.subject,
        html: options.html,
        attachments: options.attachments,
      })

      // Log email (skip if database not available)
      try {
        await prisma.emailLog.create({
          data: {
            to: options.to,
            subject: options.subject,
            body: options.html,
            status: 'SENT',
            sentAt: new Date(),
          },
        })
      } catch (dbError) {
        console.log('Database not available for email logging')
      }

      return true
    } catch (error) {
      console.error('Error sending email:', error)

      // Log failed email (skip if database not available)
      try {
        await prisma.emailLog.create({
          data: {
            to: options.to,
            subject: options.subject,
            body: options.html,
            status: 'FAILED',
          },
        })
      } catch (dbError) {
        console.log('Database not available for email logging')
      }

      return false
    }
  }

  static async sendInterviewInvitation(
    studentEmail: string,
    studentName: string,
    interviewDetails: {
      title: string
      scheduledAt: Date
      duration: number
      meetingLink: string
    }
  ): Promise<boolean> {
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Interview Invitation</h2>
        <p>Dear ${studentName},</p>
        <p>You have been invited to participate in an interview:</p>
        
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">${interviewDetails.title}</h3>
          <p><strong>Date & Time:</strong> ${interviewDetails.scheduledAt.toLocaleString()}</p>
          <p><strong>Duration:</strong> ${interviewDetails.duration} minutes</p>
          <p><strong>Meeting Link:</strong> <a href="${interviewDetails.meetingLink}">${interviewDetails.meetingLink}</a></p>
        </div>
        
        <p>Please make sure to:</p>
        <ul>
          <li>Test your camera and microphone before the interview</li>
          <li>Ensure you have a stable internet connection</li>
          <li>Prepare for coding and theory questions</li>
          <li>Join the meeting 5 minutes early</li>
        </ul>
        
        <p>Good luck with your interview!</p>
        <p>Best regards,<br>Mock Interview Team</p>
      </div>
    `

    return this.sendEmail({
      to: studentEmail,
      subject: `Interview Invitation - ${interviewDetails.title}`,
      html,
    })
  }

  static async sendInterviewResults(
    studentEmail: string,
    studentName: string,
    results: {
      score: number
      feedback: string
      recordingUrl?: string
    }
  ): Promise<boolean> {
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Interview Results</h2>
        <p>Dear ${studentName},</p>
        <p>Your interview has been completed. Here are your results:</p>
        
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Overall Score: ${results.score}/100</h3>
          <h4>Feedback:</h4>
          <p>${results.feedback}</p>
          ${results.recordingUrl ? `<p><strong>Recording:</strong> <a href="${results.recordingUrl}">View Recording</a></p>` : ''}
        </div>
        
        <p>Keep practicing and improving your skills!</p>
        <p>Best regards,<br>Mock Interview Team</p>
      </div>
    `

    return this.sendEmail({
      to: studentEmail,
      subject: 'Your Interview Results',
      html,
    })
  }

  static async sendWelcomeEmail(
    userEmail: string,
    userName: string,
    role: string
  ): Promise<boolean> {
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Welcome to Mock Interview Platform!</h2>
        <p>Dear ${userName},</p>
        <p>Welcome to our Mock Interview Platform! Your account has been created successfully.</p>
        
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Account Details</h3>
          <p><strong>Email:</strong> ${userEmail}</p>
          <p><strong>Role:</strong> ${role}</p>
        </div>
        
        <p>You can now log in and start using the platform.</p>
        <p>Best regards,<br>Mock Interview Team</p>
      </div>
    `

    return this.sendEmail({
      to: userEmail,
      subject: 'Welcome to Mock Interview Platform',
      html,
    })
  }
}
