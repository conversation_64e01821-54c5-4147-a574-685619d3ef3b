import { NextRequest, NextResponse } from 'next/server'
import { OpenAIService, InterviewResponse } from '@/lib/openai-service'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { responses } = body

    // Validate input
    if (!responses || !Array.isArray(responses)) {
      return NextResponse.json(
        { error: 'Invalid responses data' },
        { status: 400 }
      )
    }

    // Check if OpenAI is configured
    if (!OpenAIService.isConfigured()) {
      console.log('OpenAI not configured, using mock evaluation')
      
      // Mock AI evaluation for demo
      const evaluation = {
        overallScore: 85,
        technicalScore: 80,
        communicationScore: 90,
        problemSolvingScore: 85,
        strengths: [
          "Clear communication and articulation",
          "Good problem-solving approach",
          "Relevant technical knowledge"
        ],
        improvements: [
          "Provide more specific examples",
          "Demonstrate deeper technical understanding",
          "Show more confidence in responses"
        ],
        detailedFeedback: "The candidate demonstrated strong communication skills and a solid understanding of the technical concepts discussed. The responses were well-structured and showed good problem-solving thinking. To improve, the candidate should focus on providing more specific examples from their experience and demonstrating deeper technical knowledge in certain areas.",
        nextSteps: [
          "Practice explaining technical concepts with specific examples",
          "Review advanced topics in your field",
          "Work on building confidence in your responses"
        ]
      }

      return NextResponse.json({
        success: true,
        evaluation,
        source: 'mock'
      })
    }

    // Use real OpenAI evaluation
    const evaluation = await OpenAIService.evaluateInterview(responses)

    return NextResponse.json({
      success: true,
      evaluation,
      source: 'openai'
    })

  } catch (error) {
    console.error('Error evaluating interview:', error)
    return NextResponse.json(
      { error: 'Failed to evaluate interview' },
      { status: 500 }
    )
  }
}
