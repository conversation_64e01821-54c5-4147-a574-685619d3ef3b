{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/lib/mailinator-service.ts"], "sourcesContent": ["export interface MailinatorConfig {\n  apiToken?: string\n  privateDomain?: string\n  usePublicDomain: boolean\n  webhookToken?: string\n}\n\nexport interface MailinatorMessage {\n  id: string\n  from: string\n  to: string\n  subject: string\n  text?: string\n  html?: string\n  timestamp: number\n}\n\nexport interface MailinatorInbox {\n  name: string\n  messages: MailinatorMessage[]\n}\n\nexport class MailinatorService {\n  private static config: MailinatorConfig = {\n    usePublicDomain: !process.env.MAILINATOR_API_TOKEN, // Use public if no API token\n    privateDomain: process.env.MAILINATOR_PRIVATE_DOMAIN,\n    apiToken: process.env.MAILINATOR_API_TOKEN,\n    webhookToken: process.env.MAILINATOR_WEBHOOK_TOKEN\n  }\n\n  static setConfig(config: Partial<MailinatorConfig>) {\n    this.config = { ...this.config, ...config }\n  }\n\n  static getConfig(): MailinatorConfig {\n    return { ...this.config }\n  }\n\n  // Send email via Mailinator API\n  static async sendEmail(\n    to: string,\n    subject: string,\n    html: string,\n    text?: string\n  ): Promise<boolean> {\n    try {\n      if (this.config.usePublicDomain) {\n        return await this.sendToPublicDomain(to, subject, html, text)\n      } else if (this.config.privateDomain && this.config.apiToken) {\n        return await this.sendToPrivateDomain(to, subject, html, text)\n      } else {\n        console.log('📧 Mailinator Demo Mode - Email would be sent:')\n        console.log(`To: ${to}`)\n        console.log(`Subject: ${subject}`)\n        console.log(`HTML: ${html.substring(0, 200)}...`)\n        return true\n      }\n    } catch (error) {\n      console.error('❌ Failed to send email via Mailinator:', error)\n      return false\n    }\n  }\n\n  // Send to Mailinator public domain (free tier)\n  private static async sendToPublicDomain(\n    to: string,\n    subject: string,\n    html: string,\n    text?: string\n  ): Promise<boolean> {\n    try {\n      // Extract inbox name from email\n      const inboxName = this.extractInboxName(to)\n      \n      // Use Mailinator's webhook endpoint for public domain\n      const webhookUrl = `https://www.mailinator.com/api/v2/domains/public/webhook/${inboxName}/`\n      \n      const payload = {\n        from: '<EMAIL>',\n        subject: subject,\n        text: text || this.htmlToText(html),\n        html: html,\n        to: inboxName\n      }\n\n      const response = await fetch(webhookUrl, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(payload)\n      })\n\n      if (response.ok) {\n        console.log(`✅ Email sent to Mailinator public inbox: ${inboxName}@mailinator.com`)\n        console.log(`📬 Check inbox at: https://www.mailinator.com/v4/public/inboxes.jsp?to=${inboxName}`)\n        return true\n      } else {\n        const errorText = await response.text()\n        console.error('❌ Failed to send to Mailinator public domain:', response.status, errorText)\n        return false\n      }\n    } catch (error) {\n      console.error('❌ Error sending to Mailinator public domain:', error)\n      return false\n    }\n  }\n\n  // Send to Mailinator private domain (requires API token)\n  private static async sendToPrivateDomain(\n    to: string,\n    subject: string,\n    html: string,\n    text?: string\n  ): Promise<boolean> {\n    try {\n      const inboxName = this.extractInboxName(to)\n      \n      const apiUrl = `https://api.mailinator.com/api/v2/domains/private/inboxes/${inboxName}/messages`\n      \n      const payload = {\n        from: '<EMAIL>',\n        subject: subject,\n        text: text || this.htmlToText(html),\n        html: html\n      }\n\n      const response = await fetch(apiUrl, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': this.config.apiToken!\n        },\n        body: JSON.stringify(payload)\n      })\n\n      if (response.ok) {\n        console.log(`✅ Email sent to Mailinator private domain: ${to}`)\n        return true\n      } else {\n        const errorText = await response.text()\n        console.error('❌ Failed to send to Mailinator private domain:', response.status, errorText)\n        return false\n      }\n    } catch (error) {\n      console.error('❌ Error sending to Mailinator private domain:', error)\n      return false\n    }\n  }\n\n  // Fetch messages from Mailinator inbox\n  static async getInboxMessages(inboxName: string): Promise<MailinatorMessage[]> {\n    try {\n      if (this.config.usePublicDomain) {\n        return await this.getPublicInboxMessages(inboxName)\n      } else if (this.config.privateDomain && this.config.apiToken) {\n        return await this.getPrivateInboxMessages(inboxName)\n      } else {\n        console.log('📧 Mailinator not configured for message retrieval')\n        return []\n      }\n    } catch (error) {\n      console.error('❌ Failed to fetch inbox messages:', error)\n      return []\n    }\n  }\n\n  // Get messages from public domain\n  private static async getPublicInboxMessages(inboxName: string): Promise<MailinatorMessage[]> {\n    try {\n      const response = await fetch(`https://www.mailinator.com/api/v2/domains/public/inboxes/${inboxName}`)\n      \n      if (response.ok) {\n        const data = await response.json()\n        return data.msgs || []\n      } else {\n        console.error('❌ Failed to fetch public inbox messages:', response.statusText)\n        return []\n      }\n    } catch (error) {\n      console.error('❌ Error fetching public inbox messages:', error)\n      return []\n    }\n  }\n\n  // Get messages from private domain\n  private static async getPrivateInboxMessages(inboxName: string): Promise<MailinatorMessage[]> {\n    try {\n      const response = await fetch(\n        `https://api.mailinator.com/api/v2/domains/private/inboxes/${inboxName}`,\n        {\n          headers: {\n            'Authorization': this.config.apiToken!\n          }\n        }\n      )\n      \n      if (response.ok) {\n        const data = await response.json()\n        return data.msgs || []\n      } else {\n        console.error('❌ Failed to fetch private inbox messages:', response.statusText)\n        return []\n      }\n    } catch (error) {\n      console.error('❌ Error fetching private inbox messages:', error)\n      return []\n    }\n  }\n\n  // Utility functions\n  private static extractInboxName(email: string): string {\n    const parts = email.split('@')\n    return parts[0] || 'demo'\n  }\n\n  private static htmlToText(html: string): string {\n    // Simple HTML to text conversion\n    return html\n      .replace(/<[^>]*>/g, '')\n      .replace(/&nbsp;/g, ' ')\n      .replace(/&amp;/g, '&')\n      .replace(/&lt;/g, '<')\n      .replace(/&gt;/g, '>')\n      .replace(/&quot;/g, '\"')\n      .trim()\n  }\n\n  // Generate Mailinator email for testing\n  static generateTestEmail(name: string): string {\n    const cleanName = name.toLowerCase().replace(/[^a-z0-9]/g, '')\n    return `${cleanName}@mailinator.com`\n  }\n\n  // Create demo Mailinator account setup\n  static setupDemoAccount(): {\n    testEmails: string[]\n    inboxUrls: string[]\n    instructions: string[]\n  } {\n    const testEmails = [\n      '<EMAIL>',\n      '<EMAIL>',\n      '<EMAIL>',\n      '<EMAIL>'\n    ]\n\n    const inboxUrls = testEmails.map(email => {\n      const inboxName = this.extractInboxName(email)\n      return `https://www.mailinator.com/v4/public/inboxes.jsp?to=${inboxName}`\n    })\n\n    const instructions = [\n      '1. 📧 Use any of the test emails above for demo purposes',\n      '2. 🌐 Visit the inbox URLs to check received emails',\n      '3. 🔄 Emails are automatically deleted after a few hours',\n      '4. 🆓 No signup required for public Mailinator inboxes',\n      '5. 🔗 Click email links to test the full interview flow'\n    ]\n\n    return {\n      testEmails,\n      inboxUrls,\n      instructions\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAwBsB;AAFf,MAAM;IACX,OAAe,SAA2B;QACxC,iBAAiB,CAAC,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,oBAAoB;QAClD,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,yBAAyB;QACpD,UAAU,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,oBAAoB;QAC1C,cAAc,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,wBAAwB;IACpD,EAAC;IAED,OAAO,UAAU,MAAiC,EAAE;QAClD,IAAI,CAAC,MAAM,GAAG;YAAE,GAAG,IAAI,CAAC,MAAM;YAAE,GAAG,MAAM;QAAC;IAC5C;IAEA,OAAO,YAA8B;QACnC,OAAO;YAAE,GAAG,IAAI,CAAC,MAAM;QAAC;IAC1B;IAEA,gCAAgC;IAChC,aAAa,UACX,EAAU,EACV,OAAe,EACf,IAAY,EACZ,IAAa,EACK;QAClB,IAAI;YACF,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;gBAC/B,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,SAAS,MAAM;YAC1D,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;gBAC5D,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,SAAS,MAAM;YAC3D,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI;gBACvB,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS;gBACjC,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC;gBAChD,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,OAAO;QACT;IACF;IAEA,+CAA+C;IAC/C,aAAqB,mBACnB,EAAU,EACV,OAAe,EACf,IAAY,EACZ,IAAa,EACK;QAClB,IAAI;YACF,gCAAgC;YAChC,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;YAExC,sDAAsD;YACtD,MAAM,aAAa,CAAC,yDAAyD,EAAE,UAAU,CAAC,CAAC;YAE3F,MAAM,UAAU;gBACd,MAAM;gBACN,SAAS;gBACT,MAAM,QAAQ,IAAI,CAAC,UAAU,CAAC;gBAC9B,MAAM;gBACN,IAAI;YACN;YAEA,MAAM,WAAW,MAAM,MAAM,YAAY;gBACvC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,UAAU,eAAe,CAAC;gBAClF,QAAQ,GAAG,CAAC,CAAC,uEAAuE,EAAE,WAAW;gBACjG,OAAO;YACT,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,iDAAiD,SAAS,MAAM,EAAE;gBAChF,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gDAAgD;YAC9D,OAAO;QACT;IACF;IAEA,yDAAyD;IACzD,aAAqB,oBACnB,EAAU,EACV,OAAe,EACf,IAAY,EACZ,IAAa,EACK;QAClB,IAAI;YACF,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;YAExC,MAAM,SAAS,CAAC,0DAA0D,EAAE,UAAU,SAAS,CAAC;YAEhG,MAAM,UAAU;gBACd,MAAM;gBACN,SAAS;gBACT,MAAM,QAAQ,IAAI,CAAC,UAAU,CAAC;gBAC9B,MAAM;YACR;YAEA,MAAM,WAAW,MAAM,MAAM,QAAQ;gBACnC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,IAAI,CAAC,MAAM,CAAC,QAAQ;gBACvC;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,IAAI;gBAC9D,OAAO;YACT,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,kDAAkD,SAAS,MAAM,EAAE;gBACjF,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iDAAiD;YAC/D,OAAO;QACT;IACF;IAEA,uCAAuC;IACvC,aAAa,iBAAiB,SAAiB,EAAgC;QAC7E,IAAI;YACF,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;gBAC/B,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC;YAC3C,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;gBAC5D,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC;YAC5C,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,OAAO,EAAE;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO,EAAE;QACX;IACF;IAEA,kCAAkC;IAClC,aAAqB,uBAAuB,SAAiB,EAAgC;QAC3F,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,yDAAyD,EAAE,WAAW;YAEpG,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,OAAO,KAAK,IAAI,IAAI,EAAE;YACxB,OAAO;gBACL,QAAQ,KAAK,CAAC,4CAA4C,SAAS,UAAU;gBAC7E,OAAO,EAAE;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,OAAO,EAAE;QACX;IACF;IAEA,mCAAmC;IACnC,aAAqB,wBAAwB,SAAiB,EAAgC;QAC5F,IAAI;YACF,MAAM,WAAW,MAAM,MACrB,CAAC,0DAA0D,EAAE,WAAW,EACxE;gBACE,SAAS;oBACP,iBAAiB,IAAI,CAAC,MAAM,CAAC,QAAQ;gBACvC;YACF;YAGF,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,OAAO,KAAK,IAAI,IAAI,EAAE;YACxB,OAAO;gBACL,QAAQ,KAAK,CAAC,6CAA6C,SAAS,UAAU;gBAC9E,OAAO,EAAE;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,OAAO,EAAE;QACX;IACF;IAEA,oBAAoB;IACpB,OAAe,iBAAiB,KAAa,EAAU;QACrD,MAAM,QAAQ,MAAM,KAAK,CAAC;QAC1B,OAAO,KAAK,CAAC,EAAE,IAAI;IACrB;IAEA,OAAe,WAAW,IAAY,EAAU;QAC9C,iCAAiC;QACjC,OAAO,KACJ,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,WAAW,KACnB,OAAO,CAAC,UAAU,KAClB,OAAO,CAAC,SAAS,KACjB,OAAO,CAAC,SAAS,KACjB,OAAO,CAAC,WAAW,KACnB,IAAI;IACT;IAEA,wCAAwC;IACxC,OAAO,kBAAkB,IAAY,EAAU;QAC7C,MAAM,YAAY,KAAK,WAAW,GAAG,OAAO,CAAC,cAAc;QAC3D,OAAO,GAAG,UAAU,eAAe,CAAC;IACtC;IAEA,uCAAuC;IACvC,OAAO,mBAIL;QACA,MAAM,aAAa;YACjB;YACA;YACA;YACA;SACD;QAED,MAAM,YAAY,WAAW,GAAG,CAAC,CAAA;YAC/B,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;YACxC,OAAO,CAAC,oDAAoD,EAAE,WAAW;QAC3E;QAEA,MAAM,eAAe;YACnB;YACA;YACA;YACA;YACA;SACD;QAED,OAAO;YACL;YACA;YACA;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 479, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/lib/email-service.ts"], "sourcesContent": ["import crypto from 'crypto'\nimport { MailinatorService } from './mailinator-service'\n\n// Only import nodemailer on server side\nlet nodemailer: any = null\nlet prisma: any = null\n\nif (typeof window === 'undefined') {\n  // Server-side imports\n  try {\n    nodemailer = require('nodemailer')\n    prisma = require('./prisma').prisma\n  } catch (error) {\n    console.log('Server-side dependencies not available')\n  }\n}\n\nexport interface InterviewInvitation {\n  id: string\n  email: string\n  studentName: string\n  interviewTitle: string\n  scheduledAt: Date\n  token: string\n  status: 'PENDING' | 'ACCEPTED' | 'COMPLETED' | 'EXPIRED'\n  createdAt: Date\n  expiresAt: Date\n}\n\n// In-memory storage for demo (replace with database in production)\nconst invitations: Map<string, InterviewInvitation> = new Map()\n\nexport interface EmailOptions {\n  to: string\n  subject: string\n  html: string\n  attachments?: Array<{\n    filename: string\n    path: string\n  }>\n}\n\nexport class EmailService {\n  private static transporter: any = null\n\n  // Initialize transporter only on server side\n  private static getTransporter() {\n    if (!this.transporter && nodemailer && typeof window === 'undefined') {\n      this.transporter = nodemailer.createTransport({\n        host: process.env.EMAIL_HOST,\n        port: parseInt(process.env.EMAIL_PORT || '587'),\n        secure: false,\n        auth: {\n          user: process.env.EMAIL_USER,\n          pass: process.env.EMAIL_PASS,\n        },\n      })\n    }\n    return this.transporter\n  }\n\n  // Email service mode configuration\n  private static emailMode: 'NODEMAILER' | 'MAILINATOR' | 'DEMO' = 'MAILINATOR'\n\n  static setEmailMode(mode: 'NODEMAILER' | 'MAILINATOR' | 'DEMO') {\n    this.emailMode = mode\n  }\n\n  static getEmailMode(): string {\n    return this.emailMode\n  }\n\n  // Create invitation method\n  static createInvitation(\n    email: string,\n    studentName: string,\n    interviewTitle: string,\n    scheduledAt: Date\n  ) {\n    const token = this.generateInvitationToken()\n    const id = crypto.randomUUID()\n\n    return {\n      id,\n      token,\n      email,\n      studentName,\n      interviewTitle,\n      scheduledAt,\n      status: 'pending' as const,\n      createdAt: new Date(),\n      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days\n    }\n  }\n\n  // Invitation Token Management\n  static generateInvitationToken(): string {\n    return crypto.randomBytes(32).toString('hex')\n  }\n\n  static createInvitation(\n    email: string,\n    studentName: string,\n    interviewTitle: string,\n    scheduledAt: Date\n  ): InterviewInvitation {\n    const token = this.generateInvitationToken()\n    const id = crypto.randomUUID()\n    const expiresAt = new Date(scheduledAt.getTime() + 24 * 60 * 60 * 1000) // 24 hours after scheduled time\n\n    const invitation: InterviewInvitation = {\n      id,\n      email,\n      studentName,\n      interviewTitle,\n      scheduledAt,\n      token,\n      status: 'PENDING',\n      createdAt: new Date(),\n      expiresAt\n    }\n\n    invitations.set(token, invitation)\n    return invitation\n  }\n\n  static getInvitationByToken(token: string): InterviewInvitation | null {\n    const invitation = invitations.get(token)\n    if (!invitation) return null\n\n    // Check if expired\n    if (new Date() > invitation.expiresAt) {\n      invitation.status = 'EXPIRED'\n    }\n\n    return invitation\n  }\n\n  static updateInvitationStatus(token: string, status: InterviewInvitation['status']): boolean {\n    const invitation = invitations.get(token)\n    if (!invitation) return false\n\n    invitation.status = status\n    return true\n  }\n\n  static generateInvitationLink(token: string, baseUrl: string = 'http://localhost:3000'): string {\n    return `${baseUrl}/interview/invite/${token}`\n  }\n\n  static async sendEmail(options: EmailOptions): Promise<boolean> {\n    try {\n      let success = false\n\n      // Choose email service based on mode\n      switch (this.emailMode) {\n        case 'MAILINATOR':\n          success = await MailinatorService.sendEmail(\n            options.to,\n            options.subject,\n            options.html\n          )\n          break\n\n        case 'NODEMAILER':\n          // Skip email sending if no email configuration or not on server\n          if (typeof window !== 'undefined') {\n            console.log('NodeMailer only works on server side')\n            return false\n          }\n\n          if (!process.env.EMAIL_USER || !process.env.EMAIL_PASS) {\n            console.log('Email service not configured, skipping email send')\n            return true\n          }\n\n          const transporter = this.getTransporter()\n          if (!transporter) {\n            console.log('Failed to initialize email transporter')\n            return false\n          }\n\n          const info = await transporter.sendMail({\n            from: process.env.EMAIL_USER,\n            to: options.to,\n            subject: options.subject,\n            html: options.html,\n            attachments: options.attachments,\n          })\n          success = true\n          break\n\n        case 'DEMO':\n        default:\n          console.log('📧 Demo Mode - Email would be sent:')\n          console.log(`To: ${options.to}`)\n          console.log(`Subject: ${options.subject}`)\n          success = true\n          break\n      }\n\n      // Log email (skip if database not available)\n      try {\n        await prisma.emailLog.create({\n          data: {\n            to: options.to,\n            subject: options.subject,\n            body: options.html,\n            status: success ? 'SENT' : 'FAILED',\n            sentAt: new Date(),\n          },\n        })\n      } catch (dbError) {\n        console.log('Database not available for email logging')\n      }\n\n      return success\n    } catch (error) {\n      console.error('Error sending email:', error)\n\n      // Log failed email (skip if database not available)\n      try {\n        await prisma.emailLog.create({\n          data: {\n            to: options.to,\n            subject: options.subject,\n            body: options.html,\n            status: 'FAILED',\n          },\n        })\n      } catch (dbError) {\n        console.log('Database not available for email logging')\n      }\n\n      return false\n    }\n  }\n\n  static async sendInterviewInvitationWithToken(invitation: InterviewInvitation): Promise<boolean> {\n    const invitationLink = this.generateInvitationLink(invitation.token)\n\n    const html = `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <div style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;\">\n          <h1 style=\"color: white; margin: 0;\">🎯 Video Interview Invitation</h1>\n        </div>\n\n        <div style=\"padding: 30px; background: #f8f9fa;\">\n          <h2 style=\"color: #333;\">Hello ${invitation.studentName}!</h2>\n\n          <p style=\"font-size: 16px; line-height: 1.6; color: #555;\">\n            You've been invited to participate in an AI-powered video interview for:\n          </p>\n\n          <div style=\"background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea;\">\n            <h3 style=\"margin: 0 0 10px 0; color: #333;\">${invitation.interviewTitle}</h3>\n            <p style=\"margin: 0; color: #666;\">\n              📅 Scheduled: ${invitation.scheduledAt.toLocaleDateString()} at ${invitation.scheduledAt.toLocaleTimeString()}\n            </p>\n          </div>\n\n          <div style=\"text-align: center; margin: 30px 0;\">\n            <a href=\"${invitationLink}\"\n               style=\"background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;\">\n              🚀 Join Video Interview\n            </a>\n          </div>\n\n          <div style=\"background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0;\">\n            <h4 style=\"margin: 0 0 10px 0; color: #1976d2;\">📋 What to Expect:</h4>\n            <ul style=\"margin: 0; padding-left: 20px; color: #555;\">\n              <li>🤖 AI-powered interviewer with voice interaction</li>\n              <li>📹 Video recording for review and feedback</li>\n              <li>💻 Interactive coding and behavioral questions</li>\n              <li>📊 Real-time evaluation and scoring</li>\n              <li>🎉 Instant results and congratulations</li>\n            </ul>\n          </div>\n\n          <div style=\"background: #fff3e0; padding: 15px; border-radius: 5px; margin: 20px 0;\">\n            <h4 style=\"margin: 0 0 10px 0; color: #f57c00;\">⚠️ Technical Requirements:</h4>\n            <ul style=\"margin: 0; padding-left: 20px; color: #555;\">\n              <li>🌐 Stable internet connection</li>\n              <li>📷 Working camera and microphone</li>\n              <li>🌍 Modern web browser (Chrome recommended)</li>\n              <li>🔇 Quiet environment for recording</li>\n              <li>💾 Allow browser permissions for camera/mic</li>\n            </ul>\n          </div>\n\n          <p style=\"font-size: 14px; color: #777; margin-top: 30px;\">\n            This invitation expires on ${invitation.expiresAt.toLocaleDateString()}.\n            If you have any questions, please contact our support team.\n          </p>\n        </div>\n\n        <div style=\"background: #333; padding: 20px; text-align: center;\">\n          <p style=\"color: #ccc; margin: 0; font-size: 14px;\">\n            © 2024 AI Interview Platform. All rights reserved.\n          </p>\n        </div>\n      </div>\n    `\n\n    return this.sendEmail({\n      to: invitation.email,\n      subject: `🎯 Video Interview Invitation - ${invitation.interviewTitle}`,\n      html,\n    })\n  }\n\n  static async sendInterviewInvitation(\n    studentEmail: string,\n    studentName: string,\n    interviewDetails: {\n      title: string\n      scheduledAt: Date\n      duration: number\n      meetingLink: string\n    }\n  ): Promise<boolean> {\n    const html = `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <h2 style=\"color: #2563eb;\">Interview Invitation</h2>\n        <p>Dear ${studentName},</p>\n        <p>You have been invited to participate in an interview:</p>\n        \n        <div style=\"background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n          <h3 style=\"margin-top: 0;\">${interviewDetails.title}</h3>\n          <p><strong>Date & Time:</strong> ${interviewDetails.scheduledAt.toLocaleString()}</p>\n          <p><strong>Duration:</strong> ${interviewDetails.duration} minutes</p>\n          <p><strong>Meeting Link:</strong> <a href=\"${interviewDetails.meetingLink}\">${interviewDetails.meetingLink}</a></p>\n        </div>\n        \n        <p>Please make sure to:</p>\n        <ul>\n          <li>Test your camera and microphone before the interview</li>\n          <li>Ensure you have a stable internet connection</li>\n          <li>Prepare for coding and theory questions</li>\n          <li>Join the meeting 5 minutes early</li>\n        </ul>\n        \n        <p>Good luck with your interview!</p>\n        <p>Best regards,<br>Mock Interview Team</p>\n      </div>\n    `\n\n    return this.sendEmail({\n      to: studentEmail,\n      subject: `Interview Invitation - ${interviewDetails.title}`,\n      html,\n    })\n  }\n\n  static async sendInterviewResults(\n    studentEmail: string,\n    studentName: string,\n    results: {\n      score: number\n      feedback: string\n      recordingUrl?: string\n    }\n  ): Promise<boolean> {\n    const html = `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <h2 style=\"color: #2563eb;\">Interview Results</h2>\n        <p>Dear ${studentName},</p>\n        <p>Your interview has been completed. Here are your results:</p>\n        \n        <div style=\"background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n          <h3 style=\"margin-top: 0;\">Overall Score: ${results.score}/100</h3>\n          <h4>Feedback:</h4>\n          <p>${results.feedback}</p>\n          ${results.recordingUrl ? `<p><strong>Recording:</strong> <a href=\"${results.recordingUrl}\">View Recording</a></p>` : ''}\n        </div>\n        \n        <p>Keep practicing and improving your skills!</p>\n        <p>Best regards,<br>Mock Interview Team</p>\n      </div>\n    `\n\n    return this.sendEmail({\n      to: studentEmail,\n      subject: 'Your Interview Results',\n      html,\n    })\n  }\n\n  static async sendWelcomeEmail(\n    userEmail: string,\n    userName: string,\n    role: string\n  ): Promise<boolean> {\n    const html = `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <h2 style=\"color: #2563eb;\">Welcome to Mock Interview Platform!</h2>\n        <p>Dear ${userName},</p>\n        <p>Welcome to our Mock Interview Platform! Your account has been created successfully.</p>\n        \n        <div style=\"background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n          <h3 style=\"margin-top: 0;\">Account Details</h3>\n          <p><strong>Email:</strong> ${userEmail}</p>\n          <p><strong>Role:</strong> ${role}</p>\n        </div>\n        \n        <p>You can now log in and start using the platform.</p>\n        <p>Best regards,<br>Mock Interview Team</p>\n      </div>\n    `\n\n    return this.sendEmail({\n      to: userEmail,\n      subject: 'Welcome to Mock Interview Platform',\n      html,\n    })\n  }\n\n  // Demo function to create sample invitations\n  static createDemoInvitations(): InterviewInvitation[] {\n    const demoInvitations = [\n      {\n        email: '<EMAIL>',\n        studentName: 'John Doe',\n        interviewTitle: 'Frontend Developer Position',\n        scheduledAt: new Date(Date.now() + 2 * 60 * 60 * 1000) // 2 hours from now\n      },\n      {\n        email: '<EMAIL>',\n        studentName: 'Jane Smith',\n        interviewTitle: 'Full Stack Developer Role',\n        scheduledAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 1 day from now\n      },\n      {\n        email: '<EMAIL>',\n        studentName: 'Alex Johnson',\n        interviewTitle: 'React Developer Interview',\n        scheduledAt: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3 days from now\n      }\n    ]\n\n    return demoInvitations.map(demo =>\n      this.createInvitation(demo.email, demo.studentName, demo.interviewTitle, demo.scheduledAt)\n    )\n  }\n}\n\n// Initialize demo invitations\nif (typeof window === 'undefined') {\n  EmailService.createDemoInvitations()\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,wCAAwC;AACxC,IAAI,aAAkB;AACtB,IAAI,SAAc;AAElB,uCAAmC;;AAQnC;AAcA,mEAAmE;AACnE,MAAM,cAAgD,IAAI;AAYnD,MAAM;IACX,OAAe,cAAmB,KAAI;IAEtC,6CAA6C;IAC7C,OAAe,iBAAiB;QAC9B,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,cAAc,aAAkB,aAAa;;QAUtE;QACA,OAAO,IAAI,CAAC,WAAW;IACzB;IAEA,mCAAmC;IACnC,OAAe,YAAkD,aAAY;IAE7E,OAAO,aAAa,IAA0C,EAAE;QAC9D,IAAI,CAAC,SAAS,GAAG;IACnB;IAEA,OAAO,eAAuB;QAC5B,OAAO,IAAI,CAAC,SAAS;IACvB;IAEA,2BAA2B;IAC3B,OAAO,iBACL,KAAa,EACb,WAAmB,EACnB,cAAsB,EACtB,WAAiB,EACjB;QACA,MAAM,QAAQ,IAAI,CAAC,uBAAuB;QAC1C,MAAM,KAAK,4KAAA,CAAA,UAAM,CAAC,UAAU;QAE5B,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA,QAAQ;YACR,WAAW,IAAI;YACf,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,SAAS;QACrE;IACF;IAEA,8BAA8B;IAC9B,OAAO,0BAAkC;QACvC,OAAO,4KAAA,CAAA,UAAM,CAAC,WAAW,CAAC,IAAI,QAAQ,CAAC;IACzC;IAEA,OAAO,iBACL,KAAa,EACb,WAAmB,EACnB,cAAsB,EACtB,WAAiB,EACI;QACrB,MAAM,QAAQ,IAAI,CAAC,uBAAuB;QAC1C,MAAM,KAAK,4KAAA,CAAA,UAAM,CAAC,UAAU;QAC5B,MAAM,YAAY,IAAI,KAAK,YAAY,OAAO,KAAK,KAAK,KAAK,KAAK,MAAM,gCAAgC;;QAExG,MAAM,aAAkC;YACtC;YACA;YACA;YACA;YACA;YACA;YACA,QAAQ;YACR,WAAW,IAAI;YACf;QACF;QAEA,YAAY,GAAG,CAAC,OAAO;QACvB,OAAO;IACT;IAEA,OAAO,qBAAqB,KAAa,EAA8B;QACrE,MAAM,aAAa,YAAY,GAAG,CAAC;QACnC,IAAI,CAAC,YAAY,OAAO;QAExB,mBAAmB;QACnB,IAAI,IAAI,SAAS,WAAW,SAAS,EAAE;YACrC,WAAW,MAAM,GAAG;QACtB;QAEA,OAAO;IACT;IAEA,OAAO,uBAAuB,KAAa,EAAE,MAAqC,EAAW;QAC3F,MAAM,aAAa,YAAY,GAAG,CAAC;QACnC,IAAI,CAAC,YAAY,OAAO;QAExB,WAAW,MAAM,GAAG;QACpB,OAAO;IACT;IAEA,OAAO,uBAAuB,KAAa,EAAE,UAAkB,uBAAuB,EAAU;QAC9F,OAAO,GAAG,QAAQ,kBAAkB,EAAE,OAAO;IAC/C;IAEA,aAAa,UAAU,OAAqB,EAAoB;QAC9D,IAAI;YACF,IAAI,UAAU;YAEd,qCAAqC;YACrC,OAAQ,IAAI,CAAC,SAAS;gBACpB,KAAK;oBACH,UAAU,MAAM,sIAAA,CAAA,oBAAiB,CAAC,SAAS,CACzC,QAAQ,EAAE,EACV,QAAQ,OAAO,EACf,QAAQ,IAAI;oBAEd;gBAEF,KAAK;oBACH,gEAAgE;oBAChE,wCAAmC;wBACjC,QAAQ,GAAG,CAAC;wBACZ,OAAO;oBACT;;oBAOA,MAAM;oBAMN,MAAM;gBAUR,KAAK;gBACL;oBACE,QAAQ,GAAG,CAAC;oBACZ,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE;oBAC/B,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,QAAQ,OAAO,EAAE;oBACzC,UAAU;oBACV;YACJ;YAEA,6CAA6C;YAC7C,IAAI;gBACF,MAAM,OAAO,QAAQ,CAAC,MAAM,CAAC;oBAC3B,MAAM;wBACJ,IAAI,QAAQ,EAAE;wBACd,SAAS,QAAQ,OAAO;wBACxB,MAAM,QAAQ,IAAI;wBAClB,QAAQ,UAAU,SAAS;wBAC3B,QAAQ,IAAI;oBACd;gBACF;YACF,EAAE,OAAO,SAAS;gBAChB,QAAQ,GAAG,CAAC;YACd;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YAEtC,oDAAoD;YACpD,IAAI;gBACF,MAAM,OAAO,QAAQ,CAAC,MAAM,CAAC;oBAC3B,MAAM;wBACJ,IAAI,QAAQ,EAAE;wBACd,SAAS,QAAQ,OAAO;wBACxB,MAAM,QAAQ,IAAI;wBAClB,QAAQ;oBACV;gBACF;YACF,EAAE,OAAO,SAAS;gBAChB,QAAQ,GAAG,CAAC;YACd;YAEA,OAAO;QACT;IACF;IAEA,aAAa,iCAAiC,UAA+B,EAAoB;QAC/F,MAAM,iBAAiB,IAAI,CAAC,sBAAsB,CAAC,WAAW,KAAK;QAEnE,MAAM,OAAO,CAAC;;;;;;;yCAOuB,EAAE,WAAW,WAAW,CAAC;;;;;;;yDAOT,EAAE,WAAW,cAAc,CAAC;;4BAEzD,EAAE,WAAW,WAAW,CAAC,kBAAkB,GAAG,IAAI,EAAE,WAAW,WAAW,CAAC,kBAAkB,GAAG;;;;;qBAKvG,EAAE,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCA6BC,EAAE,WAAW,SAAS,CAAC,kBAAkB,GAAG;;;;;;;;;;;IAW/E,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,IAAI,WAAW,KAAK;YACpB,SAAS,CAAC,gCAAgC,EAAE,WAAW,cAAc,EAAE;YACvE;QACF;IACF;IAEA,aAAa,wBACX,YAAoB,EACpB,WAAmB,EACnB,gBAKC,EACiB;QAClB,MAAM,OAAO,CAAC;;;gBAGF,EAAE,YAAY;;;;qCAIO,EAAE,iBAAiB,KAAK,CAAC;2CACnB,EAAE,iBAAiB,WAAW,CAAC,cAAc,GAAG;wCACnD,EAAE,iBAAiB,QAAQ,CAAC;qDACf,EAAE,iBAAiB,WAAW,CAAC,EAAE,EAAE,iBAAiB,WAAW,CAAC;;;;;;;;;;;;;;IAcjH,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,IAAI;YACJ,SAAS,CAAC,uBAAuB,EAAE,iBAAiB,KAAK,EAAE;YAC3D;QACF;IACF;IAEA,aAAa,qBACX,YAAoB,EACpB,WAAmB,EACnB,OAIC,EACiB;QAClB,MAAM,OAAO,CAAC;;;gBAGF,EAAE,YAAY;;;;oDAIsB,EAAE,QAAQ,KAAK,CAAC;;aAEvD,EAAE,QAAQ,QAAQ,CAAC;UACtB,EAAE,QAAQ,YAAY,GAAG,CAAC,wCAAwC,EAAE,QAAQ,YAAY,CAAC,wBAAwB,CAAC,GAAG,GAAG;;;;;;IAM9H,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,IAAI;YACJ,SAAS;YACT;QACF;IACF;IAEA,aAAa,iBACX,SAAiB,EACjB,QAAgB,EAChB,IAAY,EACM;QAClB,MAAM,OAAO,CAAC;;;gBAGF,EAAE,SAAS;;;;;qCAKU,EAAE,UAAU;oCACb,EAAE,KAAK;;;;;;IAMvC,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,IAAI;YACJ,SAAS;YACT;QACF;IACF;IAEA,6CAA6C;IAC7C,OAAO,wBAA+C;QACpD,MAAM,kBAAkB;YACtB;gBACE,OAAO;gBACP,aAAa;gBACb,gBAAgB;gBAChB,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,mBAAmB;YAC5E;YACA;gBACE,OAAO;gBACP,aAAa;gBACb,gBAAgB;gBAChB,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,MAAM,iBAAiB;YAC3E;YACA;gBACE,OAAO;gBACP,aAAa;gBACb,gBAAgB;gBAChB,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,kBAAkB;YAChF;SACD;QAED,OAAO,gBAAgB,GAAG,CAAC,CAAA,OACzB,IAAI,CAAC,gBAAgB,CAAC,KAAK,KAAK,EAAE,KAAK,WAAW,EAAE,KAAK,cAAc,EAAE,KAAK,WAAW;IAE7F;AACF;AAEA,8BAA8B;AAC9B,uCAAmC;;AAEnC", "debugId": null}}, {"offset": {"line": 813, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/lib/demo-auth.ts"], "sourcesContent": ["// Demo authentication system for testing without database\nexport interface DemoUser {\n  id: string\n  email: string\n  name: string\n  role: 'ADMIN' | 'TENANT' | 'STUDENT'\n  password: string\n}\n\n// Demo users for testing\nexport const DEMO_USERS: DemoUser[] = [\n  {\n    id: 'admin-1',\n    email: '<EMAIL>',\n    name: 'System Administrator',\n    role: 'ADMIN',\n    password: 'admin123'\n  },\n  {\n    id: 'tenant-1',\n    email: '<EMAIL>',\n    name: 'TechCorp Manager',\n    role: 'TENANT',\n    password: 'tenant123'\n  },\n  {\n    id: 'student-1',\n    email: '<EMAIL>',\n    name: '<PERSON>',\n    role: 'STUDENT',\n    password: 'student123'\n  },\n  {\n    id: 'student-2',\n    email: '<EMAIL>',\n    name: '<PERSON>',\n    role: 'STUDENT',\n    password: 'student123'\n  },\n  {\n    id: 'tenant-2',\n    email: '<EMAIL>',\n    name: 'StartupXYZ HR',\n    role: 'TENANT',\n    password: 'startup123'\n  }\n]\n\nexport class DemoAuthService {\n  // Validate demo user credentials\n  static validateUser(email: string, password: string): DemoUser | null {\n    const user = DEMO_USERS.find(u => u.email === email && u.password === password)\n    return user || null\n  }\n\n  // Check if email exists\n  static emailExists(email: string): boolean {\n    return DEMO_USERS.some(u => u.email === email)\n  }\n\n  // Add new demo user (for signup)\n  static addUser(userData: Omit<DemoUser, 'id'>): DemoUser {\n    const newUser: DemoUser = {\n      id: `demo-${Date.now()}`,\n      ...userData\n    }\n    DEMO_USERS.push(newUser)\n    return newUser\n  }\n\n  // Get user by ID\n  static getUserById(id: string): DemoUser | null {\n    return DEMO_USERS.find(u => u.id === id) || null\n  }\n\n  // Get all users (for admin)\n  static getAllUsers(): DemoUser[] {\n    return DEMO_USERS\n  }\n}\n\n// Client-side authentication helper\nexport class DemoAuth {\n  private static readonly STORAGE_KEY = 'demo_auth_user'\n\n  // Set current user in localStorage\n  static setCurrentUser(user: DemoUser): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(user))\n    }\n  }\n\n  // Get current user from localStorage\n  static getCurrentUser(): DemoUser | null {\n    if (typeof window !== 'undefined') {\n      const stored = localStorage.getItem(this.STORAGE_KEY)\n      if (stored) {\n        try {\n          return JSON.parse(stored)\n        } catch {\n          return null\n        }\n      }\n    }\n    return null\n  }\n\n  // Clear current user\n  static clearCurrentUser(): void {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem(this.STORAGE_KEY)\n    }\n  }\n\n  // Check if user is authenticated\n  static isAuthenticated(): boolean {\n    return this.getCurrentUser() !== null\n  }\n\n  // Login with email and password\n  static login(email: string, password: string): DemoUser | null {\n    const user = DemoAuthService.validateUser(email, password)\n    if (user) {\n      this.setCurrentUser(user)\n    }\n    return user\n  }\n\n  // Logout\n  static logout(): void {\n    this.clearCurrentUser()\n  }\n\n  // Register new user\n  static register(email: string, password: string, name: string, role: 'ADMIN' | 'TENANT' | 'STUDENT' = 'STUDENT'): DemoUser | null {\n    // Check if email already exists\n    if (DemoAuthService.emailExists(email)) {\n      return null\n    }\n\n    // Create new user\n    const newUser = DemoAuthService.addUser({\n      email,\n      password,\n      name,\n      role\n    })\n\n    // Auto-login the new user\n    this.setCurrentUser(newUser)\n    return newUser\n  }\n}\n"], "names": [], "mappings": "AAAA,0DAA0D;;;;;;AAUnD,MAAM,aAAyB;IACpC;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;IACZ;CACD;AAEM,MAAM;IACX,iCAAiC;IACjC,OAAO,aAAa,KAAa,EAAE,QAAgB,EAAmB;QACpE,MAAM,OAAO,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,SAAS,EAAE,QAAQ,KAAK;QACtE,OAAO,QAAQ;IACjB;IAEA,wBAAwB;IACxB,OAAO,YAAY,KAAa,EAAW;QACzC,OAAO,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;IAC1C;IAEA,iCAAiC;IACjC,OAAO,QAAQ,QAA8B,EAAY;QACvD,MAAM,UAAoB;YACxB,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;YACxB,GAAG,QAAQ;QACb;QACA,WAAW,IAAI,CAAC;QAChB,OAAO;IACT;IAEA,iBAAiB;IACjB,OAAO,YAAY,EAAU,EAAmB;QAC9C,OAAO,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO;IAC9C;IAEA,4BAA4B;IAC5B,OAAO,cAA0B;QAC/B,OAAO;IACT;AACF;AAGO,MAAM;IACX,OAAwB,cAAc,iBAAgB;IAEtD,mCAAmC;IACnC,OAAO,eAAe,IAAc,EAAQ;QAC1C,wCAAmC;YACjC,aAAa,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,SAAS,CAAC;QACxD;IACF;IAEA,qCAAqC;IACrC,OAAO,iBAAkC;QACvC,wCAAmC;YACjC,MAAM,SAAS,aAAa,OAAO,CAAC,IAAI,CAAC,WAAW;YACpD,IAAI,QAAQ;gBACV,IAAI;oBACF,OAAO,KAAK,KAAK,CAAC;gBACpB,EAAE,OAAM;oBACN,OAAO;gBACT;YACF;QACF;QACA,OAAO;IACT;IAEA,qBAAqB;IACrB,OAAO,mBAAyB;QAC9B,wCAAmC;YACjC,aAAa,UAAU,CAAC,IAAI,CAAC,WAAW;QAC1C;IACF;IAEA,iCAAiC;IACjC,OAAO,kBAA2B;QAChC,OAAO,IAAI,CAAC,cAAc,OAAO;IACnC;IAEA,gCAAgC;IAChC,OAAO,MAAM,KAAa,EAAE,QAAgB,EAAmB;QAC7D,MAAM,OAAO,gBAAgB,YAAY,CAAC,OAAO;QACjD,IAAI,MAAM;YACR,IAAI,CAAC,cAAc,CAAC;QACtB;QACA,OAAO;IACT;IAEA,SAAS;IACT,OAAO,SAAe;QACpB,IAAI,CAAC,gBAAgB;IACvB;IAEA,oBAAoB;IACpB,OAAO,SAAS,KAAa,EAAE,QAAgB,EAAE,IAAY,EAAE,OAAuC,SAAS,EAAmB;QAChI,gCAAgC;QAChC,IAAI,gBAAgB,WAAW,CAAC,QAAQ;YACtC,OAAO;QACT;QAEA,kBAAkB;QAClB,MAAM,UAAU,gBAAgB,OAAO,CAAC;YACtC;YACA;YACA;YACA;QACF;QAEA,0BAA0B;QAC1B,IAAI,CAAC,cAAc,CAAC;QACpB,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 955, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Automation/Buildmockinterview/mock-interview-app/src/app/interview/invite/%5Btoken%5D/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { usePara<PERSON>, useRouter } from 'next/navigation'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { EmailService, InterviewInvitation } from '@/lib/email-service'\nimport { DemoAuth } from '@/lib/demo-auth'\nimport { \n  Calendar, \n  Clock, \n  User, \n  Mail, \n  Lock, \n  CheckCircle, \n  AlertCircle,\n  Video,\n  Mic,\n  Monitor\n} from 'lucide-react'\n\nexport default function InvitationPage() {\n  const params = useParams()\n  const router = useRouter()\n  const token = params.token as string\n\n  const [invitation, setInvitation] = useState<InterviewInvitation | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState('')\n  const [showAuth, setShowAuth] = useState(false)\n  const [isLogin, setIsLogin] = useState(true)\n  const [authLoading, setAuthLoading] = useState(false)\n\n  // Auth form state\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [confirmPassword, setConfirmPassword] = useState('')\n  const [fullName, setFullName] = useState('')\n\n  useEffect(() => {\n    if (token) {\n      const invitationData = EmailService.getInvitationByToken(token)\n      if (invitationData) {\n        setInvitation(invitationData)\n        setEmail(invitationData.email)\n        setFullName(invitationData.studentName)\n      } else {\n        setError('Invalid or expired invitation link')\n      }\n      setLoading(false)\n    }\n  }, [token])\n\n  const handleAuthSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setAuthLoading(true)\n    setError('')\n\n    try {\n      if (isLogin) {\n        // Login\n        const success = DemoAuth.login(email, password)\n        if (success) {\n          EmailService.updateInvitationStatus(token, 'ACCEPTED')\n          router.push(`/interview/briefing/${token}`)\n        } else {\n          setError('Invalid email or password')\n        }\n      } else {\n        // Signup\n        if (password !== confirmPassword) {\n          setError('Passwords do not match')\n          setAuthLoading(false)\n          return\n        }\n\n        if (password.length < 6) {\n          setError('Password must be at least 6 characters')\n          setAuthLoading(false)\n          return\n        }\n\n        const success = DemoAuth.register(email, password, fullName, 'STUDENT')\n        if (success) {\n          EmailService.updateInvitationStatus(token, 'ACCEPTED')\n          router.push(`/interview/briefing/${token}`)\n        } else {\n          setError('Registration failed. Email might already exist.')\n        }\n      }\n    } catch (err) {\n      setError('Authentication failed. Please try again.')\n    }\n\n    setAuthLoading(false)\n  }\n\n  const handleProceedToInterview = () => {\n    const currentUser = DemoAuth.getCurrentUser()\n    if (currentUser) {\n      EmailService.updateInvitationStatus(token, 'ACCEPTED')\n      router.push(`/interview/briefing/${token}`)\n    } else {\n      setShowAuth(true)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-900 flex items-center justify-center\">\n        <div className=\"text-white text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"></div>\n          <p>Loading invitation...</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (error && !invitation) {\n    return (\n      <div className=\"min-h-screen bg-gray-900 flex items-center justify-center\">\n        <Card className=\"w-full max-w-md bg-gray-800 border-gray-700\">\n          <CardContent className=\"p-6 text-center\">\n            <AlertCircle className=\"h-12 w-12 text-red-500 mx-auto mb-4\" />\n            <h2 className=\"text-xl font-bold text-white mb-2\">Invalid Invitation</h2>\n            <p className=\"text-gray-400 mb-4\">{error}</p>\n            <Button onClick={() => router.push('/')} variant=\"outline\">\n              Go to Home\n            </Button>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  if (invitation?.status === 'EXPIRED') {\n    return (\n      <div className=\"min-h-screen bg-gray-900 flex items-center justify-center\">\n        <Card className=\"w-full max-w-md bg-gray-800 border-gray-700\">\n          <CardContent className=\"p-6 text-center\">\n            <Clock className=\"h-12 w-12 text-orange-500 mx-auto mb-4\" />\n            <h2 className=\"text-xl font-bold text-white mb-2\">Invitation Expired</h2>\n            <p className=\"text-gray-400 mb-4\">\n              This invitation has expired. Please contact the interviewer for a new invitation.\n            </p>\n            <Button onClick={() => router.push('/')} variant=\"outline\">\n              Go to Home\n            </Button>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-900\">\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 p-6\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          <h1 className=\"text-3xl font-bold text-white mb-2\">🎯 Video Interview Invitation</h1>\n          <p className=\"text-blue-100\">AI-Powered Interview Experience</p>\n        </div>\n      </div>\n\n      <div className=\"max-w-4xl mx-auto p-6\">\n        {!showAuth ? (\n          // Invitation Details\n          <div className=\"grid md:grid-cols-2 gap-6\">\n            {/* Interview Details */}\n            <Card className=\"bg-gray-800 border-gray-700\">\n              <CardHeader>\n                <CardTitle className=\"text-white flex items-center\">\n                  <Video className=\"h-5 w-5 mr-2 text-blue-500\" />\n                  Interview Details\n                </CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"flex items-center space-x-3\">\n                  <User className=\"h-5 w-5 text-gray-400\" />\n                  <div>\n                    <p className=\"text-sm text-gray-400\">Candidate</p>\n                    <p className=\"text-white font-medium\">{invitation?.studentName}</p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center space-x-3\">\n                  <Mail className=\"h-5 w-5 text-gray-400\" />\n                  <div>\n                    <p className=\"text-sm text-gray-400\">Email</p>\n                    <p className=\"text-white font-medium\">{invitation?.email}</p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center space-x-3\">\n                  <Calendar className=\"h-5 w-5 text-gray-400\" />\n                  <div>\n                    <p className=\"text-sm text-gray-400\">Position</p>\n                    <p className=\"text-white font-medium\">{invitation?.interviewTitle}</p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center space-x-3\">\n                  <Clock className=\"h-5 w-5 text-gray-400\" />\n                  <div>\n                    <p className=\"text-sm text-gray-400\">Scheduled</p>\n                    <p className=\"text-white font-medium\">\n                      {invitation?.scheduledAt.toLocaleDateString()} at {invitation?.scheduledAt.toLocaleTimeString()}\n                    </p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Technical Requirements */}\n            <Card className=\"bg-gray-800 border-gray-700\">\n              <CardHeader>\n                <CardTitle className=\"text-white flex items-center\">\n                  <Monitor className=\"h-5 w-5 mr-2 text-green-500\" />\n                  Technical Requirements\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center space-x-3\">\n                    <CheckCircle className=\"h-4 w-4 text-green-500\" />\n                    <span className=\"text-gray-300\">Stable internet connection</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <Video className=\"h-4 w-4 text-green-500\" />\n                    <span className=\"text-gray-300\">Working camera</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <Mic className=\"h-4 w-4 text-green-500\" />\n                    <span className=\"text-gray-300\">Working microphone</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <Monitor className=\"h-4 w-4 text-green-500\" />\n                    <span className=\"text-gray-300\">Modern web browser</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <CheckCircle className=\"h-4 w-4 text-green-500\" />\n                    <span className=\"text-gray-300\">Quiet environment</span>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        ) : (\n          // Authentication Form\n          <div className=\"max-w-md mx-auto\">\n            <Card className=\"bg-gray-800 border-gray-700\">\n              <CardHeader>\n                <CardTitle className=\"text-white text-center\">\n                  {isLogin ? 'Login to Continue' : 'Create Account'}\n                </CardTitle>\n                <CardDescription className=\"text-center text-gray-400\">\n                  {isLogin ? 'Sign in to join your interview' : 'Create an account to proceed'}\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <form onSubmit={handleAuthSubmit} className=\"space-y-4\">\n                  {!isLogin && (\n                    <div>\n                      <Label htmlFor=\"fullName\" className=\"text-gray-300\">Full Name</Label>\n                      <Input\n                        id=\"fullName\"\n                        type=\"text\"\n                        value={fullName}\n                        onChange={(e) => setFullName(e.target.value)}\n                        className=\"bg-gray-700 border-gray-600 text-white\"\n                        required\n                      />\n                    </div>\n                  )}\n\n                  <div>\n                    <Label htmlFor=\"email\" className=\"text-gray-300\">Email</Label>\n                    <Input\n                      id=\"email\"\n                      type=\"email\"\n                      value={email}\n                      onChange={(e) => setEmail(e.target.value)}\n                      className=\"bg-gray-700 border-gray-600 text-white\"\n                      required\n                      disabled={!!invitation?.email}\n                    />\n                  </div>\n\n                  <div>\n                    <Label htmlFor=\"password\" className=\"text-gray-300\">Password</Label>\n                    <Input\n                      id=\"password\"\n                      type=\"password\"\n                      value={password}\n                      onChange={(e) => setPassword(e.target.value)}\n                      className=\"bg-gray-700 border-gray-600 text-white\"\n                      required\n                    />\n                  </div>\n\n                  {!isLogin && (\n                    <div>\n                      <Label htmlFor=\"confirmPassword\" className=\"text-gray-300\">Confirm Password</Label>\n                      <Input\n                        id=\"confirmPassword\"\n                        type=\"password\"\n                        value={confirmPassword}\n                        onChange={(e) => setConfirmPassword(e.target.value)}\n                        className=\"bg-gray-700 border-gray-600 text-white\"\n                        required\n                      />\n                    </div>\n                  )}\n\n                  {error && (\n                    <div className=\"text-red-400 text-sm text-center\">{error}</div>\n                  )}\n\n                  <Button \n                    type=\"submit\" \n                    className=\"w-full bg-blue-600 hover:bg-blue-700\"\n                    disabled={authLoading}\n                  >\n                    {authLoading ? 'Processing...' : (isLogin ? 'Login' : 'Create Account')}\n                  </Button>\n\n                  <div className=\"text-center\">\n                    <button\n                      type=\"button\"\n                      onClick={() => setIsLogin(!isLogin)}\n                      className=\"text-blue-400 hover:text-blue-300 text-sm\"\n                    >\n                      {isLogin ? \"Don't have an account? Sign up\" : \"Already have an account? Login\"}\n                    </button>\n                  </div>\n                </form>\n              </CardContent>\n            </Card>\n          </div>\n        )}\n\n        {/* Action Button */}\n        {!showAuth && (\n          <div className=\"mt-8 text-center\">\n            <Button \n              onClick={handleProceedToInterview}\n              size=\"lg\"\n              className=\"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3\"\n            >\n              🚀 Proceed to Interview\n            </Button>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAVA;;;;;;;;;;AAuBe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,QAAQ,OAAO,KAAK;IAE1B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B;IACzE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,kBAAkB;IAClB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,OAAO;gBACT,MAAM,iBAAiB,iIAAA,CAAA,eAAY,CAAC,oBAAoB,CAAC;gBACzD,IAAI,gBAAgB;oBAClB,cAAc;oBACd,SAAS,eAAe,KAAK;oBAC7B,YAAY,eAAe,WAAW;gBACxC,OAAO;oBACL,SAAS;gBACX;gBACA,WAAW;YACb;QACF;mCAAG;QAAC;KAAM;IAEV,MAAM,mBAAmB,OAAO;QAC9B,EAAE,cAAc;QAChB,eAAe;QACf,SAAS;QAET,IAAI;YACF,IAAI,SAAS;gBACX,QAAQ;gBACR,MAAM,UAAU,6HAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,OAAO;gBACtC,IAAI,SAAS;oBACX,iIAAA,CAAA,eAAY,CAAC,sBAAsB,CAAC,OAAO;oBAC3C,OAAO,IAAI,CAAC,CAAC,oBAAoB,EAAE,OAAO;gBAC5C,OAAO;oBACL,SAAS;gBACX;YACF,OAAO;gBACL,SAAS;gBACT,IAAI,aAAa,iBAAiB;oBAChC,SAAS;oBACT,eAAe;oBACf;gBACF;gBAEA,IAAI,SAAS,MAAM,GAAG,GAAG;oBACvB,SAAS;oBACT,eAAe;oBACf;gBACF;gBAEA,MAAM,UAAU,6HAAA,CAAA,WAAQ,CAAC,QAAQ,CAAC,OAAO,UAAU,UAAU;gBAC7D,IAAI,SAAS;oBACX,iIAAA,CAAA,eAAY,CAAC,sBAAsB,CAAC,OAAO;oBAC3C,OAAO,IAAI,CAAC,CAAC,oBAAoB,EAAE,OAAO;gBAC5C,OAAO;oBACL,SAAS;gBACX;YACF;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX;QAEA,eAAe;IACjB;IAEA,MAAM,2BAA2B;QAC/B,MAAM,cAAc,6HAAA,CAAA,WAAQ,CAAC,cAAc;QAC3C,IAAI,aAAa;YACf,iIAAA,CAAA,eAAY,CAAC,sBAAsB,CAAC,OAAO;YAC3C,OAAO,IAAI,CAAC,CAAC,oBAAoB,EAAE,OAAO;QAC5C,OAAO;YACL,YAAY;QACd;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;kCAAE;;;;;;;;;;;;;;;;;IAIX;IAEA,IAAI,SAAS,CAAC,YAAY;QACxB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6LAAC;4BAAG,WAAU;sCAAoC;;;;;;sCAClD,6LAAC;4BAAE,WAAU;sCAAsB;;;;;;sCACnC,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS,IAAM,OAAO,IAAI,CAAC;4BAAM,SAAQ;sCAAU;;;;;;;;;;;;;;;;;;;;;;IAOrE;IAEA,IAAI,YAAY,WAAW,WAAW;QACpC,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,6LAAC;4BAAG,WAAU;sCAAoC;;;;;;sCAClD,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS,IAAM,OAAO,IAAI,CAAC;4BAAM,SAAQ;sCAAU;;;;;;;;;;;;;;;;;;;;;;IAOrE;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAIjC,6LAAC;gBAAI,WAAU;;oBACZ,CAAC,WACA,qBAAqB;kCACrB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAA+B;;;;;;;;;;;;kDAIpD,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,WAAU;0EAA0B,YAAY;;;;;;;;;;;;;;;;;;0DAIvD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,WAAU;0EAA0B,YAAY;;;;;;;;;;;;;;;;;;0DAIvD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,WAAU;0EAA0B,YAAY;;;;;;;;;;;;;;;;;;0DAIvD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,WAAU;;oEACV,YAAY,YAAY;oEAAqB;oEAAK,YAAY,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQrF,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,2MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAAgC;;;;;;;;;;;;kDAIvD,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;sEACf,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,2MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAO1C,sBAAsB;kCACtB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAClB,UAAU,sBAAsB;;;;;;sDAEnC,6LAAC,mIAAA,CAAA,kBAAe;4CAAC,WAAU;sDACxB,UAAU,mCAAmC;;;;;;;;;;;;8CAGlD,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAK,UAAU;wCAAkB,WAAU;;4CACzC,CAAC,yBACA,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAW,WAAU;kEAAgB;;;;;;kEACpD,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wDAC3C,WAAU;wDACV,QAAQ;;;;;;;;;;;;0DAKd,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAQ,WAAU;kEAAgB;;;;;;kEACjD,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wDACxC,WAAU;wDACV,QAAQ;wDACR,UAAU,CAAC,CAAC,YAAY;;;;;;;;;;;;0DAI5B,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAW,WAAU;kEAAgB;;;;;;kEACpD,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wDAC3C,WAAU;wDACV,QAAQ;;;;;;;;;;;;4CAIX,CAAC,yBACA,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAkB,WAAU;kEAAgB;;;;;;kEAC3D,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wDAClD,WAAU;wDACV,QAAQ;;;;;;;;;;;;4CAKb,uBACC,6LAAC;gDAAI,WAAU;0DAAoC;;;;;;0DAGrD,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,WAAU;gDACV,UAAU;0DAET,cAAc,kBAAmB,UAAU,UAAU;;;;;;0DAGxD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,MAAK;oDACL,SAAS,IAAM,WAAW,CAAC;oDAC3B,WAAU;8DAET,UAAU,mCAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAU3D,CAAC,0BACA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAQb;GA/UwB;;QACP,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}