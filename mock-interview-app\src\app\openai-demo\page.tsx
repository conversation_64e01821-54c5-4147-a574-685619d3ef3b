'use client'

import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Brain, 
  MessageSquare, 
  FileQuestion, 
  BarChart3, 
  Loader2, 
  CheckCircle, 
  AlertCircle,
  Sparkles,
  Code,
  Users
} from 'lucide-react'

export default function OpenAIDemoPage() {
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<any>(null)
  const [activeTab, setActiveTab] = useState('evaluate')

  // Evaluation demo state
  const [sampleResponses, setSampleResponses] = useState([
    {
      questionId: '1',
      question: 'Tell me about yourself and your background.',
      answer: 'I am a software developer with 3 years of experience in React and Node.js. I have worked on several web applications and enjoy solving complex problems.',
      timestamp: new Date(),
      duration: 120
    },
    {
      questionId: '2', 
      question: 'Explain the difference between let, const, and var in JavaScript.',
      answer: 'Let and const are block-scoped while var is function-scoped. Const cannot be reassigned after declaration, let can be reassigned, and var can be redeclared and reassigned.',
      timestamp: new Date(),
      duration: 180
    }
  ])

  // Question generation state
  const [jobTitle, setJobTitle] = useState('Frontend Developer')
  const [experience, setExperience] = useState('3 years')
  const [skills, setSkills] = useState('React, JavaScript, TypeScript')
  const [difficulty, setDifficulty] = useState('intermediate')
  const [questionType, setQuestionType] = useState('technical')

  // Response analysis state
  const [question, setQuestion] = useState('What is your experience with React?')
  const [answer, setAnswer] = useState('I have been working with React for 2 years, building several web applications including e-commerce sites and dashboards.')

  // Follow-up generation state
  const [originalQuestion, setOriginalQuestion] = useState('Describe your problem-solving approach.')
  const [candidateAnswer, setCandidateAnswer] = useState('I usually start by understanding the problem, breaking it down into smaller parts, and then implementing solutions step by step.')

  const handleEvaluateInterview = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/ai/openai-evaluate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ responses: sampleResponses })
      })
      const data = await response.json()
      setResults(data)
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleGenerateQuestions = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/ai/generate-questions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jobTitle,
          experience,
          skills: skills.split(',').map(s => s.trim()),
          difficulty,
          questionType,
          count: 3
        })
      })
      const data = await response.json()
      setResults(data)
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAnalyzeResponse = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/ai/analyze-response', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ question, answer })
      })
      const data = await response.json()
      setResults(data)
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleGenerateFollowUp = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/ai/follow-up', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ originalQuestion, candidateAnswer })
      })
      const data = await response.json()
      setResults(data)
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-gray-900 flex items-center justify-center gap-3">
            <Brain className="h-10 w-10 text-blue-600" />
            OpenAI Integration Demo
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Experience the power of AI-driven interview evaluation, question generation, and real-time feedback using OpenAI's GPT models.
          </p>
        </div>

        {/* Configuration Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5" />
              OpenAI Configuration
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <Badge variant="secondary">
                Status: Demo Mode (Add your OpenAI API key to .env for full functionality)
              </Badge>
              <div className="text-sm text-gray-600">
                Current features work with mock responses for demonstration
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Demo Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="evaluate" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Evaluate Interview
            </TabsTrigger>
            <TabsTrigger value="generate" className="flex items-center gap-2">
              <FileQuestion className="h-4 w-4" />
              Generate Questions
            </TabsTrigger>
            <TabsTrigger value="analyze" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Analyze Response
            </TabsTrigger>
            <TabsTrigger value="followup" className="flex items-center gap-2">
              <Sparkles className="h-4 w-4" />
              Follow-up Questions
            </TabsTrigger>
          </TabsList>

          {/* Interview Evaluation Tab */}
          <TabsContent value="evaluate" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>🎯 AI Interview Evaluation</CardTitle>
                <CardDescription>
                  Analyze complete interview responses and get detailed feedback with scores
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label>Sample Interview Responses</Label>
                  <div className="space-y-2 mt-2">
                    {sampleResponses.map((response, index) => (
                      <div key={index} className="p-3 bg-gray-50 rounded-lg">
                        <p className="font-medium text-sm">{response.question}</p>
                        <p className="text-sm text-gray-600 mt-1">{response.answer}</p>
                      </div>
                    ))}
                  </div>
                </div>

                <Button 
                  onClick={handleEvaluateInterview} 
                  disabled={loading}
                  className="w-full"
                >
                  {loading ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <BarChart3 className="h-4 w-4 mr-2" />
                  )}
                  Evaluate Interview with AI
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Question Generation Tab */}
          <TabsContent value="generate" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>🤖 AI Question Generation</CardTitle>
                <CardDescription>
                  Generate personalized interview questions based on job requirements
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="jobTitle">Job Title</Label>
                    <Input
                      id="jobTitle"
                      value={jobTitle}
                      onChange={(e) => setJobTitle(e.target.value)}
                      placeholder="e.g., Frontend Developer"
                    />
                  </div>
                  <div>
                    <Label htmlFor="experience">Experience Level</Label>
                    <Input
                      id="experience"
                      value={experience}
                      onChange={(e) => setExperience(e.target.value)}
                      placeholder="e.g., 3 years"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="skills">Required Skills (comma-separated)</Label>
                  <Input
                    id="skills"
                    value={skills}
                    onChange={(e) => setSkills(e.target.value)}
                    placeholder="e.g., React, JavaScript, TypeScript"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Difficulty Level</Label>
                    <Select value={difficulty} onValueChange={setDifficulty}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="beginner">Beginner</SelectItem>
                        <SelectItem value="intermediate">Intermediate</SelectItem>
                        <SelectItem value="advanced">Advanced</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label>Question Type</Label>
                    <Select value={questionType} onValueChange={setQuestionType}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="technical">Technical</SelectItem>
                        <SelectItem value="behavioral">Behavioral</SelectItem>
                        <SelectItem value="situational">Situational</SelectItem>
                        <SelectItem value="coding">Coding</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <Button 
                  onClick={handleGenerateQuestions} 
                  disabled={loading}
                  className="w-full"
                >
                  {loading ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <FileQuestion className="h-4 w-4 mr-2" />
                  )}
                  Generate Questions with AI
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Response Analysis Tab */}
          <TabsContent value="analyze" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>💬 AI Response Analysis</CardTitle>
                <CardDescription>
                  Get real-time feedback on individual interview responses
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="question">Interview Question</Label>
                  <Textarea
                    id="question"
                    value={question}
                    onChange={(e) => setQuestion(e.target.value)}
                    placeholder="Enter the interview question..."
                    rows={2}
                  />
                </div>

                <div>
                  <Label htmlFor="answer">Candidate's Answer</Label>
                  <Textarea
                    id="answer"
                    value={answer}
                    onChange={(e) => setAnswer(e.target.value)}
                    placeholder="Enter the candidate's response..."
                    rows={4}
                  />
                </div>

                <Button
                  onClick={handleAnalyzeResponse}
                  disabled={loading}
                  className="w-full"
                >
                  {loading ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <MessageSquare className="h-4 w-4 mr-2" />
                  )}
                  Analyze Response with AI
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Follow-up Questions Tab */}
          <TabsContent value="followup" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>✨ AI Follow-up Generation</CardTitle>
                <CardDescription>
                  Generate intelligent follow-up questions based on candidate responses
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="originalQuestion">Original Question</Label>
                  <Textarea
                    id="originalQuestion"
                    value={originalQuestion}
                    onChange={(e) => setOriginalQuestion(e.target.value)}
                    placeholder="Enter the original interview question..."
                    rows={2}
                  />
                </div>

                <div>
                  <Label htmlFor="candidateAnswer">Candidate's Answer</Label>
                  <Textarea
                    id="candidateAnswer"
                    value={candidateAnswer}
                    onChange={(e) => setCandidateAnswer(e.target.value)}
                    placeholder="Enter the candidate's response..."
                    rows={4}
                  />
                </div>

                <Button
                  onClick={handleGenerateFollowUp}
                  disabled={loading}
                  className="w-full"
                >
                  {loading ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Sparkles className="h-4 w-4 mr-2" />
                  )}
                  Generate Follow-up Question
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Results Display */}
        {results && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                AI Results
                <Badge variant={results.source === 'openai' ? 'default' : 'secondary'}>
                  {results.source === 'openai' ? 'OpenAI' : 'Demo Mode'}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {/* Interview Evaluation Results */}
              {results.evaluation && (
                <div className="space-y-4">
                  <div className="grid grid-cols-4 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{results.evaluation.overallScore}</div>
                      <div className="text-sm text-gray-600">Overall Score</div>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{results.evaluation.technicalScore}</div>
                      <div className="text-sm text-gray-600">Technical</div>
                    </div>
                    <div className="text-center p-4 bg-purple-50 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">{results.evaluation.communicationScore}</div>
                      <div className="text-sm text-gray-600">Communication</div>
                    </div>
                    <div className="text-center p-4 bg-orange-50 rounded-lg">
                      <div className="text-2xl font-bold text-orange-600">{results.evaluation.problemSolvingScore}</div>
                      <div className="text-sm text-gray-600">Problem Solving</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-semibold text-green-700 mb-2">✅ Strengths</h4>
                      <ul className="space-y-1">
                        {results.evaluation.strengths.map((strength: string, index: number) => (
                          <li key={index} className="text-sm text-gray-600">• {strength}</li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold text-orange-700 mb-2">🎯 Areas for Improvement</h4>
                      <ul className="space-y-1">
                        {results.evaluation.improvements.map((improvement: string, index: number) => (
                          <li key={index} className="text-sm text-gray-600">• {improvement}</li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-2">📝 Detailed Feedback</h4>
                    <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
                      {results.evaluation.detailedFeedback}
                    </p>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-2">🚀 Next Steps</h4>
                    <ul className="space-y-1">
                      {results.evaluation.nextSteps.map((step: string, index: number) => (
                        <li key={index} className="text-sm text-gray-600">• {step}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}

              {/* Generated Questions Results */}
              {results.questions && (
                <div className="space-y-4">
                  <h4 className="font-semibold">🤖 Generated Questions</h4>
                  {results.questions.map((q: any, index: number) => (
                    <div key={index} className="p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="outline">{q.type}</Badge>
                        <Badge variant="secondary">{q.difficulty}</Badge>
                        <span className="text-xs text-gray-500">{q.timeLimit}s</span>
                      </div>
                      <p className="font-medium mb-2">{q.question}</p>
                      {q.expectedPoints && (
                        <div className="text-sm text-gray-600">
                          <strong>Expected points:</strong> {q.expectedPoints.join(', ')}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {/* Response Analysis Results */}
              {results.analysis && (
                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{results.analysis.score}</div>
                      <div className="text-sm text-gray-600">Score</div>
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold mb-2">💬 Feedback</h4>
                      <p className="text-sm text-gray-600">{results.analysis.feedback}</p>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">💡 Suggestions</h4>
                    <ul className="space-y-1">
                      {results.analysis.suggestions.map((suggestion: string, index: number) => (
                        <li key={index} className="text-sm text-gray-600">• {suggestion}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}

              {/* Follow-up Question Results */}
              {results.followUpQuestion && (
                <div className="space-y-4">
                  <h4 className="font-semibold">✨ Generated Follow-up Question</h4>
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <p className="font-medium text-blue-800">{results.followUpQuestion}</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Setup Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>🔧 Setup Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">1. Get OpenAI API Key</h4>
                <p className="text-sm text-gray-600">
                  Visit <a href="https://platform.openai.com/api-keys" target="_blank" className="text-blue-600 hover:underline">OpenAI Platform</a> to create an API key
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">2. Update Environment Variables</h4>
                <div className="bg-gray-900 text-green-400 p-3 rounded-lg font-mono text-sm">
                  OPENAI_API_KEY=sk-your-actual-openai-api-key-here
                </div>
              </div>
              <div>
                <h4 className="font-semibold mb-2">3. Restart Development Server</h4>
                <p className="text-sm text-gray-600">
                  After updating the .env file, restart your development server to load the new API key
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
