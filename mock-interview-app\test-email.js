const http = require('http');

const data = JSON.stringify({
  email: '<EMAIL>',
  studentName: '<PERSON> <PERSON>',
  interviewTitle: 'Technical Interview - Software Developer Position'
});

const options = {
  hostname: 'localhost',
  port: 3000,
  path: '/api/email/send-invitation',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': data.length
  }
};

const req = http.request(options, (res) => {
  console.log(`Status: ${res.statusCode}`);
  console.log(`Headers: ${JSON.stringify(res.headers)}`);
  
  let responseData = '';
  res.on('data', (chunk) => {
    responseData += chunk;
  });
  
  res.on('end', () => {
    console.log('Response:', responseData);
  });
});

req.on('error', (e) => {
  console.error(`Problem with request: ${e.message}`);
});

req.write(data);
req.end();
